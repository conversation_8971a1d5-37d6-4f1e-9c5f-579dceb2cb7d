#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include <QSettings> // 确保已包含QSettings头文件

#include "ui_mainwindow.h"
#include "tools/header/DockManager.h"
#include "tools/header/DockAreaWidget.h"
#include "tools/header/DockWidget.h"
#include "tools/header/DockSplitter.h"
#include "tools/header/DockAreaWidget.h"
#include "tools/header/DockAreaTitleBar.h"
#include "tools/header/DockAreaTabBar.h"
#include "tools/header/FloatingDockContainer.h"
#include "tools/header/DockComponentsFactory.h"

#include "tools/Qwt_image/plot.h"
#include "tools/Qwt_image/ThreadPool.h"
#include "tools/Qwt_image/DirectoryScanner.h"

#include "UI/comm/classwidgetcomm.h"
#include "UI/HistoryDialog.h"
#include "UI/SensorStatusDialog.h"
#include "UI/LogDialog.h"
#include "UI/ControlSetParameterDialog.h"
#include "UI/System/DailyMaintenanceDialog.h"
#include "UI/System/SystemMaintenanceDialog.h"
#include "UI/System/UploadMonitorDialog.h"
#include "UI/System/SensorStatusRecodeDialog.h"
#include "UI/Set/FMGParameterDialog.h"
#include "UI/SystemConfiguration/SystemConfigurationDialog.h"
#include "UI/SystemRegister.h"
#include "UI/System/BreakDownDialog.h"
#include "UI/set/FMGWidget.h"
#include "tools/Configdata/printscreen.h"
#include "tools/BrightCalcClass.h"
#include "UI/set/FTPDataDialog.h"
#include "UI/set/FTPDialog.h"
#include "tools/ObservationController.h"
#include "UI/SystemConfiguration/HandCalibrationDialog.h"

QT_BEGIN_NAMESPACE
namespace Ui
{
    class MainWindow;
}
QT_END_NAMESPACE

using namespace ads;
using namespace std;


class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();
    
    void deleteOldFiles(QString filepath, QString time);//删除文件功能

protected:
    bool eventFilter(QObject* watched, QEvent* event) override;
    void closeEvent(QCloseEvent* event) override;
signals:
    void CMainWindowToSystemConfigurationDialog(int);
    void Signal_CLightDataLv1();//lv1界面数据保存
    void set_logRecord_color_signal(QString);//日志表格错误信息内容添加
private slots:
    void get_lv1_data();
    void set_system_time_status();
    void History_file_Path(QString hispath); // LV2数据路径
    void onFileFound(const QString& filePath);// 文件查找
    void History_file_Path_lv1(QString hispath); // LV1数据路径
    void onFileFound1(const QString& filePath);
    void onOPtion_view(QAction* option); // 视图选择
    void onOPtion_dis(QAction* option);  // 距离档位
    void onOPtion_time(QAction* option); // 时间档位

    /**** 界面显示或隐藏 *****/
     //传感器状态界面
    void sensor_status_recode();
    // 显示或隐藏历史记录槽
    void slot_history_data();
    // 打开日志文件槽
    void slot_open_log_file();
    // 显示或隐藏传感器状态槽
    void slot_sensor_state();
    // 显示或隐藏日志记录槽
    void slot_log_record();
    // 基础数据显示
    void slot_basic_data_show();
    // 屏幕截图
    void slot_screenshot();
    // 图像左移
    void slot_image_left();
    // 图像右移
    void slot_image_right();
    // 观测开关
    void slot_observe_switch();// 残缺
    // 遥控本控开关
    void slot_telecontrol_switch();// 残缺
    // 自动刷新开关
    void slot_auto_renovate_switch();//残缺
    // 风机开关
    void slot_fan_switch();//残缺
    // 噪声开关
    void slot_unpitched_sound_switch();//残缺
    // 重启按钮
    void slot_facility_reboot();//残缺
    // 廓线图
    void slot_outline_image();
    // 实时
    void slot_real_time_switch(); // 残缺
    //网络连接
    void set_FSJ_netWork();

    //保存布局
    void slot_saveLayout();

    //设置皮肤颜色
    void set_menubar_styleSheet(int);

public slots:
    void slot_delect_lv1_data();
    void slot_delect_lv2_data();

    void slot_auto_delect_data();

private:
    Ui::MainWindow *ui;
    QDateTime currentDateTime;
    QTimer *system_time;
    QTimer* get_lv1;
    QTimer* auto_delect_data;
    QLabel *infomation_label;
    QLabel *timelabel;
    QPushButton *statusPushButton;
    classWidgetComm *commNetWork;

    ObservationController* controller;

    CxcDataExchange* lv1_data;
    QString currentDateTimeString;

    QMenu *menu_view;
    QMenu *menu_time;
    QMenu *menu_dis;

    // 绘图参数
    Plot* d_plot;
    Plot* d_plot2;
    Plot* d_plot3;
    Plot* d_plot4;

    QCPItemTracer *tracer_one;
    QCPItemText *tracerLabe_one;
    QCPItemTracer *tracer_two;
    QCPItemText *tracerLabe_two;
    QCPItemTracer * tracer_three ;
    QCPItemText * tracerLabe_three;

    QAction* action_view;
    HistoryDialog *historydialog;
    LogDialog *logdialog;
    SensorStatusDialog *sensorstatusdialog;
    ControlSetParameterDialog *setParameter;
    DailyMaintenanceDialog *dailymaintenance;
    SystemMaintenanceDialog *systemmaintenance;
    UploadMonitorDialog *uploadMonitor;
    BreakDownDialog *breakdown;
    FMGParameterDialog *fmgparameter;
    FMGWidget *fmgwidget;
    SystemConfigurationDialog *systemconfiguration;
    SystemRegister *systemregister;
    SensorStatusRecodeDialog* sensorStatusRecord;

    FTPDialog* ftp;

    HandCalibrationDialog* handca;

    QString title_skin;
    QString button_title_close_skin;
    QString title_skin1;

    CDockManager *DockManager;
    CDockWidget *CentralDockWidget;
    CDockWidget *leftPlace;
    CDockWidget *rightUpPlaceholder;
    CDockWidget *rightDownPlaceholder;

    QSettings settings;

    // 历史数据的数据树
    QString save_path;
    ThreadPool* m_pool;
    DirectoryScanner* m_scanner;
    ThreadPool* m_pool1;
    DirectoryScanner* m_scanner1;
    QTreeWidgetItem* m_rootItem;
    QTreeWidgetItem* m_rootItem1;
    QString cur_filePath = "";
    int cur_mode = 2; // 1 历史数据 2实时数据
    int count = 0;

    QString save_path1;
    QString cur_filePath1 = "";

    QVector<QDateTime> m_times[4];
    QVector<double> m_heights[4];
    QVector<QVector<double>> m_temps[4];

    QVector<QDateTime> timerVector[4]; // 时间容器
    QVector<QVector<double>> envParamaVector; // 通道高度容器
    QVector<QVector<double>> channelsVector; // 温度容器
    QString index_view;

    QVector<double> table_header[4];

    QVector<QVector<double>> humidityProfilees[4]; //湿度
    QVector<QVector<double>> temperatureProfilees[4]; //温度
    QVector<QVector<double>> vaporProfilees[4]; //水汽

    int open_lv1_lv2;

    // 恢复布局管理
    bool profile_image = false;

    // 界面显示的标志位
    // 记录界面视图状态
    bool hisdialog_view = false;
    bool sensor_view = false;
    bool log_view = false;
    bool observe_switch_flag = false;
    bool telecontrol_flag = false;
    bool auto_renovate_flag = false;
    bool fan_switch_flag = false;
    bool unpitched_sound_flag = false;
    bool outline_image_flag = false;
    int outline_flag = 3;
    bool fmg_flag = false;
    uchar fan_speed = 0;




   

    void qwt_init();         // 绘图初始化
    void creat_configfile(); // 创建ini配置文件
    void loadLayout();       // 加载当前保存的布局
    void loadDefault();      // 恢复默认布局
    void set_DockWidget_title_setStyleSheet(CDockWidget *dock, QString title_skin, QString button_title_close_skin);
    void set_action_comboBox();// 时间，距离档位 视图选择 下拉框 
    void set_tracer(QCustomPlot* plot, QCPItemTracer* tracer, QCPItemText* tracerLabel);
    void change_Spectrogram(int num, QString data); // 鼠标右键改变视图选择  lv2数据
    void change_Spectrogram_lv1(int num, QString data); // 鼠标右键改变视图选择  lv1数据
    void set_title(Plot* d_p, QString text);        // 设置图表的标题
    void outline_image(bool flag);


    void set_title_Color(Plot* d_p, QString text, QColor, QColor);//设置图表的标题
    void set_view_plot_line(QCustomPlot* plot, QColor color_background, QColor color_axis);//设置轮阔图表格颜色
    void save_DockManager();//保存界面拖拽视角

    void LoadStyleFile(QString strStyle);//qss文件读取并加载到界面
    QString GetAllStyle(QStringList strListStyleFiles, QString strDirPath);//多个qss文件读取

     //亮温质量控制
    int lv1_n1_fun(struct Lv1_Bright_temperature_data* data);
    QVector<QVector<double>> Minimum_variability_check_data;
    int Minimum_variability_num = 0;
    void lv1_Historical_data(struct Lv1_Bright_temperature_data* data);
    int lv1_n2_fun(struct Lv1_Bright_temperature_data* data);
    QString lv1_n3_start_time;
    bool lv1_n3_1 = true;
    int lv1_n3_fun(struct Lv1_Bright_temperature_data* data, int sec);

    int lv1_n4_fun(struct Lv1_Bright_temperature_data* data);

    int lv1_n5_fun(struct Lv1_Bright_temperature_data* data, double min, double max);

    int set_QCFlag(struct Lv1_Bright_temperature_data* data);


};

#endif // MAINWINDOW_H
