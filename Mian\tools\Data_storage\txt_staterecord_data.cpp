﻿#include "tools/Data_storage/txt_staterecord_data.h"
#include "tools/Global.h"
txt_staterecord_data::txt_staterecord_data(QObject *parent) : QObject(parent)
{
    StandardItemModel = new QStandardItemModel(this);
    StandardItemModel->setHorizontalHeaderItem(0, new QStandardItem(QObject::tr("时间")));
    StandardItemModel->setHorizontalHeaderItem(1, new QStandardItem(QObject::tr("描述")));
}

txt_staterecord_data::~txt_staterecord_data()
{
    QObject::disconnect(coon);
}
/*
 *使用QStandardItemModel模型
 * 参数:
 *    view 用来显示数据的QTableView
 * 返回值: 无
 */
void txt_staterecord_data::use_QStandardItemModel(QTableView *view, QString filepath)
{
    if (StandardItemModel_status)
        return;

    currentFolder = filepath;
    // 创建目录结构
    QDir dir(filepath);
    if (!dir.exists())
        dir.mkpath(filepath);

    view->setModel(StandardItemModel);
    // view->setColumnHidden(0,true);
    tableview = view;
    set_column_edit(0, false);
    set_column_edit(1, false);

    view->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
    coon = connect(tableview->horizontalHeader(), &QHeaderView::geometriesChanged, [&]()
                   {
                       if (tableview == nullptr)
                           return;
                       int totalwidth = tableview->viewport()->width();
                       int ratiosum = std::accumulate(ratios.begin(), ratios.end(), 0);

                       tableview->setColumnWidth(0, (totalwidth * ratios[0]) / ratiosum);
                       tableview->setColumnWidth(1, (totalwidth * ratios[1]) / ratiosum);
                   });
    StandardItemModel_status = true;
}
/*
 *插入日志数据
 * 参数:
 *    data 实时日志
 * 返回值: true 插入成功
 *        false 插入失败
 */
bool txt_staterecord_data::insertdata(StateRecord_SQ_GROUP data)
{
    if (data.time == "beijing")
    {
        data.time = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    }
    else if (data.time == "UTC")
    {
        data.time = QDateTime::currentDateTimeUtc().toString("yyyy-MM-dd HH:mm:ss");
    }
    else
    {
        if (!QDateTime::fromString(data.time, "yyyy-MM-dd HH:mm:ss").isValid())
        {

            return false;
        }
    }
    if (StandardItemModel_status)
    {
        temp_data.append(data);
        const int newrow = StandardItemModel->rowCount();
        StandardItemModel->insertRow(newrow);
        StandardItemModel->blockSignals(true);
        StandardItemModel->setItem(newrow, 0, new QStandardItem(data.time));
        StandardItemModel->setItem(newrow, 1, new QStandardItem(data.content));

        for (int i = 0; i < m_nonEditableColumns.count(); i++)
        {
            StandardItemModel->item(newrow, i)->setFlags(Qt::ItemIsEditable | Qt::ItemIsSelectable | Qt::ItemIsEnabled);
        }

        StandardItemModel->item(newrow, 0)->setForeground(QBrush(QColor(0, 0, 0)));
        StandardItemModel->item(newrow, 1)->setForeground(QBrush(QColor(0, 0, 0)));
        StandardItemModel->blockSignals(false);
        tableview->scrollToBottom();
        cur_row = newrow;
        return true;
    }
    return false;
}
/*
 *保存StandardItemModel日志文件到txt
 * 参数:
 *    time_type    文件名中的时间可选UTC beijing yyyyMMddHHmmss
 *    title       标题
 * 返回值: true 设置成功
 *        false 失败
 */
bool txt_staterecord_data::save_StandardItemModelTotxt(QString time_type, const QVector<QString> &title)
{
    QString timestamp = "";
    if (time_type == "UTC")
    {
        timestamp = QDateTime::currentDateTimeUtc().toString("yyyyMMdd");
    }
    else if (time_type == "beijing")
    {
        timestamp = QDateTime::currentDateTime().toString("yyyyMMdd");
    }
    else
    {
        if (!QDateTime::fromString(time_type, "yyyyMMdd").isValid())
        {
            return false;
        }
        //        QDateTime temp = QDateTime::fromString(time_type,"yyyy-MM-dd");
        //        timestamp = temp.toString("yyyyMMdd") ;
        timestamp = time_type;
    }

    QString fileName = QString("/日志文件/日志_%1.txt").arg(timestamp);
    // QString filepath = QDir(currentFolder).filePath(fileName);
    QString filepath = systemconfigfdata.systemdata.save_log_path + fileName;

    // 创建目录结构
    QFileInfo fi(filepath);
    QDir().mkpath(fi.absolutePath());
    QFile file(filepath);
    // if(!file.open(QIODevice::WriteOnly|QIODevice::Text))
    if (!file.open(QIODevice::Append | QIODevice::Text))
    {
        return false;
    }
    QTextStream out(&file);
    out.setCodec("UTF-8");

    for (int i = 0; i < title.count(); i++)
    {
        out << title[i] << "\n";
    }

    for (int row = 0; row < temp_data.count(); ++row)
    {
        QStringList rowdata;
        //        for(int col = 0;col <2;++col)
        //        {
        // QStandardItem *item = StandardItemModel->item(row,col);
        // QString text = item?item->text():"";
        QString text = temp_data[row].time;
        if (text.contains("\t") || text.contains("\n"))
        {
            text = "\"" + text.replace("\"", "\"\"") + "\"";
        }
        rowdata << text;

        text = temp_data[row].content;
        if (text.contains("\t") || text.contains("\n"))
        {
            text = "\"" + text.replace("\"", "\"\"") + "\"";
        }
        rowdata << text;
        //        }
        out << rowdata.join("\t") << "\n";
    }
    temp_data.clear();
    file.close();
    return true;
}
/*
 *删除指定时间节点前的日志文件
 * 参数:
 *    time    指定时间节点
 * 返回值: 无
 */
void txt_staterecord_data::deleteOldFiles(QString currentFolder,QString time)
{
    if (!QDateTime::fromString(time, "yyyy-MM-dd HH:mm:ss").isValid())
    {
        return;
    }

    QDateTime targettime = QDateTime::fromString(time, "yyyy-MM-dd HH:mm:ss");
    QDir dir(currentFolder);
    if (!dir.exists())
    {
        //QMessageBox::warning(nullptr, "警告", QString("文件路径不存在:\n%1").arg(currentFolder));
        return;
    }
    int deletecount = 0;
    QStringList filestr;
    filestr << "日志_*.txt";
    QFileInfoList files = dir.entryInfoList(filestr, QDir::Files);

    foreach (const QFileInfo &fileinfo, files)
    {
        QString filename = fileinfo.fileName();
        QString timestr = filename.mid(3, 8);
        QDateTime filetime = QDateTime::fromString(timestr, "yyyyMMddHHmmss");
        if (!filetime.isValid())
        {
            continue;
        }
        if (filetime < targettime)
        {
            if (QFile::remove(fileinfo.absoluteFilePath()))
            {
                deletecount++;
            }
            else
            {
                //QMessageBox::warning(nullptr, "警告", QString("无法删除:\n%1").arg(fileinfo.absoluteFilePath()));
            }
        }
    }

    //QMessageBox::information(nullptr, "完成", QString("已删除 %1 个文件").arg(deletecount));
}

void txt_staterecord_data::clear_StandardItemModel_data()
{
    StandardItemModel->clear();
    StandardItemModel->setHorizontalHeaderItem(0, new QStandardItem(QObject::tr("时间")));
    StandardItemModel->setHorizontalHeaderItem(1, new QStandardItem(QObject::tr("描述")));
}
/*
 *设置最后一行的颜色
 * 参数:
 *    c 颜色
 * 返回值: true 设置完成
 *        false 行列越界
 */
bool txt_staterecord_data::set_column_color(QVector<QColor> c)
{
    if (c.count() != StandardItemModel->columnCount())
        return false;
    StandardItemModel->blockSignals(true);

    for (int i = 0; i < StandardItemModel->columnCount(); i++)
    {
        StandardItemModel->item(StandardItemModel->rowCount() - 1, i)->setForeground(QBrush(c[i]));
    }

    StandardItemModel->blockSignals(false);
    return true;
}
/*
 *设置指定单元项的颜色
 * 参数:
 *    row    行
 *    Column 列
 *    c      颜色
 * 返回值: true 设置成功
 *        false 失败
 */
bool txt_staterecord_data::set_column_color(int row, int column, const QColor &c)
{
    if (StandardItemModel->rowCount() < row && StandardItemModel->columnCount() < column)
    {
        StandardItemModel->blockSignals(true);
        StandardItemModel->item(row, column)->setForeground(QBrush(c));
        StandardItemModel->blockSignals(false);
        return true;
    }
    return false;
}
/*
 *更新文件存储路径
 * 参数:
 *    filepath    新的文件路径
 * 返回值: true 更新成功
 *        false 失败
 */
bool txt_staterecord_data::update_filepath(QString filepath)
{

    // 创建目录结构
    QDir dir(filepath);
    if (!dir.exists())
    {
        if (dir.mkpath(filepath))
        {
            currentFolder = filepath;
            return true;
        }
        else
            return false;
    }
    currentFolder = filepath;
    return true;
}

void txt_staterecord_data::set_column_edit(int column, bool en)
{
    if (en)
        m_nonEditableColumns.insert(column);
    else
        m_nonEditableColumns.remove(column);
}
