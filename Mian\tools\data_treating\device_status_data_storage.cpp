﻿#include "device_status_data_storage.h"

Device_status_data_storage::Device_status_data_storage(QString file_path, FILENAME file_name, QObject *parent)
{
    Q_UNUSED(parent);
    filepath = file_path;
    filename = file_name;

    file = nullptr;
}
/*
函数名：Generate_new_Device_status_data
作用：设备状态存入日文件。
参数要求：
Record、DateTime由函数内部生成;
其他参数需用户输入

返回值：true :保存成功
       false :保存失败
*/

bool Device_status_data_storage::Generate_new_Device_status_data(Device_status_data device_status_data)
{
    // 创建目录结构
    struct FILENAME temp = filename;
    temp.pf1ag = "Z";
    temp.productidentifier = "UPAR";
    if (use_beijing_utc)
        temp.yyyyMMddhhmmss = QDateTime::currentDateTime().toString("yyyyMMddHHmmss");
    else
        temp.yyyyMMddhhmmss = QDateTime::currentDateTimeUtc().toString("yyyyMMddHHmmss");
    temp.deviceidentification = "YMWR";
    temp.frequency = "D";
    if (txt_or_xml)
        temp.type = "txt";
    else
        temp.type = "xml";
    temp.datatype = "STA";
    temp.ftype = "R";

    QString filenamestr = Generate_file_name(temp);
    QString filepath_dir;
    if (use_beijing_utc)
        filepath_dir = filepath + QDateTime::currentDateTime().toString("yyyy-MM-dd") + "/";
    else
        filepath_dir = filepath + QDateTime::currentDateTimeUtc().toString("yyyy-MM-dd") + "/";
    QDir dir(filepath_dir);
    if (!dir.exists())
        dir.mkpath(filepath_dir);

    bool isexitsfile = false;
    foreach (const QFileInfo &fileinfo, dir.entryInfoList(QDir::Files))
    {
        if (comparefilenames(fileinfo.fileName(), filenamestr))
        {

            if (cur_day_filename != fileinfo.fileName())
            {

                if (file != nullptr)
                    delete file;
                file = new QFile(fileinfo.path() + "/" + fileinfo.fileName());
                if (!file->open(QIODevice::ReadWrite | QIODevice::Text))
                {
                    return false;
                }
                cur_day_filename = fileinfo.fileName();
                if (txt_or_xml)
                {
                    QTextStream in(file);
                    int linecount = 0;

                    while (!in.atEnd())
                    {
                        in.readLine();
                        linecount++;
                    }
                    Record_number = linecount;

                    file->close();
                }
                else
                {
                    // 通过有多少行判断
                    QDomDocument doc;
                    // file->seek(0);

                    int errorline, errorcolumn;
                    QString errormes;
                    if (!doc.setContent(file, &errormes, &errorline, &errorcolumn))
                    {
                        return false;
                    }

                    file->close();

                    QDomElement root = doc.documentElement();

                    if (root.isNull() || root.tagName() != "StatusInformation")
                    {
                        return false;
                    }
                    Record_number = root.elementsByTagName("Status").size() + 1;
                }
            }

            if (file != nullptr)
                delete file;
            file = new QFile(fileinfo.path() + "/" + fileinfo.fileName());
            if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
            {
                return false;
            }

            isexitsfile = true;
        }
    }
    if (!isexitsfile)
    {
        // 创建文件
        if (file != nullptr)
            delete file;
        file = new QFile(filepath_dir + filenamestr);
        if (txt_or_xml)
        {
            if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
            {
                return false;
            }
        }
        else
        {
            if (!file->open(QIODevice::WriteOnly | QIODevice::Truncate))
            {
                return false;
            }
        }
        cur_day_filename = filenamestr;
        Record_number = 1; // 行号置1

        Insert_new_Device_status_Basic_parameters();
    }

    Insert_new_Device_status_data(device_status_data);
    return true;
}
/*
函数名：Generate_new_Device_status_data
作用：设备状态存入分钟文件。
参数要求：
Record、DateTime由函数内部生成;
其他参数需用户输入

返回值：true :保存成功
       false :保存失败
*/
bool Device_status_data_storage::Generate_new_Device_status_Minutes_file(Device_status_data device_status_data)
{

    // 创建目录结构
    struct FILENAME temp = filename;
    temp.pf1ag = "Z";
    temp.productidentifier = "UPAR";
    if (use_beijing_utc)
        temp.yyyyMMddhhmmss = QDateTime::currentDateTime().toString("yyyyMMddHHmmss");
    else
        temp.yyyyMMddhhmmss = QDateTime::currentDateTimeUtc().toString("yyyyMMddHHmmss");
    temp.deviceidentification = "YMWR";
    temp.frequency = "M";
    if (txt_or_xml)
        temp.type = "txt";
    else
        temp.type = "xml";
    temp.datatype = "STA";
    temp.ftype = "R";

    QString filenamestr = Generate_file_name(temp);
    QString filepath_dir;
    if (use_beijing_utc)
        filepath_dir = filepath + QDateTime::currentDateTime().toString("yyyy-MM-dd") + "/";
    else
        filepath_dir = filepath + QDateTime::currentDateTimeUtc().toString("yyyy-MM-dd") + "/";
    QDir dir(filepath_dir);
    if (!dir.exists())
        dir.mkpath(filepath_dir);

    if (txt_or_xml)
    {
        QFile *file = new QFile(filepath_dir + Generate_file_name(temp));
        if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
        {
            return false;
        }
        Record_number = 1;

        QByteArray linedata;
        linedata.clear();
        QString table_header = "Record,DateTime,General,EServo,AServo,RCV0,RCV1,TRec1,TRec2,SRec1,SRec2,LO,BIB,TAmb1,TAmb2,TAmb3,TAmb4,SurTem,SurHum,SurPre,Rain,Tir,TimeSync,ECM,ExPower,Communication";
        linedata = table_header.toLatin1();
        linedata.append("\r\n");

        if (file->write(linedata) == -1)
        {
            file->close();
            return false;
        }

        linedata.clear();
        QString line = QString::number(Record_number++) + ','
                       //+bright_temperature.DateTime +','
                       + QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss") + ',' + deal_int_data(device_status_data.General) + ',' + deal_int_data(device_status_data.EServo) + ',' + deal_int_data(device_status_data.AServo) + ',' + deal_int_data(device_status_data.RCV0) + ',' + deal_int_data(device_status_data.RCV1) + ',' + Control_decimal_bit(device_status_data.TRec1, 2, true) + ',' + Control_decimal_bit(device_status_data.TRec2, 2, true) + ',' + deal_int_data(device_status_data.SRec1) + ',' + deal_int_data(device_status_data.SRec2) + ',' + deal_int_data(device_status_data.LO) + ',' + deal_int_data(device_status_data.BIB) + ',' + Control_decimal_bit(device_status_data.TAmb1, 2, true) + ',' + Control_decimal_bit(device_status_data.TAmb2, 2, true) + ',' + Control_decimal_bit(device_status_data.TAmb3, 2, true) + ',' + Control_decimal_bit(device_status_data.TAmb4, 2, true) + ',' + deal_int_data(device_status_data.SurTem) + ',' + deal_int_data(device_status_data.SurHum) + ',' + deal_int_data(device_status_data.SurPre) + ',' + deal_int_data(device_status_data.Rain) + ',' + deal_int_data(device_status_data.Tir) + ',' + deal_int_data(device_status_data.TimeSync) + ',' + deal_int_data(device_status_data.ECM) + ',' + deal_int_data(device_status_data.ExPower) + ',' + deal_int_data(device_status_data.Communication);
        if (!isPureAscii(line))
        {
            file->close();
            return false;
        }
        linedata = line.toLatin1();
        linedata.append("\r\n");

        if (file->write(linedata) == -1)
        {
            file->close();
            return false;
        }
        file->close();
        return true;
    }
    else
    {
        // 创建文件
        QFile *file = new QFile(filepath_dir + filenamestr);

        QDomDocument doc;
        // 添加XML声明
        QDomProcessingInstruction header = doc.createProcessingInstruction("xml", "version=\"1.0\" encoding=\"UTF-8\"");
        doc.appendChild(header);
        // 创建根节点
        QDomElement root = doc.createElement("StatusInformation");
        root.setAttribute("device", device);
        root.setAttribute("type", type);
        doc.appendChild(root);

        // 创建Status节点

        QDomElement statuselement = doc.createElement("Status");

        // 添加数据
        QDomElement recordelement = doc.createElement("Record");
        recordelement.appendChild(doc.createTextNode(QString::number(Record_number++)));
        statuselement.appendChild(recordelement);
        QDomElement DateTimeelement = doc.createElement("DateTime");
        DateTimeelement.appendChild(doc.createTextNode(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss")));
        statuselement.appendChild(DateTimeelement);
        QDomElement Generalelement = doc.createElement("General");
        Generalelement.appendChild(doc.createTextNode(QString::number(device_status_data.General)));
        statuselement.appendChild(Generalelement);
        QDomElement EServoelement = doc.createElement("EServo");
        EServoelement.appendChild(doc.createTextNode(QString::number(device_status_data.EServo)));
        statuselement.appendChild(EServoelement);
        QDomElement AServoelement = doc.createElement("AServo");
        AServoelement.appendChild(doc.createTextNode(QString::number(device_status_data.AServo)));
        statuselement.appendChild(AServoelement);

        QDomElement RCV0element = doc.createElement("RCV0");
        RCV0element.appendChild(doc.createTextNode(QString::number(device_status_data.RCV0)));
        statuselement.appendChild(RCV0element);

        QDomElement RCV1element = doc.createElement("RCV1");
        RCV1element.appendChild(doc.createTextNode(QString::number(device_status_data.RCV1)));
        statuselement.appendChild(RCV1element);

        QDomElement TRec1element = doc.createElement("TRec1");
        TRec1element.appendChild(doc.createTextNode(Control_decimal_bit(device_status_data.TRec1, 2, true)));
        statuselement.appendChild(TRec1element);

        QDomElement TRec2element = doc.createElement("TRec2");
        TRec2element.appendChild(doc.createTextNode(Control_decimal_bit(device_status_data.TRec2, 2, true)));
        statuselement.appendChild(TRec2element);

        QDomElement SRec1element = doc.createElement("SRec1");
        SRec1element.appendChild(doc.createTextNode(QString::number(device_status_data.SRec1)));
        statuselement.appendChild(SRec1element);

        QDomElement SRec2element = doc.createElement("SRec2");
        SRec2element.appendChild(doc.createTextNode(QString::number(device_status_data.SRec2)));
        statuselement.appendChild(SRec2element);

        QDomElement LOelement = doc.createElement("LO");
        LOelement.appendChild(doc.createTextNode(QString::number(device_status_data.LO)));
        statuselement.appendChild(LOelement);

        QDomElement BIBelement = doc.createElement("BIB");
        BIBelement.appendChild(doc.createTextNode(QString::number(device_status_data.BIB)));
        statuselement.appendChild(BIBelement);

        QDomElement TAmb1element = doc.createElement("TAmb1");
        TAmb1element.appendChild(doc.createTextNode(Control_decimal_bit(device_status_data.TAmb1, 2, true)));
        statuselement.appendChild(TAmb1element);

        QDomElement TAmb2element = doc.createElement("TAmb2");
        TAmb2element.appendChild(doc.createTextNode(Control_decimal_bit(device_status_data.TAmb2, 2, true)));
        statuselement.appendChild(TAmb2element);

        QDomElement TAmb3element = doc.createElement("TAmb3");
        TAmb3element.appendChild(doc.createTextNode(Control_decimal_bit(device_status_data.TAmb3, 2, true)));
        statuselement.appendChild(TAmb3element);

        QDomElement TAmb4element = doc.createElement("TAmb4");
        TAmb4element.appendChild(doc.createTextNode(Control_decimal_bit(device_status_data.TAmb4, 2, true)));
        statuselement.appendChild(TAmb4element);

        QDomElement SurTemelement = doc.createElement("SurTem");
        SurTemelement.appendChild(doc.createTextNode(QString::number(device_status_data.SurTem)));
        statuselement.appendChild(SurTemelement);

        QDomElement SurHumelement = doc.createElement("SurHum");
        SurHumelement.appendChild(doc.createTextNode(QString::number(device_status_data.SurHum)));
        statuselement.appendChild(SurHumelement);

        QDomElement SurPreelement = doc.createElement("SurPre");
        SurPreelement.appendChild(doc.createTextNode(QString::number(device_status_data.SurPre)));
        statuselement.appendChild(SurPreelement);

        QDomElement Rainelement = doc.createElement("Rain");
        Rainelement.appendChild(doc.createTextNode(QString::number(device_status_data.Rain)));
        statuselement.appendChild(Rainelement);

        QDomElement Tirelement = doc.createElement("Tir");
        Tirelement.appendChild(doc.createTextNode(QString::number(device_status_data.Tir)));
        statuselement.appendChild(Tirelement);

        QDomElement TimeSyncelement = doc.createElement("TimeSync");
        TimeSyncelement.appendChild(doc.createTextNode(QString::number(device_status_data.TimeSync)));
        statuselement.appendChild(TimeSyncelement);

        QDomElement ECMelement = doc.createElement("ECM");
        ECMelement.appendChild(doc.createTextNode(QString::number(device_status_data.ECM)));
        statuselement.appendChild(ECMelement);

        QDomElement ExPowerelement = doc.createElement("ExPower");
        ExPowerelement.appendChild(doc.createTextNode(QString::number(device_status_data.ExPower)));
        statuselement.appendChild(ExPowerelement);

        QDomElement Communicationelement = doc.createElement("Communication");
        Communicationelement.appendChild(doc.createTextNode(QString::number(device_status_data.Communication)));
        statuselement.appendChild(Communicationelement);

        root.appendChild(statuselement);

        if (!file->open(QIODevice::ReadWrite | QIODevice::Truncate | QIODevice::Text))
        {
            return false; // 如果
        }

        QTextStream stream(file);
        stream.setCodec("UTF-8");
        doc.save(stream, 4);
        file->close();

        return true;
    }
}
/*
 * 函数名：set_device_and_type
 * 作用：更改xml文件中device及type
 */
void Device_status_data_storage::set_device_and_type(QString device, QString type)
{
    this->device = device;
    this->type = type;
}

/*
 * set_file_path
 * 作用：更改文件路径
 */
void Device_status_data_storage::set_file_path(QString path)
{
    filepath = path;
}
/*
 * set_file_name
 * 作用：更改文件名中的of1ag(按台站区站号进行编码)、originator(气象台站区站号)、equipmenttype(设备型号),其他参数无法修改。
 * 注意：name中参数必须严格按照格式要求、程序中不检查该格式
 */
void Device_status_data_storage::set_file_name(FILENAME name)
{
    filename = name;
}
/*
 * set_Minutes_file_en
 * 作用：启动按分钟保存文件。
 */
void Device_status_data_storage::set_Minutes_file_en(bool en)
{
    Minutes_file_en = en;
}
/*
 * set_Minutes_file_en
 * 作用：启动按天保存文件。
 */
void Device_status_data_storage::set_Day_file_en(bool en)
{
    Day_file_en = en;
}
/*
 * set_beijing_or_utc
 * 作用：设置文件路径及文件名中的时间是使用北京时间还是UTC时间。
 * true beijing false utc
 */
void Device_status_data_storage::set_beijing_or_utc(bool choose)
{
    use_beijing_utc = choose;
}

void Device_status_data_storage::set_save_mode(bool mode)
{
    txt_or_xml = mode;
}

void Device_status_data_storage::save_data(Device_status_data device_status_data)
{
    if (Day_file_en)
    {
        bool re = Generate_new_Device_status_data(device_status_data);
        if (re)
        {
            qDebug() << "Device_status day succeed";
        }
        else
        {
            qDebug() << "Device_status day failure";
        }
    }

    if (Minutes_file_en)
    {
        bool re = Generate_new_Device_status_Minutes_file(device_status_data);
        if (re)
        {
            qDebug() << "Device_status Minutes succeed";
        }
        else
        {
            qDebug() << "Device_status Minutes failure";
        }
    }
}
/*
 * Insert_new_Device_status_Basic_parameters
 * 作用：创建XML声明及根节点
 */
bool Device_status_data_storage::Insert_new_Device_status_Basic_parameters()
{
    if (txt_or_xml)
    {
        if (!file->isOpen())
        {
            if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
            {
                return false; // 如果
            }
        }

        QByteArray linedata;
        linedata.clear();
        QString table_header = "Record,DateTime,General,EServo,AServo,RCV0,RCV1,TRec1,TRec2,SRec1,SRec2,LO,BIB,TAmb1,TAmb2,TAmb3,TAmb4,SurTem,SurHum,SurPre,Rain,Tir,TimeSync,ECM,ExPower,Communication";
        if (!isPureAscii(table_header))
        {
            file->close();
            return false;
        }
        linedata = table_header.toLatin1();
        linedata.append("\r\n");

        if (file->write(linedata) == -1)
        {
            file->close();
            return false;
        }
        file->close();
        return true;
    }
    else
    {
        file->close();
        QDomDocument doc;
        // 添加XML声明
        QDomProcessingInstruction header = doc.createProcessingInstruction("xml", "version=\"1.0\" encoding=\"UTF-8\"");
        doc.appendChild(header);
        // 创建根节点
        QDomElement root = doc.createElement("StatusInformation");
        root.setAttribute("device", device);
        root.setAttribute("type", type);
        doc.appendChild(root);

        if (!file->isOpen())
        {
            if (!file->open(QIODevice::WriteOnly | QIODevice::Truncate))
            {
                return false; // 如果
            }
        }
        QTextStream stream(file);
        stream.setCodec("UTF-8");
        doc.save(stream, 4);
        file->close();
        return true;
    }
}
/*
 * Insert_new_Device_status_data
 * 作用：在日文件中插入一组设备状态数据
 */
bool Device_status_data_storage::Insert_new_Device_status_data(Device_status_data device_status_data)
{
    if (txt_or_xml)
    {
        if (!file->isOpen())
        {
            if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
            {
                return false; // 如果
            }
        }

        QByteArray linedata;
        QString line = QString::number(Record_number++) + ','
                       //+bright_temperature.DateTime +','
                       + QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss") + ',' + deal_int_data(device_status_data.General) + ',' + deal_int_data(device_status_data.EServo) + ',' + deal_int_data(device_status_data.AServo) + ',' + deal_int_data(device_status_data.RCV0) + ',' + deal_int_data(device_status_data.RCV1) + ',' + Control_decimal_bit(device_status_data.TRec1, 2, true) + ',' + Control_decimal_bit(device_status_data.TRec2, 2, true) + ',' + deal_int_data(device_status_data.SRec1) + ',' + deal_int_data(device_status_data.SRec2) + ',' + deal_int_data(device_status_data.LO) + ',' + deal_int_data(device_status_data.BIB) + ',' + Control_decimal_bit(device_status_data.TAmb1, 2, true) + ',' + Control_decimal_bit(device_status_data.TAmb2, 2, true) + ',' + Control_decimal_bit(device_status_data.TAmb3, 2, true) + ',' + Control_decimal_bit(device_status_data.TAmb4, 2, true) + ',' + deal_int_data(device_status_data.SurTem) + ',' + deal_int_data(device_status_data.SurHum) + ',' + deal_int_data(device_status_data.SurPre) + ',' + deal_int_data(device_status_data.Rain) + ',' + deal_int_data(device_status_data.Tir) + ',' + deal_int_data(device_status_data.TimeSync) + ',' + deal_int_data(device_status_data.ECM) + ',' + deal_int_data(device_status_data.ExPower) + ',' + deal_int_data(device_status_data.Communication);
        if (!isPureAscii(line))
        {
            file->close();
            return false;
        }
        linedata = line.toLatin1();
        linedata.append("\r\n");

        if (file->write(linedata) == -1)
        {
            file->close();
            return false;
        }
        file->close();
        return true;
    }
    else
    {

        QDomDocument doc;
        if (file->isOpen())
        {
            file->close();
        }

        if (!file->open(QIODevice::ReadWrite | QIODevice::Text))
        {
            return false; // 如果
        }

        // file->seek(0);
        if (!doc.setContent(file))
        {
            file->close();
            return false;
        }

        file->close();

        /*可添加判断xml声明是否存在，如果不存在则添加声明*/
        // 获取或创建根节点
        QDomElement root = doc.documentElement();
        if (root.isNull())
        {
            root = doc.createElement("StatusInformation");
            root.setAttribute("device", device);
            root.setAttribute("type", type);
            doc.appendChild(root);
        }

        // 创建Status节点

        QDomElement statuselement = doc.createElement("Status");

        // 添加数据
        QDomElement recordelement = doc.createElement("Record");
        recordelement.appendChild(doc.createTextNode(QString::number(Record_number++)));
        statuselement.appendChild(recordelement);
        QDomElement DateTimeelement = doc.createElement("DateTime");
        DateTimeelement.appendChild(doc.createTextNode(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss")));
        statuselement.appendChild(DateTimeelement);
        QDomElement Generalelement = doc.createElement("General");
        Generalelement.appendChild(doc.createTextNode(QString::number(device_status_data.General)));
        statuselement.appendChild(Generalelement);
        QDomElement EServoelement = doc.createElement("EServo");
        EServoelement.appendChild(doc.createTextNode(QString::number(device_status_data.EServo)));
        statuselement.appendChild(EServoelement);
        QDomElement AServoelement = doc.createElement("AServo");
        AServoelement.appendChild(doc.createTextNode(QString::number(device_status_data.AServo)));
        statuselement.appendChild(AServoelement);

        QDomElement RCV0element = doc.createElement("RCV0");
        RCV0element.appendChild(doc.createTextNode(QString::number(device_status_data.RCV0)));
        statuselement.appendChild(RCV0element);

        QDomElement RCV1element = doc.createElement("RCV1");
        RCV1element.appendChild(doc.createTextNode(QString::number(device_status_data.RCV1)));
        statuselement.appendChild(RCV1element);

        QDomElement TRec1element = doc.createElement("TRec1");
        TRec1element.appendChild(doc.createTextNode(Control_decimal_bit(device_status_data.TRec1, 2, true)));
        statuselement.appendChild(TRec1element);

        QDomElement TRec2element = doc.createElement("TRec2");
        TRec2element.appendChild(doc.createTextNode(Control_decimal_bit(device_status_data.TRec2, 2, true)));
        statuselement.appendChild(TRec2element);

        QDomElement SRec1element = doc.createElement("SRec1");
        SRec1element.appendChild(doc.createTextNode(QString::number(device_status_data.SRec1)));
        statuselement.appendChild(SRec1element);

        QDomElement SRec2element = doc.createElement("SRec2");
        SRec2element.appendChild(doc.createTextNode(QString::number(device_status_data.SRec2)));
        statuselement.appendChild(SRec2element);

        QDomElement LOelement = doc.createElement("LO");
        LOelement.appendChild(doc.createTextNode(QString::number(device_status_data.LO)));
        statuselement.appendChild(LOelement);

        QDomElement BIBelement = doc.createElement("BIB");
        BIBelement.appendChild(doc.createTextNode(QString::number(device_status_data.BIB)));
        statuselement.appendChild(BIBelement);

        QDomElement TAmb1element = doc.createElement("TAmb1");
        TAmb1element.appendChild(doc.createTextNode(Control_decimal_bit(device_status_data.TAmb1, 2, true)));
        statuselement.appendChild(TAmb1element);

        QDomElement TAmb2element = doc.createElement("TAmb2");
        TAmb2element.appendChild(doc.createTextNode(Control_decimal_bit(device_status_data.TAmb2, 2, true)));
        statuselement.appendChild(TAmb2element);

        QDomElement TAmb3element = doc.createElement("TAmb3");
        TAmb3element.appendChild(doc.createTextNode(Control_decimal_bit(device_status_data.TAmb3, 2, true)));
        statuselement.appendChild(TAmb3element);

        QDomElement TAmb4element = doc.createElement("TAmb4");
        TAmb4element.appendChild(doc.createTextNode(Control_decimal_bit(device_status_data.TAmb4, 2, true)));
        statuselement.appendChild(TAmb4element);

        QDomElement SurTemelement = doc.createElement("SurTem");
        SurTemelement.appendChild(doc.createTextNode(QString::number(device_status_data.SurTem)));
        statuselement.appendChild(SurTemelement);

        QDomElement SurHumelement = doc.createElement("SurHum");
        SurHumelement.appendChild(doc.createTextNode(QString::number(device_status_data.SurHum)));
        statuselement.appendChild(SurHumelement);

        QDomElement SurPreelement = doc.createElement("SurPre");
        SurPreelement.appendChild(doc.createTextNode(QString::number(device_status_data.SurPre)));
        statuselement.appendChild(SurPreelement);

        QDomElement Rainelement = doc.createElement("Rain");
        Rainelement.appendChild(doc.createTextNode(QString::number(device_status_data.Rain)));
        statuselement.appendChild(Rainelement);

        QDomElement Tirelement = doc.createElement("Tir");
        Tirelement.appendChild(doc.createTextNode(QString::number(device_status_data.Tir)));
        statuselement.appendChild(Tirelement);

        QDomElement TimeSyncelement = doc.createElement("TimeSync");
        TimeSyncelement.appendChild(doc.createTextNode(QString::number(device_status_data.TimeSync)));
        statuselement.appendChild(TimeSyncelement);

        QDomElement ECMelement = doc.createElement("ECM");
        ECMelement.appendChild(doc.createTextNode(QString::number(device_status_data.ECM)));
        statuselement.appendChild(ECMelement);

        QDomElement ExPowerelement = doc.createElement("ExPower");
        ExPowerelement.appendChild(doc.createTextNode(QString::number(device_status_data.ExPower)));
        statuselement.appendChild(ExPowerelement);

        QDomElement Communicationelement = doc.createElement("Communication");
        Communicationelement.appendChild(doc.createTextNode(QString::number(device_status_data.Communication)));
        statuselement.appendChild(Communicationelement);

        root.appendChild(statuselement);

        if (!file->open(QIODevice::ReadWrite | QIODevice::Truncate | QIODevice::Text))
        {
            return false; // 如果
        }

        QTextStream stream(file);
        stream.setCodec("UTF-8");
        doc.save(stream, 4);
        file->close();

        return true;
    }
}

/*
 * Generate_file_name
 * 作用：根据file_name中信息生成文件名
 */
QString Device_status_data_storage::Generate_file_name(FILENAME file_name)
{
    return file_name.pf1ag + "_" + file_name.productidentifier + "_" + file_name.of1ag + "_" + file_name.originator + "_" + file_name.yyyyMMddhhmmss + "_" + file_name.ftype + "_" + file_name.deviceidentification + "_" + file_name.equipmenttype + "_" + file_name.datatype + "_" + file_name.frequency + "." + file_name.type;
}
/*
 * isPureAscii
 * 作用：判断字符串str是否是ASCII字符
 */
bool Device_status_data_storage::isPureAscii(const QString &str)
{
    for (const QChar &ch : str)
    {
        if (ch.unicode() > 0x7f) // ASCII范围0x00~0x7f
        {
            return false;
        }
    }
    return true;
}

/*
 * Control_decimal_bit
 * 作用：将data数据保留bit个小数，并且转化成qstring返回
 * status：true 保留小数位时四舍五入
 *         false：保留小数位时不四舍五入
 */
QString Device_status_data_storage::Control_decimal_bit(double data, int bit, bool status)
{
    if (data == std::numeric_limits<double>::lowest())
    {
        return "-";
    }
    if (status)
    {
        return QString::number(data, 'f', bit);
    }
    else
    {
        double temp = std::trunc(data * std::pow(10, bit)) / std::pow(10, bit);
        return QString::number(temp, 'f', bit);
    }
}

QString Device_status_data_storage::deal_int_data(int data)
{
    if (data == std::numeric_limits<int>::lowest())
    {
        return "-";
    }
    return QString::number(data);
}

QString Device_status_data_storage::deal_qstring_data(QString data)
{
    if (data.isEmpty())
        return "-";
    return data;
}
/*
 * comparefilenames
 * 作用：对比两个文件名除了时分秒外的其他数据是否一致，并且检查时间是否合法
 * status：true 保留小数位时四舍五入
 *         false：保留小数位时不四舍五入
 */
bool Device_status_data_storage::comparefilenames(const QString &filename1, const QString &filename2)
{
    QString processedbasename1, extension1;
    QString processedbasename2, extension2;
    bool time1valid, time2valid;

    bool valid1 = processFileName(filename1, time1valid, processedbasename1, extension1);
    bool valid2 = processFileName(filename2, time2valid, processedbasename2, extension2);

    if (!valid1 || !valid2 || !time1valid || !time2valid)
    {
        return false;
    }
    if (extension1 != extension2)
    {
        return false;
    }

    return (processedbasename1 == processedbasename2);
}

bool Device_status_data_storage::processFileName(const QString &filename, bool &timevalid, QString &processedbasename, QString &extension)
{
    QFileInfo fileinfo(filename);
    QString basename = fileinfo.baseName();
    extension = fileinfo.suffix();

    QStringList parts = basename.split('_');
    if (parts.size() < 5)
    {
        timevalid = false;
        return false;
    }

    // 获取时间字段
    QString timestr = parts.value(4);
    if (timestr.length() != 14)
    {
        timevalid = false;
        return false;
    }

    // 验证时间合法性
    QDateTime dt = QDateTime::fromString(timestr, "yyyyMMddHHmmss");
    if (!dt.isValid())
    {
        timevalid = false;
        return false;
    }
    timevalid = true;

    // 替换时分秒为000000
    QString newtime = timestr.left(8) + "000000";
    parts[4] = newtime;
    processedbasename = parts.join("_");
    return true;
}
