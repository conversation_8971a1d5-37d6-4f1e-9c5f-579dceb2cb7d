﻿#ifndef CCOLORCARDDIALOG_H
#define CCOLORCARDDIALOG_H

#include <QDialog>
#include <QTabWidget>
#include <QTableWidgetItem>
namespace Ui
{
    class CColorCardDialog;
}

class CColorCardDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CColorCardDialog(QWidget *parent = nullptr);
    ~CColorCardDialog();
    void initializeTable();
    void setInitialColors();

private slots:
    // 当表格内容变化时更新颜色
    void onTableItemChanged(QTableWidgetItem *item);
    void on_radioButton_clicked();

    void on_pushButton_create_clicked();

private:
    Ui::CColorCardDialog *ui;
    // RGB字符串转QColor
    QColor parseColor(const QString &colorStr);
};

#endif // CCOLORCARDDIALOG_H
