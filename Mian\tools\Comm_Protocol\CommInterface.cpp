﻿#include "CommInterface.h"
#include <QDebug>

CommInterface::CommInterface(InterfaceType type, QObject *parent)
    : QObject(parent), m_type(type), m_serial(nullptr), m_tcpClient(nullptr), m_udp(nullptr)
{
    if (m_type == SERIAL)
    {
        m_serial = new SerialInterface(this);
        connect(m_serial, &SerialInterface::dataReceived, this, &CommInterface::handleSerialData);
        connect(m_serial, &SerialInterface::errorOccurred, this, &CommInterface::handleSerialError);
    }
    else if (m_type == TCP_CLIENT)
    {
        m_tcpClient = new TcpClientInterface(this);
        connect(m_tcpClient, &TcpClientInterface::dataReceived, this, &CommInterface::handleTcpData);
        connect(m_tcpClient, &TcpClientInterface::errorOccurred, this, &CommInterface::handleTcpError);
    }
    else if (m_type == UDP_CLIENT)
    {
        m_udp = new UdpInterface(this);
        connect(m_udp, &UdpInterface::dataReceived, this, &CommInterface::handleUdpData);
        connect(m_udp, &UdpInterface::errorOccurred, this, &CommInterface::handleUdpError);
    }
}
bool CommInterface::InterfaceTypeToggle(InterfaceType type)
{
    this->close();
    this->m_type = type;
    if (m_serial != NULL)
    {
        disconnect(m_serial, &SerialInterface::dataReceived, this, &CommInterface::handleSerialData);
        disconnect(m_serial, &SerialInterface::errorOccurred, this, &CommInterface::handleSerialError);
        delete (m_serial);
        m_serial = nullptr;
    }
    if (m_tcpClient != NULL)
    {
        disconnect(m_tcpClient, &TcpClientInterface::dataReceived, this, &CommInterface::handleTcpData);
        disconnect(m_tcpClient, &TcpClientInterface::errorOccurred, this, &CommInterface::handleTcpError);
        delete (m_tcpClient);
        m_tcpClient = nullptr;
    }
    if (m_type == SERIAL)
    {
        m_serial = new SerialInterface(this);
        connect(m_serial, &SerialInterface::dataReceived, this, &CommInterface::handleSerialData);
        connect(m_serial, &SerialInterface::errorOccurred, this, &CommInterface::handleSerialError);
    }
    else if (m_type == TCP_CLIENT)
    {
        m_tcpClient = new TcpClientInterface(this);
        connect(m_tcpClient, &TcpClientInterface::dataReceived, this, &CommInterface::handleTcpData);
        connect(m_tcpClient, &TcpClientInterface::errorOccurred, this, &CommInterface::handleTcpError);
    }
    else if (m_type == UDP_CLIENT)
    {
        m_udp = new UdpInterface(this);
        connect(m_udp, &UdpInterface::dataReceived, this, &CommInterface::handleUdpData);
        connect(m_udp, &UdpInterface::errorOccurred, this, &CommInterface::handleUdpError);
    }
    return true;
}

bool CommInterface::open(const QString &args, quint16 port)
{
    if (m_type == SERIAL)
    {
        return m_serial->openPort(args, QSerialPort::Baud115200, QSerialPort::Data8,
                                  QSerialPort::NoParity, QSerialPort::OneStop);
    }
    else if (m_type == TCP_CLIENT)
    {
        return m_tcpClient->connectToHost(args, port);
    }
    else if (m_type == UDP_CLIENT)
    {
        return m_udp->connectToHost(args, port);
    }
}

void CommInterface::close()
{
    if (m_type == SERIAL && m_serial)
    {
        m_serial->closePort();
    }
    else if (m_tcpClient)
    {
        m_tcpClient->disconnectFromHost();
    }
    else if (m_udp)
    {
        m_udp->close();
    }
}

qint64 CommInterface::send(const QByteArray &data)
{
    if (m_type == SERIAL && m_serial)
    {
        return m_serial->writeData(data);
    }
    else if (m_tcpClient)
    {
        return m_tcpClient->sendData(data);
    }
    else if (m_udp)
    {
        return m_udp->writeData(data);
    }
    return -1;
}

bool CommInterface::isConnected() const
{
    if (m_type == SERIAL && m_serial)
    {
        return m_serial->isOpen();
    }
    else if (m_tcpClient)
    {
        return m_tcpClient->isConnected();
    }
    else if (m_udp)
    {
        return m_udp->isBound();
    }
    return false;
}

void CommInterface::handleSerialData(const QByteArray &data)
{
    emit dataReceived(data);
}

void CommInterface::handleTcpData(const QByteArray &data)
{
    emit dataReceived(data);
}

void CommInterface::handleSerialError(const QString &error)
{
    emit errorOccurred(tr("串口错误: %1").arg(error));
}

void CommInterface::handleTcpError(const QString &error)
{
    // qDebug()<<tr("TCP错误: %1").arg(error);
    emit errorOccurred(tr("TCP错误: %1").arg(error));
}

void CommInterface::handleUdpData(const QByteArray &data)
{
    emit dataReceived(data);
}

void CommInterface::handleUdpError(const QString &error)
{
    emit errorOccurred(tr("UDP错误: %1").arg(error));
}
