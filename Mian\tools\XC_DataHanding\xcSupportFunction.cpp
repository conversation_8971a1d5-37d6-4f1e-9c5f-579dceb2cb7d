﻿#include "xcSupportFunction.h"
#include "string.h"

#define _xc_DISPLAY_MIN_FLOAT_ 0.00001

// 判断浮点数是否有效
int FloatIsValid(float f)
{
	return ((*(unsigned int *)&f) & 0x7f800000) != 0x7f800000;
}

// 得到字符串的长度, 以空格或者'\0'或者'\n'结尾
int GetStrLen(char *pStr)
{
	char *p = pStr;
	while (p != 0 && *p != ' ' && *p != '\n' && *p != 0)
		p++;
	return (p - pStr);
}

// 得到字符串的最后的地址
char *GetStrEnd(char *pStr)
{
	char *p = pStr;
	while (p != 0 && *p != 0)
		p++;
	return p;
}

// 比较字符串, 小于返回-1, 大于返回1, 等于返回0
int CompareStr(char *pStr1, char *pStr2, int iLen)
{
	while (pStr1 && pStr2 && *pStr1 && *pStr2 && iLen > 0)
	{
		if (*pStr1 < *pStr2)
			return -1;
		else if (*pStr1 > *pStr2)
			return 1;
		iLen--;
	}
	return 0;
}

// 比较内存, 等于返回0, 不等于返回非0
int CompareMem(void *pMem1, void *pMem2, int iLen)
{
	int i = 0;
	char *p1 = (char *)pMem1;
	char *p2 = (char *)pMem2;
	while (i < iLen)
	{
		if (p1[i] != p2[i])
			return -1;
		i++;
	}
	return 0;
}

// 内存拷贝
int MemCopy(char *pSrc, char *pDes, int iLen)
{
	int i;
	for (i = 0; i < iLen; i++)
		pDes[i] = pSrc[i];
	return 1;
}

// 内存拷贝（反转）
int MemCopyExt(char *pDes, char *pSrc, int iLen)
{
	int i;
	for (i = 0; i < iLen; i++)
		pDes[i] = pSrc[iLen - 1 - i];
	return 1;
}

// 内存设置
void MemSet1(unsigned char *buff, unsigned char data, unsigned int len)
{
	unsigned int i;
	for (i = 0; i < len; i++)
		buff[i] = data;
}

// 查找ListEdit的字符串
char *FindListEditStr(char *pStr, int iIndex)
{
	char *p = pStr;
	while (p != 0 && *p != 0 && iIndex > 0)
	{
		if ('\n' == *p)
			iIndex--;
		p++;
	}
	return p;
}

// 整数转换成字符串
char *PosIntToString(char *pBuffer, int iValue, char iCount)
{
	int i = iCount - 1;
	pBuffer[iCount] = 0;
	for (; i >= 0; i--)
	{
		pBuffer[i] = (iValue % 10) + '0';
		iValue = iValue / 10;
	}
	return pBuffer;
}

// 整型数转换成字符串
char *IntToString(char *pBuffer, int iValue, char iCount)
{
	if (iValue >= 0)
	{ // 是整数
		PosIntToString(pBuffer, iValue, iCount);
	}
	else
	{ // 是负数
		pBuffer[0] = '-';
		PosIntToString(pBuffer + 1, -iValue, iCount);
	}
	return pBuffer;
}
// 整数转换成字符串,自动计算位数,最大99999999
char *IntToStringExt(char *pBuffer, int iValue, int *iCount)
{
	int i;
	int minus = 0;
	// if( iValue > 999999999)return 0;
	*iCount = 0;
	if (iValue < 0)
	{
		iValue = -iValue;
		minus = 1;
	}
	if (iValue > 999999999)
		*iCount = 10;
	else if (iValue > 99999999)
		*iCount = 9;
	else if (iValue > 9999999)
		*iCount = 8;
	else if (iValue > 999999)
		*iCount = 7;
	else if (iValue > 99999)
		*iCount = 6;
	else if (iValue > 9999)
		*iCount = 5;
	else if (iValue > 999)
		*iCount = 4;
	else if (iValue > 99)
		*iCount = 3;
	else if (iValue > 9)
		*iCount = 2;
	else
		*iCount = 1;

	pBuffer[*iCount + minus] = '\0';
	i = *iCount + minus - 1;
	for (; i >= minus; i--)
	{
		pBuffer[i] = (iValue % 10) + '0';
		iValue = iValue / 10;
	}

	//
	if (minus > 0)
	{
		pBuffer[0] = '-';
		(*iCount)++;
	}

	return pBuffer;
}
// 整型数转换成16进制字符串
char *IntToHex(char *pBuffer, int iValue, char iCount)
{
	int i = iCount - 1;
	for (; i >= 0; i--)
	{
		char c = (iValue & 0xF);
		if (c < 10)
			pBuffer[i] = c + '0';
		else
			pBuffer[i] = c + '7';
		iValue = iValue >> 4;
	}
	return pBuffer;
}

// 整型数转换成BCD
char *IntToBCDExt(char *pBuffer, int iValue, char iCount)
{
	int i;
	for (i = 0; i < iCount; i++)
	{
		char c = (iValue % 10);
		iValue = iValue / 10;
		pBuffer[i] = c + ((iValue % 10) << 4);
		iValue = iValue / 10;
	}
	return pBuffer;
}

// 整型数转换成BCD
char *IntToBCD(char *pBuffer, int iValue, char iCount)
{
	int i = iCount - 1;
	for (; i >= 0; i--)
	{
		char c = (iValue % 10);
		iValue = iValue / 10;
		pBuffer[i] = c + ((iValue % 10) << 4);
		iValue = iValue / 10;
	}
	return pBuffer;
}

// 正浮点数转换成字符串
char *PosFloatToString(char *pBuffer, float fValue, char iIntCount, char iDecimalCount)
{
	int iInt;
	int iMulti = 1;
	int i = 0;
	for (; i < iDecimalCount; i++)
		iMulti = iMulti * 10;

	iInt = (int)(fValue * iMulti + 0.5);

	// 整数部分
	IntToString(pBuffer, iInt / iMulti, iIntCount);
	pBuffer[iIntCount] = '.';

	// 小数部分
	IntToString(pBuffer + iIntCount + 1, iInt % iMulti, iDecimalCount);
	return pBuffer;
}
// 正浮点数转换成字符串
char *PosFloatToStringExt(char *pBuffer, float fValue, char iDecimalCount, int *iCount)
{
	int iInt;
	int iMulti = 1;
	int i = 0;
	for (; i < iDecimalCount; i++)
		iMulti = iMulti * 10;
	*iCount = 0;

	iInt = (int)(fValue * iMulti + 0.5);

	// 整数部分
	IntToStringExt(pBuffer, iInt / iMulti, &i);
	pBuffer[i] = '.';

	// 小数部分
	IntToString(pBuffer + i + 1, iInt % iMulti, iDecimalCount);
	*iCount = i + iDecimalCount + 1;
	return pBuffer;
}

// 浮点数转换成字符串
char *FloatToString(char *pBuffer, float fValue, char iIntCount, char iDecimalCount)
{
	if (!FloatIsValid(fValue))
	{ // 数据非法,直接赋值--.--
		memset(pBuffer, '-', iIntCount);
		pBuffer[iIntCount] = '.';
		memset(pBuffer + iIntCount + 1, '-', iDecimalCount);
	}
	else
	{
		if (fValue > -LCD_MINFLOATVALUE && fValue < LCD_MINFLOATVALUE)
		{
			fValue = 0.0;
		}

		if (fValue >= 0.0)
		{
			PosFloatToString(pBuffer, fValue, iIntCount, iDecimalCount);
		}
		else
		{
			pBuffer[0] = '-';
			PosFloatToString(pBuffer + 1, -fValue, iIntCount, iDecimalCount);
		}
	}
	return pBuffer;
}
// 浮点数转换成字符串
char *FloatToStringExt(char *pBuffer, float fValue, char iDecimalCount, int *iCount)
{
	*iCount = 0;
	if (!FloatIsValid(fValue))
	{ // 数据非法,直接赋值--.--
		memset(pBuffer, '-', 1);
		pBuffer[1] = '.';
		memset(pBuffer + 2, '-', iDecimalCount);
		*iCount = iDecimalCount + 2;
	}
	else
	{
		if (fValue > -LCD_MINFLOATVALUE && fValue < LCD_MINFLOATVALUE)
		{
			fValue = 0.0;
		}

		if (fValue >= 0.0)
		{
			PosFloatToStringExt(pBuffer, fValue, iDecimalCount, iCount);
		}
		else
		{
			pBuffer[0] = '-';
			PosFloatToStringExt(pBuffer + 1, -fValue, iDecimalCount, iCount);
			*iCount++;
		}
	}
	return pBuffer;
}

// IP转换成字符串
// *************** ->"***************"
char *IPToString(char *pBuffer, unsigned char aIP[4])
{
	IntToString(pBuffer + 0, aIP[0], 3);
	pBuffer[3] = '.';
	IntToString(pBuffer + 4, aIP[1], 3);
	pBuffer[7] = '.';
	IntToString(pBuffer + 8, aIP[2], 3);
	pBuffer[11] = '.';
	IntToString(pBuffer + 12, aIP[3], 3);
	return pBuffer;
}
// IP转换成字符串
// *************** ->"*******"
char *IPToStringExt(char *pBuffer, unsigned char aIP[4])
{
	int c = 3, s = 0, i;
	for (i = 0; i < 4; i++)
	{
		c = 3;
		if (aIP[i] < 100)
			c = 2;
		if (aIP[i] < 10)
			c = 1;
		IntToString(pBuffer + s, aIP[i], c);
		s += c;
		pBuffer[s++] = '.';
	}
	pBuffer[s - 1] = 0;

	return pBuffer;
}

// 日期转换成字符串
char *DateToString(char *pBuffer, unsigned short iYear, unsigned short iMonth, unsigned short iDay)
{
	// 年
	IntToString(pBuffer + 0, iYear, 4);
	pBuffer[4] = '-';
	// 月
	IntToString(pBuffer + 5, iMonth, 2);
	pBuffer[7] = '-';
	// 日
	IntToString(pBuffer + 8, iDay, 2);
	//	pBuffer[10] = 0;
	return pBuffer;
}

// 时间转换成字符串
char *TimeToString(char *pBuffer, unsigned short iHour, unsigned short iMinute, unsigned short iSecond)
{
	// 时
	IntToString(pBuffer + 0, iHour, 2);
	pBuffer[2] = ':';
	// 分
	IntToString(pBuffer + 3, iMinute, 2);
	pBuffer[5] = ':';
	// 秒
	IntToString(pBuffer + 6, iSecond, 2);
	//	pBuffer[8] = 0;
	return pBuffer;
}

// 16进制转换成字符串
char *HexToString(char *pHex, char *pStr, char iCount)
{
	int i = 0;
	for (; i < iCount / 2; i++)
	{
		char c = (pHex[i] >> 4) & 0xF;
		if (c < 10)
			pStr[2 * i + 0] = c + '0';
		else
			pStr[2 * i + 0] = c + '7';
		c = pHex[i] & 0xF;
		if (c < 10)
			pStr[2 * i + 1] = c + '0';
		else
			pStr[2 * i + 1] = c + '7';
	}
	return pStr;
}

// 字符串转换成整型数
int StringToInt(char *pBuffer, char iCount)
{
	int iValue = 0;
	int i = 0;
	for (; i < iCount; i++)
	{
		if ((pBuffer[i] >= '0') && (pBuffer[i] <= '9'))
			iValue = iValue * 10 + pBuffer[i] - '0';
		else
			break;
	}
	return iValue;
} // 字符串转换成整型数
int StringToIntExt(char *pBuffer, int *iCount)
{
	int iValue = 0;
	int i = 0;
	for (; i < *iCount; i++)
	{
		if ((pBuffer[i] >= '0') && (pBuffer[i] <= '9'))
			iValue = iValue * 10 + pBuffer[i] - '0';
		else
			break;
	}
	*iCount = i;
	return iValue;
}

// 判断bcd码是否非法
// 返回0-合法
// 返回1-非法
int BCDJudge(char *pBuffer, char iCount)
{
	int i;
	for (i = 0; i < iCount; i++)
	{
		if ((pBuffer[i] & 0x0f) > 9)
			return 1;
		if (((pBuffer[i] >> 4) & 0x0f) > 9)
			return 1;
	}
	return 0;
}
int BCDToIntExt(char *pBuffer, char iCount)
{
	int iValue = 0;
	int i = 0;
	for (i = iCount - 1; i >= 0; i--)
	{
		iValue = iValue * 10 + ((unsigned char)pBuffer[i] >> 4);
		iValue = iValue * 10 + ((unsigned char)pBuffer[i] & 0xf);
	}
	return iValue;
}

// BCD转换成整型数
int BCDToInt(char *pBuffer, char iCount)
{
	int iValue = 0;
	int i = 0;
	for (; i < iCount; i++)
	{
		iValue = iValue * 10 + ((unsigned char)pBuffer[i] >> 4);
		iValue = iValue * 10 + ((unsigned char)pBuffer[i] & 0xf);
	}
	return iValue;
}

// Hex转换成整型数
int HexToInt(char *pBuffer, char iCount)
{
	int i = 0;
	int iRet = 0;
	for (; i < iCount; i++)
	{
		char c = pBuffer[i] - '0';
		if (c >= 10)
			c -= 7;
		iRet = iRet * 16 + c;
	}
	return iRet;
}

// 字符串转换成浮点数
float StringToFloat(char *pBuffer, char iCount)
{
	float fIntValue = 0.0;
	float fDecimalValue = 0.0;
	float DecimalNumber = 0;
	// 计算整数部分

	char *p = pBuffer;
	if ((*p) < '0')
		return 0;
	if ((*p) > '9')
		return 0;
	while (p - pBuffer < iCount && '.' != *p)
	{
		if ((*p) < '0')
			break;
		if ((*p) > '9')
			break;

		fIntValue = fIntValue * 10 + (*p) - '0';
		p++;
	}
	// 计算小数部分
	p++;
	DecimalNumber = 1;
	while (p - pBuffer < iCount)
	{
		if ((*p) < '0')
			break;
		if ((*p) > '9')
			break;
		fDecimalValue = fDecimalValue * 10 + (*p) - '0';
		DecimalNumber *= 10;
		if (DecimalNumber >= 1000000)
			break;
		p++;
	}
	fDecimalValue /= DecimalNumber;
	return fIntValue + fDecimalValue;
}

// 字符串转换成IP
char StringToIP(char *pBuffer, unsigned char aIP[4])
{
	int iSub = 0;
	// int ip = 0;
	iSub = StringToInt(pBuffer, 3);
	if (iSub > 255)
		return 1;
	aIP[0] = iSub;
	iSub = StringToInt(pBuffer + 4, 3);
	if (iSub > 255)
		return 1;
	aIP[1] = iSub;
	iSub = StringToInt(pBuffer + 8, 3);
	if (iSub > 255)
		return 1;
	aIP[2] = iSub;
	iSub = StringToInt(pBuffer + 12, 3);
	if (iSub > 255)
		return 1;
	aIP[3] = iSub;
	return 0;
}
// 字符串转换成IP
char StringToIPExt(char *pBuffer, unsigned char aIP[4])
{
	int iSub = 0;
	int iCount = 3;
	int iIndex = 0;
	// int ip = 0;
	iSub = StringToIntExt(pBuffer + iIndex, &iCount);
	if (iSub > 255)
		return 1;
	aIP[0] = iSub;
	iIndex += iCount + 1;
	iCount = 3;
	iSub = StringToIntExt(pBuffer + iIndex, &iCount);
	if (iSub > 255)
		return 1;
	aIP[1] = iSub;
	iIndex += iCount + 1;
	iCount = 3;
	iSub = StringToIntExt(pBuffer + iIndex, &iCount);
	if (iSub > 255)
		return 1;
	aIP[2] = iSub;
	iIndex += iCount + 1;
	iCount = 3;
	iSub = StringToIntExt(pBuffer + iIndex, &iCount);
	if (iSub > 255)
		return 1;
	aIP[3] = iSub;
	return 0;
}
char DaysPerMonthArray[] =
	{
		0,	// 0
		31, // 1
		28, // 2
		31, // 3
		30, // 4
		31, // 5
		30, // 6
		31, // 7
		31, // 8
		30, // 9
		31, // 10
		30, // 11
		31	// 12
};

// 字符串转换成日期, 返回0, 表示格式正确, 返回非0, 表示格式错误
char StringToDate(char *pBuffer, unsigned short *iYear, unsigned short *iMonth, unsigned short *iDay)
{
	unsigned short year, month, day;
	// 年
	int bLeap = 0;
	year = StringToInt(pBuffer, 4);
	if (year < 2000)
		return ERROR_DATE_YEAR_LOWERLIMIT;
	if (year > 2250)
		return ERROR_DATE_YEAR_UPPERLIMIT;
	*iYear = year;

	// 月
	month = StringToInt(pBuffer + 5, 2);
	if (month <= 0)
		return ERROR_DATE_MONTH_LOWERLIMIT;
	if (month > 12)
		return ERROR_DATE_MONTH_UPPERLIMIT;
	*iMonth = month;

	// 日
	day = StringToInt(pBuffer + 8, 2);
	if (day <= 0)
		return ERROR_DATE_DAY_LOWERLIMIT;

	// 是否是闰年
	bLeap = ((0 == (year) % 4) && (0 != (year) % 100)) || (0 == (year) % 400);
	if (day > DaysPerMonthArray[month])
	{
		if (2 == month && day <= 29)
		{
			if (!((bLeap && day <= 29) || (!bLeap && day <= 28)))
				return ERROR_DATE_BEAP;
		}
		else
			return ERROR_DATE_DAY_UPPERLIMIT;
	}
	*iDay = day;
	return 0;
}

// 字符串转换成时间, 返回0, 表示格式正确, 返回非0, 表示格式错误
char StringToTime(char *pBuffer, unsigned short *iHour, unsigned short *iMinute, unsigned short *iSecond)
{
	unsigned short hour, minute, second;
	hour = StringToInt(pBuffer, 2);
	if (hour > 23)
		return ERROR_TIME_HOUR_UPPERLIMIT;
	minute = StringToInt(pBuffer + 3, 2);
	if (minute > 59)
		return ERROR_TIME_MINUTE_UPPERLIMIT;
	second = StringToInt(pBuffer + 6, 2);
	if (second > 59)
		return ERROR_TIME_SECOND_UPPERLIMIT;
	*iHour = hour;
	*iMinute = minute;
	*iSecond = second;
	return 0;
}

// 字符串转换成16进制
char *StringToHex(char *pStr, char *pHex, char iCount)
{
	int i = 0;
	for (; i < iCount / 2; i++)
	{
		char c = pStr[2 * i + 0];
		if (c <= '9')
			pHex[i] = (c - '0') << 4;
		else
			pHex[i] = (c - '7') << 4;

		c = pStr[2 * i + 1];
		if (c <= '9')
			pHex[i] += (c - '0');
		else
			pHex[i] += (c - '7');
	}
	return pHex;
}

// 改变数字, bInc为0, 表示减1, bInc为1, 表示加1
char ChangeIntValue(char *pValue, char bInc)
{
	if (bInc)
	{
		if (*pValue >= '9')
			*pValue = '0';
		else if (*pValue < '0')
			*pValue = '0';
		else
			*pValue = *pValue + 1;
	}
	else
	{
		if (*pValue <= '0')
			*pValue = '9';
		else if (*pValue > '0')
			*pValue = '9';
		else
			*pValue = *pValue - 1;
	}
	return *pValue;
}

// 改变数字(Hex), bInc为0, 表示减1, bInc为1, 表示加1
char ChangeHexValue(char *pValue, char bInc)
{
	if (bInc)
	{
		if (*pValue == '9')
			*pValue = 'A';
		else if (*pValue >= 'F')
			*pValue = '0';
		else
			*pValue = (*pValue) + 1;
	}
	else
	{
		if (*pValue <= '0')
			*pValue = 'F';
		else if (*pValue == 'A')
			*pValue = '9';
		else
			*pValue = (*pValue) - 1;
	}
	return *pValue;
}

// 字节次序转换函数
void ByteOrderChange(char *pValue, int iByteCount)
{
	int i;
	if (iByteCount <= 1)
		return;
	for (i = 0; i < (iByteCount >> 1); i++)
	{
		char c = pValue[i];
		pValue[i] = pValue[iByteCount - i - 1];
		pValue[iByteCount - i - 1] = c;
	}
}

// 比较浮点数
int xcCompareFloat(float f1, float f2)
{
	float f = f1 - f2;
	if (f < -_xc_DISPLAY_MIN_FLOAT_)
		return -1;
	else if (f > _xc_DISPLAY_MIN_FLOAT_)
		return 1;
	else
		return 0;
}

// 比较浮点数
int xcCompareDouble(double f1, double f2)
{
	double f = f1 - f2;
	if (f < -_xc_DISPLAY_MIN_FLOAT_)
		return -1;
	else if (f > _xc_DISPLAY_MIN_FLOAT_)
		return 1;
	else
		return 0;
}

unsigned char Data_Hextoint(unsigned char iValue)
{
	return ((iValue >> 4) * 10 + (iValue & 0x0F));
}
// BCD码转换成float型，digit为总共字节数，decimal为小数位数
float GetBCDToFloatValue(unsigned char *BCD, unsigned char digit, unsigned char decimal)
{
	// digit：转换前共几个字节，decimal：转换后小数位数
	unsigned char i, j;
	unsigned int res, tmp;
	float ft;

	res = 0;
	for (i = 0; i < digit; i++)
	{
		tmp = Data_Hextoint(*(BCD + i));
		for (j = 0; j < (2 * i); j++)
		{
			tmp = tmp * 10;
		}
		res = res + tmp;
	}
	tmp = 1;
	for (i = 0; i < decimal; i++)
	{
		tmp = tmp * 10;
	}
	ft = (float)res / tmp;
	return ft;
}

unsigned char Data_InttoHex(unsigned char iValue)
{
	unsigned char iTmp;
	unsigned char iYu;
	iTmp = iValue;
	if (/*(iTmp>=0) && */ (iTmp < 100))
	{
		iYu = iTmp / 10;
		iTmp = iTmp % 10;
		return ((iYu << 4) + iTmp);
	}
	else
		return 0xFF;
}

//------------------------------------------------------------------------
// ASCII 到 HEX的转换函数
// 入口参数： O_data: 转换数据的入口指针，
// N_data: 转换后新数据的入口指针
// len : 需要转换的长度
// 返回参数：-1: 转换失败
// 其它：转换后数据长度
// 注意：O_data[]数组中的数据在转换过程中会被修改。

int ascii_2_hex(char *O_data, char *N_data, int len)
{

	int i, j, tmp_len;
	unsigned char tmpData;
	char *O_buf = O_data;
	char *N_buf = N_data;

	for (i = 0; i < len; i++)
	{

		if ((O_buf[i] >= '0') && (O_buf[i] <= '9'))
		{
			tmpData = O_buf[i] - '0';
		}
		else if ((O_buf[i] >= 'A') && (O_buf[i] <= 'F')) // A....F
		{
			tmpData = O_buf[i] - 0x37;
		}
		else if ((O_buf[i] >= 'a') && (O_buf[i] <= 'f')) // a....f
		{
			tmpData = O_buf[i] - 0x57;
		}
		else
		{
			return 0;
		}

		O_buf[i] = tmpData;
	}

	for (tmp_len = 0, j = 0; j < i; j += 2)
	{
		N_buf[tmp_len++] = (O_buf[j] << 4) | O_buf[j + 1];
	}

	return tmp_len;
}

//-----------------------------------------------------------------

// HEX 到 ASCII的转换函数
// 入口参数： data: 转换数据的入口指针
// buffer: 转换后数据入口指针
// len : 需要转换的长度
// 返回参数：转换后数据长度
// 高4位在后，低4位在前
// 如 0x12 0x34 ==>> 0x32 0x31 0x34 0x33
int hex_2_ascii(char *data, char *buffer, int len)
{
	const char ascTable[17] = {"0123456789ABCDEF"};
	char *tmp_p = buffer;
	int i, pos;

	pos = 0;

	for (i = 0; i < len; i++)
	{
		tmp_p[pos++] = ascTable[0x0f & (data[i] >> 4)];
		tmp_p[pos++] = ascTable[data[i] & 0x0f];
	}

	tmp_p[pos] = '\0';

	return pos;
}

// 字符数组转换成BCD数组
unsigned char *CharArr2BcdArr(unsigned char *pBcdArr, unsigned char *pCharArr, int iCount)
{
	int i = 0;
	for (; i < iCount; i++)
		pBcdArr[i] = ((pCharArr[i] / 10) << 4) + (pCharArr[i] % 10);
	return pBcdArr;
}

// BCD数组转换成字符数组
unsigned char *BcdArr2CharArr(unsigned char *pCharArr, unsigned char *pBcdArr, int iCount)
{
	int i = 0;
	for (; i < iCount; i++)
		pCharArr[i] = ((pBcdArr[i] >> 4) * 10) + (pBcdArr[i] & 0xF);
	return pCharArr;
}

// 添加数据到Flash,返回下一个数据保存的地址
unsigned int AddDataToFlash(unsigned char *buff, unsigned int len,
							unsigned int addr_all, unsigned int size_all,
							unsigned int size_sector, unsigned int pos,
							FlashWriteCallback write, FlashSectorEraseACallback erase)
{
	// 当前的扇区
	unsigned int iCurrSeries = ((pos - addr_all) % size_all) / size_sector;
	// 新的扇区(注意这里预先计算下一个扇区,保证总有一个扇区的数据是0xFF)
	unsigned int iNewSeries = ((pos + len + 1 - addr_all) % size_all) / size_sector;

	// 如果跨扇区就清除下一个扇区的
	if (iCurrSeries != iNewSeries)
	{
		erase(addr_all + iNewSeries * size_sector);
	}

	// 写数据
	if (iCurrSeries != iNewSeries && 0 == iNewSeries)
	{ // 新的扇区在起始扇区
		unsigned int len1 = addr_all + size_all - pos;
		write(pos, buff, len1);
		if (len > len1)
		{
			unsigned int len2 = len - len1;
			write(addr_all, buff + len1, len2);
			return addr_all + len2;
		}
		else
			return addr_all;
	}
	else
	{ // 新的扇区不在起始扇区
		write(pos, buff, len);
		return pos + len;
	}
}

// 从Flash读取数据,返回读取的数据长度
unsigned int ReadDataFromFlash(unsigned char *buff, unsigned int len,
							   unsigned int addr_all, unsigned int size_all,
							   unsigned int size_sector, int pos,
							   FlashReadCallback read)
{
	// 如果地址超出范围，则重新设置地址
	if (pos < (int)addr_all)
		pos += size_all;

	// 读取数据
	if (pos + len > addr_all + size_all)
	{ // 读取的数据在边界上
		unsigned int len1 = pos + len - addr_all - size_all;
		unsigned int len2 = len - len1;
		read(pos, buff, len1);
		read(addr_all, buff + len1, len2);
	}
	else
	{ // 读取的数据在连续的内部空间
		read(pos, buff, len);
	}
	return len;
}

// 浮点型数据转换为bcd码
// I 整数位数
// D 小数位数
// ft:123456.789 -> 90 78 56 34 12
void FloatToBcd(float ft, unsigned char *pBCD, unsigned char I, unsigned char D)
{
	if (!FloatIsValid(ft))
	{ // 数据非法,直接赋值FF
		memset(pBCD, 0xFF, (I + D) >> 1);
		return;
	}
	else
	{
		double d = ft;
		double ift = 1;
		double iPow = 1;
		int i;
		unsigned char hex;
		for (i = 0; i < D; i++) // ft = 123456789.123456 I = 6,D = 3;
		{
			d *= 10; // ft = 123456789123.456
		}
		d += 0.5;
		I += D; // I = 9;

		for (i = 0; i < I; i++)
		{
			iPow *= 10; // iPow = 1000000000;
		}

		ift = (int)(d / iPow); // 可能会有问题，比如溢出

		d -= (ift * iPow); // d = 4 56 78 91 23.456
		if (I & 0x01)
			iPow *= 10; // iPow = 1 00 00 00 00 00;

		if (I & 0x01)
			I++;
		i = I >> 1;
		while (i > 0)
		{
			iPow /= 100; // iPow = 10000000

			ift = (int)(d / iPow);

			hex = (unsigned char)(d / iPow); // hex = 04;
			d -= (hex * iPow);
			IntToBCD((char *)&pBCD[i - 1], hex, 1); // pBcd:04 56 78 91 23
			i--;
		}
	}
}

// bcd码转换为浮点型数据
// I 整数位数
// D 小数位数
// pBcd[0..n]:12 34 56 78 -> 8563.412 (I = 4,D = 3)
void BcdToFloat(unsigned char *pBcd, float *ft, unsigned char I, unsigned char D)
{
	unsigned char i, len;
	double dec = 1;

	len = I + D;
	if (len & 0x01)
	{
		len++;
		pBcd[(len >> 1) - 1] &= 0x0f;
	}

	for (i = 0; i < D; i++)
	{
		dec *= 10;
	}

	len >>= 1;
	(*ft) = 0;
	for (i = 0; i < len; i++)
	{
		(*ft) *= 100;
		(*ft) += BCDToInt((char *)&pBcd[i], 1);
	}
	(*ft) /= dec;
}

// 计算异或校验和
unsigned char CalcCheckIORSum8(unsigned char *pData, unsigned int iLen)
{
	unsigned int i = 0;
	unsigned char sum = 0;
	for (; i < iLen; i++)
		sum ^= pData[i];
	return sum;
}

// 计算校验和
unsigned int CalcCheckSum8(unsigned char *pData, unsigned int iLen)
{
	unsigned int i = 0;
	unsigned int sum = 0;
	for (; i < iLen; i++)
		sum += pData[i];
	return sum;
}
unsigned short CalcCheckSum16(unsigned short *pData, unsigned int iLen)
{
	unsigned int i = 0;
	unsigned short sum = 0;
	for (; i < iLen; i++)
		sum += pData[i];
	return sum;
}
unsigned int CalcCheckSum32(unsigned int *pData, unsigned int iLen)
{
	unsigned int i = 0;
	unsigned int sum = 0;
	for (; i < iLen; i++)
		sum += pData[i];
	return sum;
}

// 在内存中查找某个数据,返回找到的第1个字节的地址,否则返回0
unsigned char *MemoryFind(unsigned char *pData, unsigned int iLen, unsigned char c)
{
	unsigned int i = 0;
	for (; i < iLen; i++)
	{
		if (c == pData[i])
			return &pData[i];
	}
	return 0;
}

// CRC8 校验
unsigned char CalcCRC8(unsigned char *ptr, unsigned char len)
{
	unsigned char i;
	unsigned char crc = 0;
	while (len-- != 0)
	{
		for (i = 1; i != 0; i *= 2)
		{
			if ((crc & 1) != 0)
			{
				crc /= 2;
				crc ^= 0x8C;
			}
			else
				crc /= 2;
			if ((*ptr & i) != 0)
				crc ^= 0x8C;
		}
		ptr++;
	}
	return (crc);
}

// CRC16 校验
unsigned short CalcCRC16(unsigned char *Array, unsigned int iLen)
{
	unsigned int IX, IY, CRC;
	CRC = 0xFFFF;

	if (iLen < 1)
		CRC = 0;
	else
	{
		iLen--;
		for (IX = 0; IX <= iLen; IX++)
		{
			CRC = CRC ^ (unsigned int)(Array[IX]);
			for (IY = 0; IY <= 7; IY++)
				if ((CRC & 1) != 0)
					CRC = (CRC >> 1) ^ 0xA001;
				else
					CRC = CRC >> 1;
		}
	}
	return (CRC); // 高位置在低8位，低位置在高8位， 共16位
}

#ifdef _EMB_SIMULATION
// CRC32 校验
unsigned int CalcCRC32(unsigned int *Array, unsigned int iLen)
{
	unsigned int xbit;
	unsigned int id;
	unsigned int bits;
	unsigned int _CRC = 0xffffffff;
	int dwPolynomialtt = 0x04c11db7;
	for (int i = 0; i < iLen; i++)
	{
		id = (unsigned int)(Array[i]);
		xbit = (1 << 31);
		for (bits = 0; bits < 32; bits++)
		{
			if (_CRC & 0x80000000)
			{
				_CRC <<= 1;
				_CRC ^= dwPolynomialtt;
			}
			else
			{
				_CRC <<= 1;
			}
			if (id & xbit)
			{
				_CRC ^= dwPolynomialtt;
			}
			xbit >>= 1;
		}
	}
	return _CRC;
}

unsigned int CRC32Reset(void)
{
	return 0xffffffff;
}
// CRC32 校验
unsigned int CalcCRC32Accumulate(unsigned int *Array, unsigned int iLen, unsigned int _CRC)
{
	unsigned int xbit;
	unsigned int id;
	unsigned int bits;
	int dwPolynomialtt = 0x04c11db7;
	for (int i = 0; i < iLen; i++)
	{
		id = (unsigned int)(Array[i]);
		xbit = (1 << 31);
		for (bits = 0; bits < 32; bits++)
		{
			if (_CRC & 0x80000000)
			{
				_CRC <<= 1;
				_CRC ^= dwPolynomialtt;
			}
			else
			{
				_CRC <<= 1;
			}
			if (id & xbit)
			{
				_CRC ^= dwPolynomialtt;
			}
			xbit >>= 1;
		}
	}
	return _CRC;
}
#endif

// 时间结构转换
void Time2Pack(PTIME_PACK dest, PTIME_REC src)
{
	dest->Year = (unsigned char)(src->wYear - 2000);
	dest->Month = (unsigned char)src->wMonth;
	dest->Day = (unsigned char)src->wDay;
	dest->Hour = (unsigned char)src->wHour;
	dest->Min = (unsigned char)src->wMinute;
	dest->Sec = (unsigned char)src->wSecond;
}
void Time2PackBCD(PTIME_PACK dest, PTIME_REC src)
{
	src->wYear -= 2000;
	IntToBCD((char *)&dest->Year, src->wYear, 1);
	IntToBCD((char *)&dest->Month, src->wMonth, 1);
	IntToBCD((char *)&dest->Day, src->wDay, 1);
	IntToBCD((char *)&dest->Hour, src->wHour, 1);
	IntToBCD((char *)&dest->Min, src->wMinute, 1);
	IntToBCD((char *)&dest->Sec, src->wSecond, 1);
}
void Pack2Time(PTIME_REC dest, PTIME_PACK src)
{
	dest->wYear = (unsigned short)src->Year + 2000;
	dest->wMonth = (unsigned short)src->Month;
	dest->wDay = (unsigned short)src->Day;
	dest->wHour = (unsigned short)src->Hour;
	dest->wMinute = (unsigned short)src->Min;
	dest->wSecond = (unsigned short)src->Sec;
	dest->wMilliseconds = 0;
}

// 数据转化
// Byte0..ByteN..
// Byte0 >>= N;   //循环右移
// 高低字节4位交换
void DataEnByXc(char *pData, int iLen, char *pReData)
{
	int i, j;
	char d = 0;
	const uchar bitdef[] = {0x00, 0x01, 0x03, 0x07, 0x0f, 0x1f, 0x3f, 0x7f, 0xff};
	for (i = 0; i < iLen; i++)
	{
		// 右移
		j = i & 7;

		d = (*pData) & (bitdef[j]);
		*pReData = (*pData >> j);
		(*pReData) &= (0xff >> j);
		(*pReData) |= (d << (8 - j));

		// 高低位转换
		d = *pReData;
		*pReData <<= 4;
		*pReData &= 0xf0;
		*pReData |= (0x0f & (d >> 4));

		pData++;
		pReData++;
	}
}

// 数据转化
// Byte0..ByteN..
// 高低字节4位交换
// Byte0 <<= N;   //循环右移
void DataDeByXc(char *pReData, int iLen, char *pData)
{
	int i, j;
	char d = 0;
	const uchar bitdef[] = {0x00, 0x80, 0xc0, 0xe0, 0xf0, 0xf8, 0xfc, 0xfe, 0xff};

	for (i = 0; i < iLen; i++)
	{
		// 高低位转换
		d = *pReData;
		*pReData <<= 4;
		*pReData &= 0xf0;
		*pReData |= (0x0f & (d >> 4));

		// 左移
		j = i & 7;
		d = (*pReData) & ((bitdef[j]));
		*pData = (*pReData << j);
		(*pData) &= (0xff << j);
		(*pData) |= (d >> ((8 - j)));

		pData++;
		pReData++;
	}
}

// 在pStr1中查找数字组成的首个子串，返回长度；返回0表示无数字子串
int GerSubNumStr(char *pStr1, int len1, char *pStr2)
{
	int i, len = 0;
	for (i = 0; i < len1; i++)
	{
		if ((*(pStr1 + i) >= '0') && (*(pStr1 + i) <= '9'))
		{
			*(pStr2 + (len++)) = *(pStr1 + i);
		}
		else if (len > 0)
		{
			break;
		}
	}
	return len;
}

int ConvertVersionToInteger(char *strVersion)
{
	int IntVersion = 0;
	for (int i = 0; i < strlen(strVersion); i++)
	{
		int t = strVersion[i];
		if ((t >= '0') && (t <= '9'))
		{
			IntVersion *= 10;
			IntVersion += (t - '0');
		}
	}
	return IntVersion;
}

const unsigned int LunarCalendarTable[199] =
	{
		0x04AE53, 0x0A5748, 0x5526BD, 0x0D2650, 0x0D9544, 0x46AAB9, 0x056A4D, 0x09AD42, 0x24AEB6, 0x04AE4A, /*1901-1910*/
		0x6A4DBE, 0x0A4D52, 0x0D2546, 0x5D52BA, 0x0B544E, 0x0D6A43, 0x296D37, 0x095B4B, 0x749BC1, 0x049754, /*1911-1920*/
		0x0A4B48, 0x5B25BC, 0x06A550, 0x06D445, 0x4ADAB8, 0x02B64D, 0x095742, 0x2497B7, 0x04974A, 0x664B3E, /*1921-1930*/
		0x0D4A51, 0x0EA546, 0x56D4BA, 0x05AD4E, 0x02B644, 0x393738, 0x092E4B, 0x7C96BF, 0x0C9553, 0x0D4A48, /*1931-1940*/
		0x6DA53B, 0x0B554F, 0x056A45, 0x4AADB9, 0x025D4D, 0x092D42, 0x2C95B6, 0x0A954A, 0x7B4ABD, 0x06CA51, /*1941-1950*/
		0x0B5546, 0x555ABB, 0x04DA4E, 0x0A5B43, 0x352BB8, 0x052B4C, 0x8A953F, 0x0E9552, 0x06AA48, 0x6AD53C, /*1951-1960*/
		0x0AB54F, 0x04B645, 0x4A5739, 0x0A574D, 0x052642, 0x3E9335, 0x0D9549, 0x75AABE, 0x056A51, 0x096D46, /*1961-1970*/
		0x54AEBB, 0x04AD4F, 0x0A4D43, 0x4D26B7, 0x0D254B, 0x8D52BF, 0x0B5452, 0x0B6A47, 0x696D3C, 0x095B50, /*1971-1980*/
		0x049B45, 0x4A4BB9, 0x0A4B4D, 0xAB25C2, 0x06A554, 0x06D449, 0x6ADA3D, 0x0AB651, 0x093746, 0x5497BB, /*1981-1990*/
		0x04974F, 0x064B44, 0x36A537, 0x0EA54A, 0x86B2BF, 0x05AC53, 0x0AB647, 0x5936BC, 0x092E50, 0x0C9645, /*1991-2000*/
		0x4D4AB8, 0x0D4A4C, 0x0DA541, 0x25AAB6, 0x056A49, 0x7AADBD, 0x025D52, 0x092D47, 0x5C95BA, 0x0A954E, /*2001-2010*/
		0x0B4A43, 0x4B5537, 0x0AD54A, 0x955ABF, 0x04BA53, 0x0A5B48, 0x652BBC, 0x052B50, 0x0A9345, 0x474AB9, /*2011-2020*/
		0x06AA4C, 0x0AD541, 0x24DAB6, 0x04B64A, 0x69573D, 0x0A4E51, 0x0D2646, 0x5E933A, 0x0D534D, 0x05AA43, /*2021-2030*/
		0x36B537, 0x096D4B, 0xB4AEBF, 0x04AD53, 0x0A4D48, 0x6D25BC, 0x0D254F, 0x0D5244, 0x5DAA38, 0x0B5A4C, /*2031-2040*/
		0x056D41, 0x24ADB6, 0x049B4A, 0x7A4BBE, 0x0A4B51, 0x0AA546, 0x5B52BA, 0x06D24E, 0x0ADA42, 0x355B37, /*2041-2050*/
		0x09374B, 0x8497C1, 0x049753, 0x064B48, 0x66A53C, 0x0EA54F, 0x06B244, 0x4AB638, 0x0AAE4C, 0x092E42, /*2051-2060*/
		0x3C9735, 0x0C9649, 0x7D4ABD, 0x0D4A51, 0x0DA545, 0x55AABA, 0x056A4E, 0x0A6D43, 0x452EB7, 0x052D4B, /*2061-2070*/
		0x8A95BF, 0x0A9553, 0x0B4A47, 0x6B553B, 0x0AD54F, 0x055A45, 0x4A5D38, 0x0A5B4C, 0x052B42, 0x3A93B6, /*2071-2080*/
		0x069349, 0x7729BD, 0x06AA51, 0x0AD546, 0x54DABA, 0x04B64E, 0x0A5743, 0x452738, 0x0D264A, 0x8E933E, /*2081-2090*/
		0x0D5252, 0x0DAA47, 0x66B53B, 0x056D4F, 0x04AE45, 0x4A4EB9, 0x0A4D4C, 0x0D1541, 0x2D92B5			/*2091-2099*/
};
const int MonthAdd[12] = {0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334};
/*天干名称*/
const char *cTianGan[] = {
	"辛",
	"壬",
	"癸",
	"甲",
	"乙",
	"丙",
	"丁",
	"戊",
	"己",
	"庚",
};
/*地支名称*/
const char *cDiZhi[] = {
	"丑",
	"寅",
	"卯",
	"辰",
	"巳",
	"午",
	"未",
	"申",
	"酉",
	"戌",
	"亥",
	"子",
};
/*属相名称*/
const char *cShuXiang[] = {
	"牛",
	"虎",
	"兔",
	"龙",
	"蛇",
	"马",
	"羊",
	"猴",
	"鸡",
	"狗",
	"猪",
	"鼠",
};
/*农历日期名*/
const char *cDayName[] = {"*", "初一", "初二", "初三", "初四", "初五", "初六", "初七", "初八", "初九", "初十",
						  "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十", "廿一", "廿二", "廿三", "廿四", "廿五",
						  "廿六", "廿七", "廿八", "廿九", "三十"};
/*农历月份名*/
const char *cMonName[] = {"*", "正", "二", "三", "四", "五", "六", "七", "八", "九", "十", "冬", "腊"};
int GetDayOfLunar(int year, int month, int day, char *pStrLunar, int spkflag)
{
	char *pBak = pStrLunar;
	int Spring_NY, Sun_NY, StaticDayCount;
	int index, flag;
	// Spring_NY 记录春节离当年元旦的天数。
	// Sun_NY 记录阳历日离当年元旦的天数。
	if (((LunarCalendarTable[year - 1901] & 0x0060) >> 5) == 1)
		Spring_NY = (LunarCalendarTable[year - 1901] & 0x001F) - 1;
	else
		Spring_NY = (LunarCalendarTable[year - 1901] & 0x001F) - 1 + 31;
	Sun_NY = MonthAdd[month - 1] + day - 1;
	if ((!(year % 4)) && (month > 2))
		Sun_NY++;
	// StaticDayCount记录大小月的天数 29 或30
	// index 记录从哪个月开始来计算。
	// flag 是用来对闰月的特殊处理。
	// 判断阳历日在春节前还是春节后
	if (Sun_NY >= Spring_NY) // 阳历日在春节后（含春节那天）
	{
		Sun_NY -= Spring_NY;
		month = 1;
		index = 1;
		flag = 0;
		if ((LunarCalendarTable[year - 1901] & (0x80000 >> (index - 1))) == 0)
			StaticDayCount = 29;
		else
			StaticDayCount = 30;
		while (Sun_NY >= StaticDayCount)
		{
			Sun_NY -= StaticDayCount;
			index++;
			if (month == ((LunarCalendarTable[year - 1901] & 0xF00000) >> 20))
			{
				flag = ~flag;
				if (flag == 0)
					month++;
			}
			else
				month++;
			if ((LunarCalendarTable[year - 1901] & (0x80000 >> (index - 1))) == 0)
				StaticDayCount = 29;
			else
				StaticDayCount = 30;
		}
		day = Sun_NY + 1;
	}
	else // 阳历日在春节前
	{
		Spring_NY -= Sun_NY;
		year--;
		month = 12;
		if (((LunarCalendarTable[year - 1901] & 0xF00000) >> 20) == 0)
			index = 12;
		else
			index = 13;
		flag = 0;
		if ((LunarCalendarTable[year - 1901] & (0x80000 >> (index - 1))) == 0)
			StaticDayCount = 29;
		else
			StaticDayCount = 30;
		while (Spring_NY > StaticDayCount)
		{
			Spring_NY -= StaticDayCount;
			index--;
			if (flag == 0)
				month--;
			if (month == ((LunarCalendarTable[year - 1901] & 0xF00000) >> 20))
				flag = ~flag;
			if ((LunarCalendarTable[year - 1901] & (0x80000 >> (index - 1))) == 0)
				StaticDayCount = 29;
			else
				StaticDayCount = 30;
		}
		day = StaticDayCount - Spring_NY + 1;
	}
	if (month == ((LunarCalendarTable[year - 1901] & 0xF00000) >> 20))
		flag = 1;
	else
		flag = 0;

	year -= 1901;
	year %= 60;
#pragma warning(suppress : 4996)
	strcpy(pStrLunar, cTianGan[year % 10]);
	pStrLunar += 2;
#pragma warning(suppress : 4996)
	strcpy(pStrLunar, cDiZhi[year % 12]);
	pStrLunar += 2;
	*pStrLunar = '(';
	if (spkflag == 1)
		*pStrLunar = ' ';
	pStrLunar++;
#pragma warning(suppress : 4996)
	strcpy(pStrLunar, cShuXiang[year % 12]);
	pStrLunar += 2;
	*pStrLunar = ')';
	if (spkflag == 1)
		*pStrLunar = ' ';

	pStrLunar++;
#pragma warning(suppress : 4996)
	strcpy(pStrLunar, "年");
	pStrLunar += 2;
	if (flag == 1)
	{
#pragma warning(suppress : 4996)
		strcpy(pStrLunar, "闰");
		pStrLunar += 2;
	}
#pragma warning(suppress : 4996)
	strcpy(pStrLunar, cMonName[month]);
	pStrLunar += 2;
#pragma warning(suppress : 4996)
	strcat(pStrLunar, "月");
	pStrLunar += 2;
#pragma warning(suppress : 4996)
	strcat(pStrLunar, cDayName[day]);

	if ((day >= 21) && (day <= 29))
	{
		if (spkflag > 0)
		{
			*pStrLunar = '2';
			*(pStrLunar + 1) = '0';
		}
	}
	pStrLunar += 4;
	*pStrLunar = 0;
	return (int)(pStrLunar - pBak);
}

void StringToHexArray(char *pStr, char *pHex, int maxBN)
{
	int i = 0;
	while (maxBN--)
	{
		if (((*pStr) >= '0') && ((*pStr) <= '9'))
		{
			*pHex = (*pStr - '0') << 4;
		}
		else if (((*pStr) >= 'a') && ((*pStr) <= 'f'))
		{
			*pHex = (10 + (*pStr - 'a')) << 4;
		}
		else if (((*pStr) >= 'A') && ((*pStr) <= 'F'))
		{
			*pHex = (10 + (*pStr - 'A')) << 4;
		}
		else
			break;

		pStr++;

		if (((*pStr) >= '0') && ((*pStr) <= '9'))
		{
			*pHex |= (*pStr - '0');
		}
		else if (((*pStr) >= 'a') && ((*pStr) <= 'f'))
		{
			*pHex |= (10 + (*pStr - 'a'));
		}
		else if (((*pStr) >= 'A') && ((*pStr) <= 'F'))
		{
			*pHex |= (10 + (*pStr - 'A'));
		}
		else
			break;

		pStr++;
		pHex++;
	}
}

int IsLeapYear(int year)
{
	return ((0 == (year) % 4) && (0 != (year) % 100)) || (0 == (year) % 400);
	return 0;
}

const unsigned char MonthDays[2][12] =
	{{31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31},
	 {31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31}};

#define HOURSPERDAY 24
#define MINSPERHOUR 60
#define SECSPERMIN 60
#define MSECSPERSEC 1000
#define MINSPERDAY (HOURSPERDAY * MINSPERHOUR)
#define SECSPERDAY (MINSPERDAY * SECSPERMIN)
#define MSECSPERDAY (SECSPERDAY * MSECSPERSEC)
#define MSECSPERHOUR (MINSPERHOUR * SECSPERMIN * MSECSPERSEC)
#define MSECSPERMIN (SECSPERMIN * MSECSPERSEC)

#define DATEDELTA 693594
#define FTIME_AT2000 730485

#define YEAR_4 365 * 4 + 1
#define YEAR_100 YEAR_4 * 25 + 24
#define YEAR_400 YEAR_100 * 4 + 1

unsigned int GetCentrySecond(int wYear, int wMonth, int wDay, int wHour, int wMinute, int wSecond)
{

	char i, ifLeap;
	double Day = 0.0;
	int Year;
	ifLeap = IsLeapYear(wYear);

	if ((wYear >= 1) && (wYear < 9999) &&
		(wMonth >= 1) && (wMonth <= 12) &&
		(wDay >= 1) && (wDay <= MonthDays[ifLeap][wMonth - 1]) &&
		(wHour < 24) &&
		(wMinute < 60) &&
		(wSecond < 60))
	{
		Day = wDay;
		for (i = 1; i < wMonth; i++)
		{
			Day += MonthDays[ifLeap][i - 1];
		}
		Year = wYear - 1;
		Day += Year * 365 + Year / 4 - Year / 100 + Year / 400 - 719163; // - DATEDELTA;

		Day *= (HOURSPERDAY * MINSPERHOUR * SECSPERMIN);
		Day += (wHour * (MINSPERHOUR * SECSPERMIN) +
				wMinute * (SECSPERMIN) +
				wSecond);
		return Day;
	}
	return 0;
}

void CenturySecondToDateTime(unsigned int cs, int *y, int *m, int *d, int *hh, int *mm, int *ss)
{
}
