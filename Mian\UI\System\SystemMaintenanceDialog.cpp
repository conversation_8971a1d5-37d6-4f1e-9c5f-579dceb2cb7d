#include "SystemMaintenanceDialog.h"
#include "ui_SystemMaintenanceDialog.h"

SystemMaintenanceDialog::SystemMaintenanceDialog(QWidget* parent) : QDialog(parent),
ui(new Ui::SystemMaintenanceDialog)
{
    ui->setupUi(this);
    this->setWindowTitle("系统维修");
    this->setWindowFlags(this->windowFlags() | Qt::WindowMaximizeButtonHint);
    this->setWindowFlags(Qt::Window);
    ui->dateTimeEdit_time_start->setCalendarPopup(true);
    ui->dateTimeEdit_time_start->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_time_start->setDateTime(QDateTime::currentDateTime());

    ui->dateTimeEdit_time_end->setCalendarPopup(true);
    ui->dateTimeEdit_time_end->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_time_end->setDateTime(QDateTime::currentDateTime());

    ui->dateTimeEdit_delect_maintain_data->setCalendarPopup(true);
    ui->dateTimeEdit_delect_maintain_data->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_delect_maintain_data->setDateTime(QDateTime::currentDateTime());

    ui->dateTimeEdit_maintain_time->setCalendarPopup(true);
    ui->dateTimeEdit_maintain_time->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_maintain_time->setDateTime(QDateTime::currentDateTime());
    ui->stackedWidget->setCurrentIndex(0);
    set_SystemMaintenanceDialog_styleSheet(systemconfigfdata->systemdata.set_skin);
    SystemMaintenance_data = new sqlite_SystemMaintenance_data("SqliteData/"); // 创建系统维护日志记录表
    SystemMaintenance_data->use_CustomSqlTableModel(ui->tableView_device_maintain_statistics);
    // SystemMaintenance_data->use_CustomSqlTableModel(ui->tableView_device_maintain_recode);

    auto_delect_data = new QTimer();
    connect(auto_delect_data, SIGNAL(timeout()), this, SLOT(on_pushButton_delect_clicked()));

    auto_delect_data->start(1000);
}

SystemMaintenanceDialog::~SystemMaintenanceDialog()
{
    delete ui;
    delete SystemMaintenance_data;
}
void SystemMaintenanceDialog::on_pushButton_device_maintain_recode_clicked()
{

}

void SystemMaintenanceDialog::on_pushButton_device_maintain_statistics_clicked()
{
    ui->stackedWidget->setCurrentIndex(0);
}

void SystemMaintenanceDialog::set_SystemMaintenanceDialog_styleSheet(int index)
{

}

void SystemMaintenanceDialog::on_pushButton_add_clicked()
{
    QDateTime DateStr1 = ui->dateTimeEdit_maintain_time->dateTime();
    QString timeStr1 = DateStr1.toString("yyyy-MM-dd HH:mm:ss");
    SystemMaintenance_SQ_GROUP data;
    data.time = "beijing";
    data.device = ui->lineEdit_maintain_part->text();
    data.personnel = ui->lineEdit_maintain_people->text();
    data.u_time = timeStr1;
    data.name = ui->lineEdit_device_name->text();
    data.model = ui->lineEdit_device_type->text();
    data.manufacturer = ui->lineEdit_production_vender->text();
    data.remark = ui->textEdit_other_remark->toPlainText();
    SystemMaintenance_data->insertCountLimit(data);
}

void SystemMaintenanceDialog::on_pushButton_find_clicked()
{
    QDateTime DateStr1 = ui->dateTimeEdit_time_start->dateTime();
    QString timeStr1 = DateStr1.toString("yyyy-MM-dd HH:mm:ss");
    QDateTime DateStr2 = ui->dateTimeEdit_time_end->dateTime();
    QString timeStr2 = DateStr2.toString("yyyy-MM-dd HH:mm:ss");
    QDateTime DateStr3 = ui->dateTimeEdit_maintain_time->dateTime();
    QString timeStr3 = DateStr3.toString("yyyy-MM-dd HH:mm:ss");

    SystemMaintenance_data->select_usetimeAndtype_SqlTableModel(timeStr1, timeStr2, ui->lineEdit_maintain->text());
}

void SystemMaintenanceDialog::on_pushButton_delect_clicked()
{
    QDateTime DateStr1 = ui->dateTimeEdit_delect_maintain_data->dateTime();
    QString timeStr1 = DateStr1.toString("yyyy-MM-dd HH:mm:ss");
    SystemMaintenance_data->deleteOldsql(timeStr1);
}
