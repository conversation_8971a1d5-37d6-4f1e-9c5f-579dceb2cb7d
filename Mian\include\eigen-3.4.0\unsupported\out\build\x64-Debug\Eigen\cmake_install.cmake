# Install script for directory: E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/out/install/x64-Debug")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REG<PERSON> REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Debug")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xDevelx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/unsupported/Eigen/AdolcForward;/unsupported/Eigen/AlignedVector3;/unsupported/Eigen/ArpackSupport;/unsupported/Eigen/AutoDiff;/unsupported/Eigen/BVH;/unsupported/Eigen/EulerAngles;/unsupported/Eigen/FFT;/unsupported/Eigen/IterativeSolvers;/unsupported/Eigen/KroneckerProduct;/unsupported/Eigen/LevenbergMarquardt;/unsupported/Eigen/MatrixFunctions;/unsupported/Eigen/MoreVectorization;/unsupported/Eigen/MPRealSupport;/unsupported/Eigen/NonLinearOptimization;/unsupported/Eigen/NumericalDiff;/unsupported/Eigen/OpenGLSupport;/unsupported/Eigen/Polynomials;/unsupported/Eigen/Skyline;/unsupported/Eigen/SparseExtra;/unsupported/Eigen/SpecialFunctions;/unsupported/Eigen/Splines")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/unsupported/Eigen" TYPE FILE FILES
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/AdolcForward"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/AlignedVector3"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/ArpackSupport"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/AutoDiff"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/BVH"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/EulerAngles"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/FFT"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/IterativeSolvers"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/KroneckerProduct"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/LevenbergMarquardt"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/MatrixFunctions"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/MoreVectorization"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/MPRealSupport"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/NonLinearOptimization"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/NumericalDiff"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/OpenGLSupport"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/Polynomials"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/Skyline"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/SparseExtra"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/SpecialFunctions"
    "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/Splines"
    )
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xDevelx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/unsupported/Eigen/src")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/unsupported/Eigen" TYPE DIRECTORY FILES "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/Eigen/src" FILES_MATCHING REGEX "/[^/]*\\.h$")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for each subdirectory.
  include("E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/out/build/x64-Debug/Eigen/CXX11/cmake_install.cmake")

endif()

