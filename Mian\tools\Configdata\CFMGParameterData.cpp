﻿#include "CFMGParameterData.h"
#include <QDir>
#include <QDebug>
CFMGParameterData::CFMGParameterData(QObject *parent) : QObject(parent)
{
    loadFromFile();
    creat_configfile();
}

CFMGParameterData::~CFMGParameterData()
{
}

void CFMGParameterData::saveToFile()
{

    QSettings settings("Dataconfig/CFMGParameterData.ini", QSettings::IniFormat);
    // 参数设置
    settings.setValue("FMGParatemerData/level1", fmgdata.level1);
    settings.setValue("FMGParatemerData/level2", fmgdata.level2);
    settings.setValue("FMGParatemerData/mast_angle", fmgdata.mast_angle);
    settings.setValue("FMGParatemerData/mast_ratio", fmgdata.mast_ratio);
    settings.setValue("FMGParatemerData/mast_speed", fmgdata.mast_speed);
    settings.setValue("FMGParatemerData/mast_angle_compensate", fmgdata.mast_angle_compensate);
    settings.setValue("FMGParatemerData/mast_target_angle", fmgdata.mast_target_angle);
    settings.setValue("FMGParatemerData/wind_speed", fmgdata.wind_speed);
    settings.setValue("FMGParatemerData/wind_pw", fmgdata.wind_pw);
    settings.setValue("FMGParatemerData/agreement_choice", fmgdata.agreement_choice);
    settings.setValue("FMGParatemerData/fanye_path", fmgdata.fanye_path);
    settings.setValue("FMGParatemerData/look_path", fmgdata.look_path);

    settings.setValue("FMGParatemerData/data_filter", fmgdata.data_filter);
    settings.setValue("FMGParatemerData/calculate_filter", fmgdata.calculate_filter);
    settings.setValue("FMGParatemerData/normal_temp_angle", fmgdata.normal_temp_angle);
    settings.setValue("FMGParatemerData/noise_angle", fmgdata.noise_angle);
    settings.setValue("FMGParatemerData/sky_angle", fmgdata.sky_angle);
    settings.setValue("FMGParatemerData/stop_time", fmgdata.stop_time);
    settings.setValue("FMGParatemerData/interval_time", fmgdata.interval_time);
    settings.setValue("FMGParatemerData/calibration_Mode", fmgdata.calibration_Mode);

    settings.setValue("FMGParatemerData/var_data", fmgdata.var_data);
    settings.setValue("FMGParatemerData/num_limit", fmgdata.num_limit);
    settings.setValue("FMGParatemerData/time_limit", fmgdata.time_limit);
    
    

    // 采集温控
    settings.setValue("FMGParatemerData/parameter_p1", fmgdata.parameter_p1);
    settings.setValue("FMGParatemerData/parameter_I1", fmgdata.parameter_I1);
    settings.setValue("FMGParatemerData/parameter_D1", fmgdata.parameter_D1);
    settings.setValue("FMGParatemerData/target_temp1", fmgdata.target_temp1);
    settings.setValue("FMGParatemerData/current_temp1", fmgdata.current_temp1);
    settings.setValue("FMGParatemerData/current_pw1", fmgdata.current_pw1);
    settings.setValue("FMGParatemerData/parameter_p2", fmgdata.parameter_p2);
    settings.setValue("FMGParatemerData/parameter_I2", fmgdata.parameter_I2);
    settings.setValue("FMGParatemerData/parameter_D2", fmgdata.parameter_D2);
    settings.setValue("FMGParatemerData/target_temp2", fmgdata.target_temp2);
    settings.setValue("FMGParatemerData/current_temp2", fmgdata.current_temp2);
    settings.setValue("FMGParatemerData/current_pw2", fmgdata.current_pw2);
}

void CFMGParameterData::loadFromFile()
{
    QString filePath = "F:/QtMicrowaveRadiation/ControlSetParameData.ini";
    QSettings settings("Dataconfig/CFMGParameterData.ini", QSettings::IniFormat);
    fmgdata.level1 = settings.value("FMGParatemerData/level1").toString();
    fmgdata.level2 = settings.value("FMGParatemerData/level2").toString();
    fmgdata.mast_angle = settings.value("FMGParatemerData/mast_angle").toString();
    fmgdata.mast_ratio = settings.value("FMGParatemerData/mast_ratio").toString();
    fmgdata.mast_speed = settings.value("FMGParatemerData/mast_speed").toString();
    fmgdata.mast_angle_compensate = settings.value("FMGParatemerData/mast_angle_compensate").toString();
    fmgdata.mast_target_angle = settings.value("FMGParatemerData/mast_target_angle").toDouble();
    fmgdata.wind_speed = settings.value("FMGParatemerData/wind_speed").toString();
    fmgdata.wind_pw = settings.value("FMGParatemerData/wind_pw").toDouble();
    fmgdata.agreement_choice = settings.value("FMGParatemerData/agreement_choice").toInt();
    fmgdata.fanye_path = settings.value("FMGParatemerData/fanye_path").toString();
    fmgdata.look_path = settings.value("FMGParatemerData/look_path").toString();

    fmgdata.data_filter = settings.value("FMGParatemerData/data_filter").toDouble();
    fmgdata.calculate_filter = settings.value("FMGParatemerData/calculate_filter").toDouble();
    fmgdata.normal_temp_angle = settings.value("FMGParatemerData/normal_temp_angle").toDouble();
    fmgdata.noise_angle = settings.value("FMGParatemerData/noise_angle").toDouble();
    fmgdata.sky_angle = settings.value("FMGParatemerData/sky_angle").toDouble();
    fmgdata.stop_time = settings.value("FMGParatemerData/stop_time").toDouble();
    fmgdata.interval_time = settings.value("FMGParatemerData/interval_time").toDouble();
    
    fmgdata.calibration_Mode=settings.value("FMGParatemerData/calibration_Mode").toInt();

    fmgdata.var_data = settings.value("FMGParatemerData/var_data").toDouble();
    fmgdata.num_limit = settings.value("FMGParatemerData/num_limit").toDouble();
    fmgdata.time_limit = settings.value("FMGParatemerData/time_limit").toDouble();

    fmgdata.parameter_p1 = settings.value("FMGParatemerData/parameter_p1").toDouble();
    fmgdata.parameter_I1 = settings.value("FMGParatemerData/parameter_I1").toDouble();
    fmgdata.parameter_D1 = settings.value("FMGParatemerData/parameter_D1").toDouble();
    fmgdata.target_temp1 = settings.value("FMGParatemerData/target_temp1").toDouble();
    fmgdata.current_temp1 = settings.value("FMGParatemerData/current_temp1").toString();
    fmgdata.current_pw1 = settings.value("FMGParatemerData/current_pw1").toString();
    fmgdata.parameter_p2 = settings.value("FMGParatemerData/parameter_p2").toDouble();
    fmgdata.parameter_I2 = settings.value("FMGParatemerData/parameter_I2").toDouble();
    fmgdata.parameter_D2 = settings.value("FMGParatemerData/parameter_D2").toDouble();
    fmgdata.target_temp2 = settings.value("FMGParatemerData/target_temp2").toDouble();
    fmgdata.current_temp2 = settings.value("FMGParatemerData/current_temp2").toString();
    fmgdata.current_pw2 = settings.value("FMGParatemerData/current_pw2").toString();
}

void CFMGParameterData::creat_configfile()
{
    QDir currentDir = QDir::current();
    // 创建目录（相对路径）
    QString dirpath = "Dataconfig";
    if (!currentDir.exists(dirpath))
    {
        if (!currentDir.mkdir(dirpath))
        {
            qDebug() << "创建目录失败";
            return;
        }
        qDebug() << "创建目录成功";
    }
    // 构建文件路径（确保目录存在后在操作）
    QFileInfo fileInfo(currentDir, "Dataconfig/CFMGParameterData.ini");
    // QString filePath = currentDir.filePath("Dataconfig/CFMGParameterData.ini");
    QString filePath = fileInfo.absoluteFilePath();
    // 检查文件失败
    if (fileInfo.exists())
    {
        qDebug() << "文件已存在，无需再创建";
        return;
    }
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly))
    {
        file.close();
        qDebug() << "创建成功";
    }
    else
    {
        qDebug() << "创建失败";
    }
}
