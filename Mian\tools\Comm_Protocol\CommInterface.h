﻿#ifndef COMMINTERFACE_H
#define COMMINTERFACE_H

#include "SerialInterface.h"
#include "TcpClientInterface.h"
#include "UdpInterface.h"
#include <QObject>

/**
 * @brief 统一通信接口类
 * 封装串口和TCP客户端通信，提供统一接口
 */
class CommInterface : public QObject
{
    Q_OBJECT

public:
    enum InterfaceType
    {
        SERIAL,
        TCP_CLIENT,
        UDP_CLIENT
    };

    /**
     * @brief 构造函数
     * @param type 接口类型(SERIAL或TCP_CLIENT)
     * @param parent 父对象指针
     */
    explicit CommInterface(InterfaceType type, QObject *parent = nullptr);

    /**
     * @brief 接口类型切换
     * @param type 接口类型(SERIAL或TCP_CLIENT)
     * @return 成功返回true
     */
    bool InterfaceTypeToggle(InterfaceType type);

    /**
     * @brief 打开连接
     * @param type 连接参数(串口名或TCP服务器地址)
     * @param port 端口号(串口波特率或TCP端口)
     * @return 成功返回true
     */
    bool open(const QString &args, quint16 port);

    /**
     * @brief 设置串口波特率
     * @param baudRate 波特率
     * @return 成功返回true
     */
    bool setBaudRate(quint32 baudRate);

    /**
     * @brief 关闭连接
     */
    void close();

    /**
     * @brief 发送数据
     * @param data 要发送的数据
     * @return 成功发送的字节数
     */
    qint64 send(const QByteArray &data);

    /**
     * @brief 检查是否已连接
     */
    bool isConnected() const;

signals:
    /**
     * @brief 数据接收信号
     */
    void dataReceived(const QByteArray &data);

    /**
     * @brief 错误信号
     */
    void errorOccurred(const QString &error);

private slots:
    void handleSerialData(const QByteArray &data);
    void handleTcpData(const QByteArray &data);
    void handleSerialError(const QString &error);
    void handleTcpError(const QString &error);
    void handleUdpData(const QByteArray &data);
    void handleUdpError(const QString &error);

private:
    InterfaceType m_type;
    SerialInterface *m_serial;
    TcpClientInterface *m_tcpClient;
    UdpInterface *m_udp;
};

#endif // COMMINTERFACE_H
