﻿#include "FMGWidget.h"
#include "ui_FMGWidget.h"
#include "tools/XC_DataHanding/xcSysVar.h"
#include <QDateTime>
#include <QTime>
#include "tools/Global.h"
#include <QtConcurrent/QtConcurrent>
#include <QFile>
#include <QTextStream>
#include "tools/BrightCalcClass.h"
FMGWidget::FMGWidget(QWidget* parent) : QWidget(parent),
ui(new Ui::FMGWidget)
{
	ui->setupUi(this);
	this->setWindowTitle("基础数据");
	//鼠标游标显示
   // k通道电压
	tracer_k_voltage = new QCPItemTracer(ui->widget_k_voltage);
	tracerLabe_k_voltage = new QCPItemText(ui->widget_k_voltage); // 生成游标说明
	set_tracer(ui->widget_k_voltage, tracer_k_voltage, tracerLabe_k_voltage);

	//k通道温度
	tracer_k_temp = new QCPItemTracer(ui->widget_k_temp);
	tracerLabe_k_temp = new QCPItemText(ui->widget_k_temp); // 生成游标说明
	set_tracer(ui->widget_k_temp, tracer_k_temp, tracerLabe_k_temp);

	//v通道电压
	tracer_v_voltage = new QCPItemTracer(ui->widget_v_voltage);
	tracerLabe_v_voltage = new QCPItemText(ui->widget_v_voltage); // 生成游标说明
	set_tracer(ui->widget_v_voltage, tracer_v_voltage, tracerLabe_v_voltage);
	//v通温度
	tracer_v_temp = new QCPItemTracer(ui->widget_v_temp);
	tracerLabe_v_temp = new QCPItemText(ui->widget_v_temp); // 生成游标说明
	set_tracer(ui->widget_v_temp, tracer_v_temp, tracerLabe_v_temp);

	//气象六要素
	tracer_humidity = new QCPItemTracer(ui->widget_humidity);
	tracerLabe_humidity = new QCPItemText(ui->widget_humidity); // 生成游标说明
	set_tracer(ui->widget_humidity, tracer_humidity, tracerLabe_humidity);
	tracer_pressure = new QCPItemTracer(ui->widget_pressure);
	tracerLabe_pressure = new QCPItemText(ui->widget_pressure); // 生成游标说明
	set_tracer(ui->widget_pressure, tracer_pressure, tracerLabe_pressure);
	tracer_rain = new QCPItemTracer(ui->widget_rain);
	tracerLabe_rain = new QCPItemText(ui->widget_rain); // 生成游标说明
	set_tracer(ui->widget_rain, tracer_rain, tracerLabe_rain);
	tracer_temp = new QCPItemTracer(ui->widget_temp);
	tracerLabe_temp = new QCPItemText(ui->widget_temp); // 生成游标说明
	set_tracer(ui->widget_temp, tracer_temp, tracerLabe_temp);
	tracer_wind_direction = new QCPItemTracer(ui->widget_wind_direction);
	tracerLabe_wind_direction = new QCPItemText(ui->widget_wind_direction); // 生成游标说明
	set_tracer(ui->widget_wind_direction, tracer_wind_direction, tracerLabe_wind_direction);
	tracer_wind_speed = new QCPItemTracer(ui->widget_wind_speed);
	tracerLabe_wind_speed = new QCPItemText(ui->widget_wind_speed); // 生成游标说明
	set_tracer(ui->widget_wind_speed, tracer_wind_speed, tracerLabe_wind_speed);

	//K通道亮温
	tracer_k_light_temp = new QCPItemTracer(ui->widget_k_light_temp);
	tracerLabe_k_light_temp = new QCPItemText(ui->widget_k_light_temp); // 生成游标说明
	set_tracer(ui->widget_k_light_temp, tracer_k_light_temp, tracerLabe_k_light_temp);

	//v通道亮温
	tracer_v_light_temp = new QCPItemTracer(ui->widget_v_light_temp);
	tracerLabe_v_light_temp = new QCPItemText(ui->widget_v_light_temp); // 生成游标说明
	set_tracer(ui->widget_v_light_temp, tracer_v_light_temp, tracerLabe_v_light_temp);

	//降雨量
	tracer_light_rain = new QCPItemTracer(ui->widget_light_rain);
	tracerLabe_light_rain = new QCPItemText(ui->widget_light_rain); // 生成游标说明
	set_tracer(ui->widget_light_rain, tracer_light_rain, tracerLabe_light_rain);

	//常温源
	tracer_light_temp = new QCPItemTracer(ui->widget_light_temp);
	tracerLabe_light_temp = new QCPItemText(ui->widget_light_temp); // 生成游标说明
	set_tracer(ui->widget_light_temp, tracer_light_temp, tracerLabe_light_temp);


	ui->widget_k_voltage->installEventFilter(this);
	ui->widget_k_temp->installEventFilter(this);
	ui->widget_v_voltage->installEventFilter(this);
	ui->widget_v_temp->installEventFilter(this);

	ui->widget_humidity->installEventFilter(this);
	ui->widget_pressure->installEventFilter(this);
	ui->widget_rain->installEventFilter(this);
	ui->widget_temp->installEventFilter(this);
	ui->widget_wind_direction->installEventFilter(this);
	ui->widget_wind_speed->installEventFilter(this);

	ui->widget_k_light_temp->installEventFilter(this);
	ui->widget_v_light_temp->installEventFilter(this);

	ui->widget_light_rain->installEventFilter(this);
	ui->widget_light_temp->installEventFilter(this);

	FMG_network = new classWidgetComm();
	FMG_network = widgetComm;
	widgetComm = FMG_network;
	
	data_k = new CxcDataExchange();
	data_weather_six = new CxcDataExchange();
	original_data = new CxcDataExchange();
	light_data = new CxcDataExchange();

	init_graph();

	ui->splitter->setStretchFactor(4, 1);
	ui->splitter_2->setStretchFactor(4, 1);

	ui->splitter_3->setStretchFactor(4, 1);

	buttonGroup = new QButtonGroup(this);
	buttonGroup->setExclusive(true); // 设置互斥

	buttonGroup->addButton(ui->pushButton_weather_six);
	buttonGroup->addButton(ui->pushButton_k_voltage);
	buttonGroup->addButton(ui->pushButton_v_voltage);
	buttonGroup->addButton(ui->pushButton_k_temp);
	buttonGroup->addButton(ui->pushButton_v_temp);

	buttonGroup->addButton(ui->pushButton_all_temp_voltage);
	buttonGroup->addButton(ui->pushButton_k_light);
	buttonGroup->addButton(ui->pushButton_v_light);

	buttonGroup->addButton(ui->pushButton_rain);
	buttonGroup->addButton(ui->pushButton_normal_temp);
	buttonGroup->addButton(ui->pushButton_all_light);


	foreach(QAbstractButton * btn, buttonGroup->buttons())
	{
		btn->setCheckable(true);
		btn->setFocusPolicy(Qt::NoFocus);
		btn->installEventFilter(this);
	}

	ui->stackedWidget_FMG->setCurrentIndex(0);

	ui->pushButton_weather_six->setChecked(true);
	// 气象六要素
	connect(ui->pushButton_weather_six, &QPushButton::clicked, [=](bool checked)
		{
			if (checked)
			{
				ui->stackedWidget_FMG->setCurrentIndex(0);
			} 
		});
	// 原始数据
	connect(ui->pushButton_k_voltage, &QPushButton::clicked, [=](bool checked)
		{
			if (checked)
			{
				ui->stackedWidget_FMG->setCurrentIndex(1);
				ui->widget_k_voltage->show();
				ui->widget_k_temp->hide();
				ui->widget_v_temp->hide();
				ui->widget_v_voltage->hide();
				ui->widget_k_cebian_hz->show();
				ui->widget_v_cebian_hz->hide();
				ui->widget_k_cebian_temp->hide();
				ui->widget_v_cebian_temp->hide();
			} 
		});
	connect(ui->pushButton_v_voltage, &QPushButton::clicked, [=](bool checked)
		{
			if (checked)
			{
				ui->stackedWidget_FMG->setCurrentIndex(1);
				ui->widget_k_voltage->hide();
				ui->widget_k_temp->hide();
				ui->widget_v_temp->hide();
				ui->widget_v_voltage->show();
				ui->widget_k_cebian_hz->hide();
				ui->widget_v_cebian_hz->show();
				ui->widget_k_cebian_temp->hide();
				ui->widget_v_cebian_temp->hide();
			} 
		});
	connect(ui->pushButton_k_temp, &QPushButton::clicked, [=](bool checked)
		{
			if (checked)
			{
				ui->stackedWidget_FMG->setCurrentIndex(1);
				ui->widget_k_voltage->hide();
				ui->widget_k_temp->show();
				ui->widget_v_temp->hide();
				ui->widget_v_voltage->hide();
				ui->widget_k_cebian_hz->hide();
				ui->widget_v_cebian_hz->hide();
				ui->widget_k_cebian_temp->show();
				ui->widget_v_cebian_temp->hide();
			} 
		});
	connect(ui->pushButton_v_temp, &QPushButton::clicked, [=](bool checked)
		{
			if (checked)
			{
				ui->stackedWidget_FMG->setCurrentIndex(1);
				ui->widget_k_voltage->hide();
				ui->widget_k_temp->hide();
				ui->widget_v_temp->show();
				ui->widget_v_voltage->hide();
				ui->widget_k_cebian_hz->hide();
				ui->widget_v_cebian_hz->hide();
				ui->widget_k_cebian_temp->hide();
				ui->widget_v_cebian_temp->show();
			} 
		});

	connect(ui->pushButton_all_temp_voltage, &QPushButton::clicked, [=](bool checked)
		{
			if (checked)
			{
				ui->stackedWidget_FMG->setCurrentIndex(1);
				ui->widget_k_voltage->show();
				ui->widget_k_temp->show();
				ui->widget_v_temp->show();
				ui->widget_v_voltage->show();
				ui->widget_k_cebian_hz->show();
				ui->widget_v_cebian_hz->show();
				ui->widget_k_cebian_temp->show();
				ui->widget_v_cebian_temp->show();
			} 
		});
	// 亮温数据

	connect(ui->pushButton_k_light, &QPushButton::clicked, [=](bool checked)
		{
			if (checked)
			{
				ui->stackedWidget_FMG->setCurrentIndex(2);
				ui->widget_k_light_temp->show();
				ui->widget_v_light_temp->hide();
				ui->widget_light_rain->hide();
				ui->widget_light_temp->hide();
				ui->widget_light_k_cebian_hz->show();
				ui->widget_light_v_cebian_hz->hide();
				ui->widget_light_cebian_rain->hide();
				ui->widget_light_cebian_temp->hide();
			} 
		});
	connect(ui->pushButton_v_light, &QPushButton::clicked, [=](bool checked)
		{
			if (checked)
			{
				ui->stackedWidget_FMG->setCurrentIndex(2);
				ui->widget_k_light_temp->hide();
				ui->widget_v_light_temp->show();
				ui->widget_light_rain->hide();
				ui->widget_light_temp->hide();
				ui->widget_light_k_cebian_hz->hide();
				ui->widget_light_v_cebian_hz->show();
				ui->widget_light_cebian_rain->hide();
				ui->widget_light_cebian_temp->hide();
			} 
		});

	connect(ui->pushButton_rain, &QPushButton::clicked, [=](bool checked)
		{
			if (checked)
			{
				ui->stackedWidget_FMG->setCurrentIndex(2);
				ui->widget_k_light_temp->hide();
				ui->widget_v_light_temp->hide();
				ui->widget_light_rain->show();
				ui->widget_light_temp->hide();
				ui->widget_light_k_cebian_hz->hide();
				ui->widget_light_v_cebian_hz->hide();
				ui->widget_light_cebian_rain->show();
				ui->widget_light_cebian_temp->hide();
			} 
		});
	connect(ui->pushButton_normal_temp, &QPushButton::clicked, [=](bool checked)
		{
			if (checked)
			{
				ui->stackedWidget_FMG->setCurrentIndex(2);
				ui->widget_k_light_temp->hide();
				ui->widget_v_light_temp->hide();
				ui->widget_light_rain->hide();
				ui->widget_light_temp->show();
				ui->widget_light_k_cebian_hz->hide();
				ui->widget_light_v_cebian_hz->hide();
				ui->widget_light_cebian_rain->hide();
				ui->widget_light_cebian_temp->show();
			} 
		});

	connect(ui->pushButton_all_light, &QPushButton::clicked, [=](bool checked)
		{
			if (checked)
			{
				ui->stackedWidget_FMG->setCurrentIndex(2);
				ui->widget_k_light_temp->show();
				ui->widget_v_light_temp->show();
				ui->widget_light_rain->show();
				ui->widget_light_temp->show();
				ui->widget_light_k_cebian_hz->show();
				ui->widget_light_v_cebian_hz->show();
				ui->widget_light_cebian_rain->show();
				ui->widget_light_cebian_temp->show();
			} 
		});


	ui->widget_kv_data->hide();
	ui->widget_v->hide();
	ui->widget_k->hide();
	connect(ui->pushButton_k_show_data, &QPushButton::clicked, [=]()
		{

			if (!k_data_widget)
			{
				ui->widget_kv_data->show();
				ui->widget_k->show();
				ui->widget_v->hide();
				k_data_widget = true;
			}
			else
			{
				ui->widget_kv_data->hide();

				k_data_widget = false;
			}

		});

	connect(ui->pushButton_v_show_data, &QPushButton::clicked, [=]()
		{
			if (!v_data_widget)
			{
				ui->widget_kv_data->show();
				ui->widget_v->show();
				ui->widget_k->hide();
				v_data_widget = true;
			}
			else
			{
				ui->widget_kv_data->hide();
				v_data_widget = false;
			}
		});



	// 创建模型和视图
	QVector<QString> data;
	data.append("时间");
	data.append("通道1");
	data.append("通道2");
	data.append("通道3");
	data.append("通道4");
	data.append("通道5");
	data.append("通道6");
	data.append("通道7");
	data.append("通道8");


	QVector<QString> data_ca;
	data_ca.append("定标数据类型");
	data_ca.append("通道1");
	data_ca.append("通道2");
	data_ca.append("通道3");
	data_ca.append("通道4");
	data_ca.append("通道5");
	data_ca.append("通道6");
	data_ca.append("通道7");
	data_ca.append("通道8");

	model_k = new LargeTableModel(data);
	model_v = new LargeTableModel(data);
	model_ca1 = new LargeTableModel(data_ca);
	model_ca2 = new LargeTableModel(data_ca);

	ui->tableView_k->setModel(model_k);
	ui->tableView_v->setModel(model_v);
	ui->tableView_k_calibration->setModel(model_ca1);
	ui->tableView_v_calibration->setModel(model_ca2);



	// 性能优化设置
	ui->tableView_k->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
	ui->tableView_k->verticalHeader()->setDefaultSectionSize(25);  // 固定行高
	ui->tableView_k->setVerticalScrollMode(QAbstractItemView::ScrollPerPixel);  // 像素级滚动
	ui->tableView_k->setEditTriggers(QAbstractItemView::NoEditTriggers);  // 禁用编辑

	ui->tableView_v->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
	ui->tableView_v->verticalHeader()->setDefaultSectionSize(25);  // 固定行高
	ui->tableView_v->setVerticalScrollMode(QAbstractItemView::ScrollPerPixel);  // 像素级滚动
	ui->tableView_v->setEditTriggers(QAbstractItemView::NoEditTriggers);  // 禁用编辑

	ui->tableView_k_calibration->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
	ui->tableView_k_calibration->verticalHeader()->setDefaultSectionSize(25);  // 固定行高
	ui->tableView_k_calibration->setVerticalScrollMode(QAbstractItemView::ScrollPerPixel);  // 像素级滚动
	ui->tableView_k_calibration->setEditTriggers(QAbstractItemView::NoEditTriggers);  // 禁用编辑

	ui->tableView_v_calibration->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
	ui->tableView_v_calibration->verticalHeader()->setDefaultSectionSize(25);  // 固定行高
	ui->tableView_v_calibration->setVerticalScrollMode(QAbstractItemView::ScrollPerPixel);  // 像素级滚动
	ui->tableView_v_calibration->setEditTriggers(QAbstractItemView::NoEditTriggers);  // 禁用编辑



	connect(ui->tableView_k->horizontalHeader(), &QHeaderView::geometriesChanged, [&]() {
		if (ui->tableView_k == nullptr)
			return;
		int totalwidth = ui->tableView_k->viewport()->width();
		ui->tableView_k->setColumnWidth(0, (totalwidth / 9));
		ui->tableView_k->setColumnWidth(1, (totalwidth / 9));
		ui->tableView_k->setColumnWidth(2, (totalwidth / 9));
		ui->tableView_k->setColumnWidth(3, (totalwidth / 9));
		ui->tableView_k->setColumnWidth(4, (totalwidth / 9));
		ui->tableView_k->setColumnWidth(5, (totalwidth / 9));
		ui->tableView_k->setColumnWidth(6, (totalwidth / 9));
		ui->tableView_k->setColumnWidth(7, (totalwidth / 9));
		ui->tableView_k->setColumnWidth(8, (totalwidth / 9));


		});

	connect(ui->tableView_v->horizontalHeader(), &QHeaderView::geometriesChanged, [&]() {
		if (ui->tableView_v == nullptr)
			return;
		int totalwidth = ui->tableView_v->viewport()->width();
		ui->tableView_v->setColumnWidth(0, (totalwidth / 9));
		ui->tableView_v->setColumnWidth(1, (totalwidth / 9));
		ui->tableView_v->setColumnWidth(2, (totalwidth / 9));
		ui->tableView_v->setColumnWidth(3, (totalwidth / 9));
		ui->tableView_v->setColumnWidth(4, (totalwidth / 9));
		ui->tableView_v->setColumnWidth(5, (totalwidth / 9));
		ui->tableView_v->setColumnWidth(6, (totalwidth / 9));
		ui->tableView_v->setColumnWidth(7, (totalwidth / 9));
		ui->tableView_v->setColumnWidth(8, (totalwidth / 9));
		});

	connect(ui->tableView_k_calibration->horizontalHeader(), &QHeaderView::geometriesChanged, [&]() {
		if (ui->tableView_k_calibration == nullptr)
			return;
		int totalwidth = ui->tableView_k_calibration->viewport()->width();
		ui->tableView_k_calibration->setColumnWidth(0, (totalwidth / 9));
		ui->tableView_k_calibration->setColumnWidth(1, (totalwidth / 9));
		ui->tableView_k_calibration->setColumnWidth(2, (totalwidth / 9));
		ui->tableView_k_calibration->setColumnWidth(3, (totalwidth / 9));
		ui->tableView_k_calibration->setColumnWidth(4, (totalwidth / 9));
		ui->tableView_k_calibration->setColumnWidth(5, (totalwidth / 9));
		ui->tableView_k_calibration->setColumnWidth(6, (totalwidth / 9));
		ui->tableView_k_calibration->setColumnWidth(7, (totalwidth / 9));
		ui->tableView_k_calibration->setColumnWidth(8, (totalwidth / 9));
		});


	connect(ui->tableView_v_calibration->horizontalHeader(), &QHeaderView::geometriesChanged, [&]() {
		if (ui->tableView_v_calibration == nullptr)
			return;
		int totalwidth = ui->tableView_v_calibration->viewport()->width();
		ui->tableView_v_calibration->setColumnWidth(0, (totalwidth / 9));
		ui->tableView_v_calibration->setColumnWidth(1, (totalwidth / 9));
		ui->tableView_v_calibration->setColumnWidth(2, (totalwidth / 9));
		ui->tableView_v_calibration->setColumnWidth(3, (totalwidth / 9));
		ui->tableView_v_calibration->setColumnWidth(4, (totalwidth / 9));
		ui->tableView_v_calibration->setColumnWidth(5, (totalwidth / 9));
		ui->tableView_v_calibration->setColumnWidth(6, (totalwidth / 9));
		ui->tableView_v_calibration->setColumnWidth(7, (totalwidth / 9));
		ui->tableView_v_calibration->setColumnWidth(8, (totalwidth / 9));

		});
	//绘图侧边按键 按钮样式表设置
	set_pushButton_style(ui->pushButton_k_2224, false);
	set_pushButton_style(ui->pushButton_k_2304, false);
	set_pushButton_style(ui->pushButton_k_2384, false);
	set_pushButton_style(ui->pushButton_k_2544, false);
	set_pushButton_style(ui->pushButton_k_2624, false);
	set_pushButton_style(ui->pushButton_k_2784, false);
	set_pushButton_style(ui->pushButton_k_3000, false);
	set_pushButton_style(ui->pushButton_k_3140, false);

	set_pushButton_style(ui->pushButton_v_5126, false);
	set_pushButton_style(ui->pushButton_v_5228, false);
	set_pushButton_style(ui->pushButton_v_5386, false);
	set_pushButton_style(ui->pushButton_v_5494, false);
	set_pushButton_style(ui->pushButton_v_5550, false);
	set_pushButton_style(ui->pushButton_v_5666, false);
	set_pushButton_style(ui->pushButton_v_5730, false);
	set_pushButton_style(ui->pushButton_v_5800, false);


	set_pushButton_style(ui->pushButton_k_temp1, false);
	set_pushButton_style(ui->pushButton_k_temp2, false);
	set_pushButton_style(ui->pushButton_k_temp3, false);
	set_pushButton_style(ui->pushButton_v_temp1, false);
	set_pushButton_style(ui->pushButton_v_temp2, false);
	set_pushButton_style(ui->pushButton_v_temp3, false);


	set_pushButton_style(ui->pushButton_k_light_2224, false);
	set_pushButton_style(ui->pushButton_k_light_2304, false);
	set_pushButton_style(ui->pushButton_k_light_2384, false);
	set_pushButton_style(ui->pushButton_k_light_2544, false);
	set_pushButton_style(ui->pushButton_k_light_2624, false);
	set_pushButton_style(ui->pushButton_k_light_2784, false);
	set_pushButton_style(ui->pushButton_k_light_3000, false);
	set_pushButton_style(ui->pushButton_k_light_3140, false);

	set_pushButton_style(ui->pushButton_v_light_5126, false);
	set_pushButton_style(ui->pushButton_v_light_5228, false);
	set_pushButton_style(ui->pushButton_v_light_5386, false);
	set_pushButton_style(ui->pushButton_v_light_5494, false);
	set_pushButton_style(ui->pushButton_v_light_5550, false);
	set_pushButton_style(ui->pushButton_v_light_5666, false);
	set_pushButton_style(ui->pushButton_v_light_5730, false);
	set_pushButton_style(ui->pushButton_v_light_5800, false);

	set_pushButton_style(ui->pushButton_light_rain, false);
	set_pushButton_style(ui->pushButton_light_temp, false);
	set_pushButton_style(ui->pushButton_light_temp, false);
   //k通道电压
	ui->pushButton_k_2224->setObjectName("K_v_1");
	ui->pushButton_k_2304->setObjectName("K_v_2");
	ui->pushButton_k_2384->setObjectName("K_v_3");
	ui->pushButton_k_2544->setObjectName("K_v_4");
	ui->pushButton_k_2624->setObjectName("K_v_5");
	ui->pushButton_k_2784->setObjectName("K_v_6");
	ui->pushButton_k_3000->setObjectName("K_v_7");
	ui->pushButton_k_3140->setObjectName("K_v_8");
	ui->pushButton_k_stop->setObjectName("K_v_stop");
	ui->pushButton_k_all_show->setObjectName("K_v_show");
	ui->pushButton_k_clear->setObjectName("K_v_clear");
	//v通道电压
	ui->pushButton_v_5126->setObjectName("V_v_1");
	ui->pushButton_v_5228->setObjectName("V_v_2");
	ui->pushButton_v_5386->setObjectName("V_v_3");
	ui->pushButton_v_5494->setObjectName("V_v_4");
	ui->pushButton_v_5550->setObjectName("V_v_5");
	ui->pushButton_v_5666->setObjectName("V_v_6");
	ui->pushButton_v_5730->setObjectName("V_v_7");
	ui->pushButton_v_5800->setObjectName("V_v_8");
	ui->pushButton_v_stop->setObjectName("V_v_stop");
	ui->pushButton_v_all_show->setObjectName("V_v_show");
	ui->pushButton_v_clear->setObjectName("V_v_clear");
	//k通道温度
	ui->pushButton_k_temp1->setObjectName("K_t_1");
	ui->pushButton_k_temp2->setObjectName("K_t_2");
	ui->pushButton_k_temp3->setObjectName("K_t_3");
	ui->pushButton_k_temp_stop->setObjectName("K_t_stop");
	ui->pushButton_k_temp_all_show->setObjectName("K_t_show");
	ui->pushButton_k_temp_clear->setObjectName("K_t_clear");
	//v通道温度
	ui->pushButton_v_temp1->setObjectName("V_t_1");
	ui->pushButton_v_temp2->setObjectName("V_t_2");
	ui->pushButton_v_temp3->setObjectName("V_t_3");
	ui->pushButton_v_temp_stop->setObjectName("V_t_stop");
	ui->pushButton_v_temp_all_show->setObjectName("V_t_show");
	ui->pushButton_v_temp_clear->setObjectName("V_t_clear");

	//k亮温通道电压
	ui->pushButton_k_light_2224->setObjectName("K_LT_v_1");
	ui->pushButton_k_light_2304->setObjectName("K_LT_v_2");
	ui->pushButton_k_light_2384->setObjectName("K_LT_v_3");
	ui->pushButton_k_light_2544->setObjectName("K_LT_v_4");
	ui->pushButton_k_light_2624->setObjectName("K_LT_v_5");
	ui->pushButton_k_light_2784->setObjectName("K_LT_v_6");
	ui->pushButton_k_light_3000->setObjectName("K_LT_v_7");
	ui->pushButton_k_light_3140->setObjectName("K_LT_v_8");
	ui->pushButton_k_light_stop->setObjectName("K_LT_v_stop");
	ui->pushButton_k_light_all_show->setObjectName("K_LT_v_show");
	ui->pushButton_k_light_clear->setObjectName("K_LT_v_clear");
	//v亮温通道电压
	ui->pushButton_v_light_5126->setObjectName("V_LT_v_1");
	ui->pushButton_v_light_5228->setObjectName("V_LT_v_2");
	ui->pushButton_v_light_5386->setObjectName("V_LT_v_3");
	ui->pushButton_v_light_5494->setObjectName("V_LT_v_4");
	ui->pushButton_v_light_5550->setObjectName("V_LT_v_5");
	ui->pushButton_v_light_5666->setObjectName("V_LT_v_6");
	ui->pushButton_v_light_5730->setObjectName("V_LT_v_7");
	ui->pushButton_v_light_5800->setObjectName("V_LT_v_8");
	ui->pushButton_v_light_stop->setObjectName("V_LT_v_stop");
	ui->pushButton_v_light_all_show->setObjectName("V_LT_v_show");
	ui->pushButton_v_light_clear->setObjectName("V_LT_v_clear");


	ui->pushButton_light_rain->setObjectName("L_r");
	ui->pushButton_light_rain_stop->setObjectName("L_r_stop");
	ui->pushButton_light_rain_clear->setObjectName("L_r_clear");
	ui->pushButton_light_rain_all_show->setObjectName("L_r_all_show");

	ui->pushButton_light_temp->setObjectName("L_t");
	ui->pushButton_light_temp_stop->setObjectName("L_t_stop");
	ui->pushButton_light_temp_clear->setObjectName("L_t_clear");
	ui->pushButton_light_temp_all_show->setObjectName("L_t_all_show");

	connect(ui->pushButton_k_2224, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_2304, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_2384, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_2544, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_2624, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_2784, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_3000, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_3140, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_stop, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_all_show, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_clear, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);

	connect(ui->pushButton_v_5126, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_5228, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_5386, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_5494, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_5550, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_5666, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_5730, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_5800, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_stop, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_all_show, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_clear, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);


	connect(ui->pushButton_k_temp1, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_temp2, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_temp3, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_temp_stop, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_temp_all_show, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_temp_clear, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);


	connect(ui->pushButton_v_temp1, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_temp2, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_temp3, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_temp_stop, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_temp_all_show, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_temp_clear, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);


	connect(ui->pushButton_k_light_2224, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_light_2304, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_light_2384, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_light_2544, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_light_2624, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_light_2784, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_light_3000, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_light_3140, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_light_stop, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_light_all_show, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_k_light_clear, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);

	connect(ui->pushButton_v_light_5126, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_light_5228, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_light_5386, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_light_5494, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_light_5550, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_light_5666, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_light_5730, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_light_5800, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_light_stop, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_light_all_show, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_v_light_clear, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);


	connect(ui->pushButton_light_rain, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_light_rain_stop, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_light_rain_clear, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_light_rain_all_show, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_light_temp, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_light_temp_stop, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_light_temp_clear, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);
	connect(ui->pushButton_light_temp_all_show, &QPushButton::clicked, this, &FMGWidget::handleButtonClick);

	

	//定标数据初始化
	//定标数据初始化
	QVector<QString> k_calibration_Tn = { "Tn",QString::number(calibrationValue_value.Alpha_param[0]),
		QString::number(calibrationValue_value.Alpha_param[0]),
		QString::number(calibrationValue_value.Alpha_param[1]),
		QString::number(calibrationValue_value.Alpha_param[2]),
		QString::number(calibrationValue_value.Alpha_param[3]),
		QString::number(calibrationValue_value.Alpha_param[4]),
		QString::number(calibrationValue_value.Alpha_param[5]),
		QString::number(calibrationValue_value.Alpha_param[6]),
		QString::number(calibrationValue_value.Alpha_param[7])
	};
	QVector<QString> k_calibration_c = { "C",QString::number(calibrationValue_value.gainCoef_C[0]),
		QString::number(calibrationValue_value.gainCoef_C[0]),
		QString::number(calibrationValue_value.gainCoef_C[1]),
		QString::number(calibrationValue_value.gainCoef_C[2]),
		QString::number(calibrationValue_value.gainCoef_C[3]),
		QString::number(calibrationValue_value.gainCoef_C[4]),
		QString::number(calibrationValue_value.gainCoef_C[5]),
		QString::number(calibrationValue_value.gainCoef_C[6]),
		QString::number(calibrationValue_value.gainCoef_C[7])
	};

	QVector<QString> k_calibration_Tsys = { "Tsys",QString::number(calibrationValue_value.sysBtNoi_Tsys[0]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[0]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[1]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[2]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[3]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[4]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[5]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[6]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[7])
	};

	addSingleRow(model_ca1, k_calibration_Tn);
	addSingleRow(model_ca1, k_calibration_c);
	addSingleRow(model_ca1, k_calibration_Tsys);

	QVector<QString> v_calibration_Tn = { "Tn",QString::number(calibrationValue_value.Alpha_param[0]),
		QString::number(calibrationValue_value.Alpha_param[8]),
		QString::number(calibrationValue_value.Alpha_param[9]),
		QString::number(calibrationValue_value.Alpha_param[10]),
		QString::number(calibrationValue_value.Alpha_param[11]),
		QString::number(calibrationValue_value.Alpha_param[12]),
		QString::number(calibrationValue_value.Alpha_param[13]),
		QString::number(calibrationValue_value.Alpha_param[14]),
		QString::number(calibrationValue_value.Alpha_param[15])
	};
	QVector<QString> v_calibration_c = { "C",QString::number(calibrationValue_value.gainCoef_C[0]),
		QString::number(calibrationValue_value.gainCoef_C[8]),
		QString::number(calibrationValue_value.gainCoef_C[9]),
		QString::number(calibrationValue_value.gainCoef_C[10]),
		QString::number(calibrationValue_value.gainCoef_C[11]),
		QString::number(calibrationValue_value.gainCoef_C[12]),
		QString::number(calibrationValue_value.gainCoef_C[13]),
		QString::number(calibrationValue_value.gainCoef_C[14]),
		QString::number(calibrationValue_value.gainCoef_C[15])
	};

	QVector<QString> v_calibration_Tsys = { "Tsys",QString::number(calibrationValue_value.sysBtNoi_Tsys[0]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[8]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[9]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[10]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[11]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[12]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[13]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[14]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[15])
	};
	addSingleRow(model_ca2, v_calibration_Tsys);
	addSingleRow(model_ca2, v_calibration_Tn);
	addSingleRow(model_ca2, v_calibration_c);

	connect(ui->comboBox_k_data, SIGNAL(currentIndexChanged(int)), this, SLOT(on_comboBox_k_real_data(int)));
	on_comboBox_k_real_data(0);//5分钟
	connect(ui->comboBox_v_data, SIGNAL(currentIndexChanged(int)), this, SLOT(on_comboBox_v_real_data(int)));
	on_comboBox_k_real_data(0);//5分钟

	time_show_status = new QTimer();
	connect(time_show_status, SIGNAL(timeout()), this, SLOT(set_status_show_time_status()));

	int width_swtich = ui->widget_swtich_button->width();
	int height_swtich = ui->widget_swtich_button->height();
	btn_swtich = new QCustomButton(ui->widget_swtich_button, width_swtich - 10, height_swtich - 10);
	ui->widget_swtich_button->layout()->addWidget(btn_swtich);
	connect(btn_swtich, &QCustomButton::stateChange, this, [=](bool isOn) {
		if (isOn == true)
		{
			time_show_status->start(1000);
			FMG_network->set_sendClear_fun();
			FMG_network->set_AntTgtAng_fun(180);
			FMG_network->set_sendsetting_fun();
			set_LV1_data_update(true);
			FMG_network->set_SixTemp_read_fun(true);
			FMG_network->set_AgvVoltage_read(true);
			FMG_network->set_AdcCardTempCtrl_read_fun(true);
			FMG_network->set_sixWeather_read_fun(true);
			qDebug() << "open swtich";
		}
		else if (isOn == false)
		{
			time_show_status->stop();
			set_LV1_data_update(false);
			FMG_network->set_AgvVoltage_read(false);
			FMG_network->set_AdcCardTempCtrl_read_fun(false);
			FMG_network->set_sixWeather_read_fun(false);
			qDebug() << "close swtich";
		}

		});
	}

FMGWidget::~FMGWidget()
{
	delete ui;
	delete data_weather_six;
	delete original_data;
	delete time_show_status;
	delete light_data;
}



void FMGWidget::set_graph_background(QCustomPlot* plot)
{
	plot->xAxis->setLabelColor(QColor(255, 255, 255));
	plot->yAxis->setLabelColor(QColor(255, 255, 255));

	plot->setBackground(QBrush(QColor(0, 40, 81)));
	plot->axisRect()->setBackground(QBrush(QColor(0, 40, 81)));
	plot->xAxis->setBasePen(QPen(Qt::white));
	plot->yAxis->setBasePen(QPen(Qt::white));
	plot->xAxis->setTickLabelColor(QColor(Qt::white));
	plot->yAxis->setTickLabelColor(QColor(Qt::white));
}
void FMGWidget::setupPlot(QCustomPlot* plot, QColor color)
{
	plot->graph()->setPen(QPen(color,2));
	plot->axisRect()->setupFullAxesBox(true);
	plot->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
	//plot->graph()->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssCircle, Qt::black, 5)); // 设置数据点圆圈为黑色
	plot->graph()->setLineStyle(QCPGraph::lsLine);                                            // 设置线样式为折线
	plot->graph()->setAdaptiveSampling(true);                                                 // 自适应采样
	plot->setAntialiasedElements(QCP::aeAll);                                                 // 启用所有抗锯齿
}


void FMGWidget::init_graph()
{
	// 添加画布
	// k通道电压
	ui->widget_k_voltage->addGraph();
	ui->label_k_2224->setStyleSheet("color:white;");
	setupPlot(ui->widget_k_voltage, QColor(Qt::white));
	ui->widget_k_voltage->addGraph();
	ui->label_k_2304->setStyleSheet("color:red;");
	setupPlot(ui->widget_k_voltage, QColor(Qt::red));
	ui->widget_k_voltage->addGraph();
	ui->label_k_2384->setStyleSheet("color:yellow;");
	setupPlot(ui->widget_k_voltage, QColor(Qt::yellow));
	ui->widget_k_voltage->addGraph();
	ui->label_k_2544->setStyleSheet("color:green;");
	setupPlot(ui->widget_k_voltage, QColor(Qt::green));
	ui->widget_k_voltage->addGraph();
	ui->label_k_2624->setStyleSheet("color:blue;");
	setupPlot(ui->widget_k_voltage, QColor(Qt::blue));
	ui->widget_k_voltage->addGraph();
	ui->label_k_2784->setStyleSheet("color:rgb(170,0,127);");
	setupPlot(ui->widget_k_voltage, QColor(170, 0, 127));
	ui->widget_k_voltage->addGraph();
	ui->label_k_3000->setStyleSheet("color:rgb(0,255,255);");
	setupPlot(ui->widget_k_voltage, QColor(0, 255, 255));
	ui->widget_k_voltage->addGraph();
	ui->label_k_3140->setStyleSheet("color:rgb(85, 255, 127);");
	setupPlot(ui->widget_k_voltage, QColor(85, 255, 127));
	// k通道温度
	ui->widget_k_temp->addGraph();
	ui->label_k_temp1->setStyleSheet("color:white;");
	setupPlot(ui->widget_k_temp, QColor(Qt::white));
	ui->widget_k_temp->addGraph();
	ui->label_k_temp2->setStyleSheet("color:red;");
	setupPlot(ui->widget_k_temp, QColor(Qt::red));
	ui->widget_k_temp->addGraph();
	ui->label_k_temp3->setStyleSheet("color:yellow;");
	setupPlot(ui->widget_k_temp, QColor(Qt::yellow));

	// v通道电压
	ui->widget_v_voltage->addGraph();
	ui->label_v_5126->setStyleSheet("color:white;");
	setupPlot(ui->widget_v_voltage, QColor(Qt::white));
	ui->widget_v_voltage->addGraph();
	ui->label_v_5228->setStyleSheet("color:red;");
	setupPlot(ui->widget_v_voltage, QColor(Qt::red));
	ui->widget_v_voltage->addGraph();
	ui->label_v_5386->setStyleSheet("color:yellow;");
	setupPlot(ui->widget_v_voltage, QColor(Qt::yellow));
	ui->widget_v_voltage->addGraph();
	ui->label_v_5494->setStyleSheet("color:green;");
	setupPlot(ui->widget_v_voltage, QColor(Qt::green));
	ui->widget_v_voltage->addGraph();
	ui->label_v_5550->setStyleSheet("color:blue;");
	setupPlot(ui->widget_v_voltage, QColor(Qt::blue));
	ui->widget_v_voltage->addGraph();
	ui->label_v_5666->setStyleSheet("color:rgb(170,0,127);");
	setupPlot(ui->widget_v_voltage, QColor(170, 0, 127));
	ui->widget_v_voltage->addGraph();
	ui->label_v_5730->setStyleSheet("color:rgb(0,255,255);");
	setupPlot(ui->widget_v_voltage, QColor(0, 255, 255));
	ui->widget_v_voltage->addGraph();
	ui->label_v_5800->setStyleSheet("color:rgb(85, 255, 127);");
	setupPlot(ui->widget_v_voltage, QColor(85, 255, 127));
	// v通温度
	ui->widget_v_temp->addGraph();
	ui->label_v_temp1->setStyleSheet("color:white;");
	setupPlot(ui->widget_v_temp, QColor(Qt::white));
	ui->widget_v_temp->addGraph();
	ui->label_v_temp2->setStyleSheet("color:red;");
	setupPlot(ui->widget_v_temp, QColor(Qt::red));
	ui->widget_v_temp->addGraph();
	ui->label_v_temp3->setStyleSheet("color:yellow;");
	setupPlot(ui->widget_v_temp, QColor(Qt::yellow));

	//获取秒数
	double now_secs = QDateTime::currentDateTime().toSecsSinceEpoch();
	// x轴以时间形式显示
	QSharedPointer<QCPAxisTickerDateTime> k_voltage_timeTicker(new QCPAxisTickerDateTime);
	k_voltage_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_k_voltage->xAxis->setTicker(k_voltage_timeTicker);
	ui->widget_k_voltage->xAxis->setLabel("时间");
	ui->widget_k_voltage->yAxis->setLabel("k通道接收机电压（mv）");
	ui->widget_k_voltage->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_k_voltage);
	ui->widget_k_voltage->replot(QCustomPlot::rpQueuedReplot);

	QSharedPointer<QCPAxisTickerDateTime> k_temp_timeTicker(new QCPAxisTickerDateTime);
	k_temp_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_k_temp->xAxis->setTicker(k_temp_timeTicker);
	ui->widget_k_temp->xAxis->setLabel("时间");
	ui->widget_k_temp->yAxis->setLabel("k通道接收机温度（℃）");
	ui->widget_k_temp->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_k_temp);

	ui->widget_k_temp->replot(QCustomPlot::rpQueuedReplot);

	QSharedPointer<QCPAxisTickerDateTime> v_voltage_timeTicker(new QCPAxisTickerDateTime);
	v_voltage_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_v_voltage->xAxis->setTicker(v_voltage_timeTicker);
	ui->widget_v_voltage->xAxis->setLabel("时间");
	ui->widget_v_voltage->yAxis->setLabel("v通道接收机电压（mv）");
	ui->widget_v_voltage->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_v_voltage);
	ui->widget_v_voltage->replot(QCustomPlot::rpQueuedReplot);

	QSharedPointer<QCPAxisTickerDateTime> v_temp_timeTicker(new QCPAxisTickerDateTime);
	v_temp_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_v_temp->xAxis->setTicker(v_temp_timeTicker);
	ui->widget_v_temp->xAxis->setLabel("时间");
	ui->widget_v_temp->yAxis->setLabel("v通道接收机温度（℃）");
	ui->widget_v_temp->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_v_temp);
	ui->widget_v_temp->replot(QCustomPlot::rpQueuedReplot);

	// x轴可自由变换 交互功能
	ui->widget_k_voltage->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);
	// x轴可自由变换 交互功能
	ui->widget_v_voltage->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);
	// x轴可自由变换 交互功能
	ui->widget_v_voltage->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);
	// x轴可自由变换 交互功能
	ui->widget_v_voltage->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);

	// 气象六要素
	ui->widget_humidity->addGraph();
	setupPlot(ui->widget_humidity, QColor(Qt::white));
	ui->widget_pressure->addGraph();
	setupPlot(ui->widget_pressure, QColor(Qt::white));
	ui->widget_rain->addGraph();
	setupPlot(ui->widget_rain, QColor(Qt::white));
	ui->widget_temp->addGraph();
	setupPlot(ui->widget_temp, QColor(Qt::white));
	ui->widget_wind_direction->addGraph();
	setupPlot(ui->widget_wind_direction, QColor(Qt::white));
	ui->widget_wind_speed->addGraph();
	setupPlot(ui->widget_wind_speed, QColor(Qt::white));

	QSharedPointer<QCPAxisTickerDateTime> temp_timeTicker(new QCPAxisTickerDateTime);
	temp_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_temp->xAxis->setTicker(temp_timeTicker);
	ui->widget_temp->xAxis->setLabel("时间");
	ui->widget_temp->yAxis->setLabel("温度（℃）");
	ui->widget_temp->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_temp);

	ui->widget_temp->replot(QCustomPlot::rpQueuedReplot);
	QSharedPointer<QCPAxisTickerDateTime> rain_timeTicker(new QCPAxisTickerDateTime);
	rain_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_rain->xAxis->setTicker(rain_timeTicker);
	ui->widget_rain->xAxis->setLabel("时间");
	ui->widget_rain->yAxis->setLabel("降水量（mm）");
	ui->widget_rain->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_rain);
	ui->widget_rain->replot(QCustomPlot::rpQueuedReplot);

	QSharedPointer<QCPAxisTickerDateTime> wind_direction_timeTicker(new QCPAxisTickerDateTime);
	wind_direction_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_wind_direction->xAxis->setTicker(wind_direction_timeTicker);
	ui->widget_wind_direction->xAxis->setLabel("时间");
	ui->widget_wind_direction->yAxis->setLabel("风向（度）");
	ui->widget_wind_direction->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_wind_direction);
	ui->widget_wind_direction->replot(QCustomPlot::rpQueuedReplot);

	QSharedPointer<QCPAxisTickerDateTime> wind_speed_timeTicker(new QCPAxisTickerDateTime);
	wind_speed_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_wind_speed->xAxis->setTicker(wind_speed_timeTicker);
	ui->widget_wind_speed->xAxis->setLabel("时间");
	ui->widget_wind_speed->yAxis->setLabel("风速（m/s）");
	ui->widget_wind_speed->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_wind_speed);
	ui->widget_wind_speed->replot(QCustomPlot::rpQueuedReplot);

	QSharedPointer<QCPAxisTickerDateTime> humidity_timeTicker(new QCPAxisTickerDateTime);
	humidity_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_humidity->xAxis->setTicker(humidity_timeTicker);
	ui->widget_humidity->xAxis->setLabel("时间");
	ui->widget_humidity->yAxis->setLabel("湿度（%）");
	ui->widget_humidity->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_humidity);
	ui->widget_humidity->replot(QCustomPlot::rpQueuedReplot);

	QSharedPointer<QCPAxisTickerDateTime> pressure_timeTicker(new QCPAxisTickerDateTime);
	pressure_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_pressure->xAxis->setTicker(pressure_timeTicker);
	ui->widget_pressure->xAxis->setLabel("时间");
	ui->widget_pressure->yAxis->setLabel("气压（KPa）");
	ui->widget_pressure->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_pressure);
	ui->widget_pressure->replot(QCustomPlot::rpQueuedReplot);

	// x轴可自由变换 交互功能
	ui->widget_humidity->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);
	// x轴可自由变换 交互功能
	ui->widget_temp->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);
	// x轴可自由变换 交互功能
	ui->widget_pressure->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);
	// x轴可自由变换 交互功能
	ui->widget_wind_direction->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);
	// x轴可自由变换 交互功能
	ui->widget_wind_speed->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);
	// x轴可自由变换 交互功能
	ui->widget_rain->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);

	// K通道亮温
	ui->widget_k_light_temp->addGraph();
	ui->label_k_light_2224->setStyleSheet("color:white;");
	setupPlot(ui->widget_k_light_temp, QColor(Qt::white));
	ui->widget_k_light_temp->addGraph();
	ui->label_k_light_2304->setStyleSheet("color:red;");
	setupPlot(ui->widget_k_light_temp, QColor(Qt::red));
	ui->widget_k_light_temp->addGraph();
	ui->label_k_light_2384->setStyleSheet("color:yellow;");
	setupPlot(ui->widget_k_light_temp, QColor(Qt::yellow));
	ui->widget_k_light_temp->addGraph();
	ui->label_k_light_2544->setStyleSheet("color:green;");
	setupPlot(ui->widget_k_light_temp, QColor(Qt::green));
	ui->widget_k_light_temp->addGraph();
	ui->label_k_light_2624->setStyleSheet("color:blue;");
	setupPlot(ui->widget_k_light_temp, QColor(Qt::blue));
	ui->widget_k_light_temp->addGraph();
	ui->label_k_light_2784->setStyleSheet("color:rgb(170,0,127);");
	setupPlot(ui->widget_k_light_temp, QColor(170, 0, 127));
	ui->widget_k_light_temp->addGraph();
	ui->label_k_light_3000->setStyleSheet("color:rgb(0,255,255);");
	setupPlot(ui->widget_k_light_temp, QColor(0, 255, 255));
	ui->widget_k_light_temp->addGraph();
	ui->label_k_light_3140->setStyleSheet("color:rgb(85, 255, 127);");
	setupPlot(ui->widget_k_light_temp, QColor(85, 255, 127));
	// v通道亮温
	ui->widget_v_light_temp->addGraph();
	ui->label_v_light_5126->setStyleSheet("color:white;");
	setupPlot(ui->widget_v_light_temp, QColor(Qt::white));
	ui->widget_v_light_temp->addGraph();
	ui->label_v_light_5228->setStyleSheet("color:red;");
	setupPlot(ui->widget_v_light_temp, QColor(Qt::red));
	ui->widget_v_light_temp->addGraph();
	ui->label_v_light_5386->setStyleSheet("color:yellow;");
	setupPlot(ui->widget_v_light_temp, QColor(Qt::yellow));
	ui->widget_v_light_temp->addGraph();
	ui->label_v_light_5494->setStyleSheet("color:green;");
	setupPlot(ui->widget_v_light_temp, QColor(Qt::green));
	ui->widget_v_light_temp->addGraph();
	ui->label_v_light_5550->setStyleSheet("color:blue;");
	setupPlot(ui->widget_v_light_temp, QColor(Qt::blue));
	ui->widget_v_light_temp->addGraph();
	ui->label_v_light_5666->setStyleSheet("color:rgb(170,0,127);");
	setupPlot(ui->widget_v_light_temp, QColor(170, 0, 127));
	ui->widget_v_light_temp->addGraph();
	ui->label_v_light_5730->setStyleSheet("color:rgb(0,255,255);");
	setupPlot(ui->widget_v_light_temp, QColor(0, 255, 255));
	ui->widget_v_light_temp->addGraph();
	ui->label_v_light_5800->setStyleSheet("color:rgb(85, 255, 127);");
	setupPlot(ui->widget_v_light_temp, QColor(85, 255, 127));
	// 降雨量
	ui->widget_light_rain->addGraph();
	ui->label_light_rain->setStyleSheet("color:white;");
	setupPlot(ui->widget_light_rain, QColor(Qt::white));
	// 常温源
	ui->widget_light_temp->addGraph();
	ui->label_light_temp->setStyleSheet("color:white;");
	setupPlot(ui->widget_light_temp, QColor(Qt::white));
	QSharedPointer<QCPAxisTickerDateTime> k_light_timeTicker(new QCPAxisTickerDateTime);
	k_light_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_k_light_temp->xAxis->setTicker(k_light_timeTicker);
	ui->widget_k_light_temp->xAxis->setLabel("时间");
	ui->widget_k_light_temp->yAxis->setLabel("亮温（K）");
	ui->widget_k_light_temp->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_k_light_temp);

	ui->widget_k_light_temp->replot(QCustomPlot::rpQueuedReplot);
	QSharedPointer<QCPAxisTickerDateTime> v_light_timeTicker(new QCPAxisTickerDateTime);
	v_light_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_v_light_temp->xAxis->setTicker(v_light_timeTicker);
	ui->widget_v_light_temp->xAxis->setLabel("时间");
	ui->widget_v_light_temp->yAxis->setLabel("亮温（V）");
	ui->widget_v_light_temp->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_v_light_temp);
	ui->widget_v_light_temp->replot(QCustomPlot::rpQueuedReplot);

	QSharedPointer<QCPAxisTickerDateTime> light_rain_timeTicker(new QCPAxisTickerDateTime);
	light_rain_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_light_rain->xAxis->setTicker(light_rain_timeTicker);
	ui->widget_light_rain->xAxis->setLabel("时间");
	ui->widget_light_rain->yAxis->setLabel("降雨量（%）");
	ui->widget_light_rain->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_light_rain);
	ui->widget_light_rain->replot(QCustomPlot::rpQueuedReplot);

	QSharedPointer<QCPAxisTickerDateTime> light_temp_timeTicker(new QCPAxisTickerDateTime);
	light_temp_timeTicker->setDateTimeFormat("hh:mm:ss");
	ui->widget_light_temp->xAxis->setTicker(light_temp_timeTicker);
	ui->widget_light_temp->xAxis->setLabel("时间");
	ui->widget_light_temp->yAxis->setLabel("常温源（℃）");
	ui->widget_light_temp->xAxis->setRange(now_secs - index_seconds, now_secs);
	set_graph_background(ui->widget_light_temp);
	ui->widget_light_temp->replot(QCustomPlot::rpQueuedReplot);

	// x轴可自由变换 交互功能
	ui->widget_v_light_temp->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);
	// x轴可自由变换 交互功能
	ui->widget_k_light_temp->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);
	// x轴可自由变换 交互功能
	ui->widget_light_temp->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);
	// x轴可自由变换 交互功能
	ui->widget_light_rain->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes |
		QCP::iSelectLegend | QCP::iSelectPlottables);
}

void FMGWidget::set_status_show_time_status()
{
	QtConcurrent::run(this, &FMGWidget::set_calibration_data);
	if (LV1_data_update_flag)
	{
		// 气象六要素
		QtConcurrent::run(this, &FMGWidget::set_status_show_time_weather_six);

		// 原始数据监测
		if (time_K_v_flag)
		{
			QtConcurrent::run(this, &FMGWidget::set_k_status_show_time_status);
		}
		if (time_V_v_flag)
		{
			QtConcurrent::run(this, &FMGWidget::set_v_status_show_time_status);
		}
		if (time_K_t_flag)
		{
			QtConcurrent::run(this, &FMGWidget::set_k_temp_status_show_time_status);
		}
		if (time_V_t_flag)
		{
			QtConcurrent::run(this, &FMGWidget::set_v_temp_status_show_time_status);
		}
		


		if (time_TTemp_flag)
		{
			QtConcurrent::run(this, &FMGWidget::set_temp_show_time_light);
		}
		if (time_Rain_flag)
		{
			QtConcurrent::run(this, &FMGWidget::set_rain_show_time_light);
		}
	}
	ui->tableView_k->scrollToBottom();
	ui->tableView_v->scrollToBottom();
}

void FMGWidget::set_Graph_index(QCustomPlot* plot, int i)//显示关闭图层
{
	QCPGraph* graph = plot->graph(i);
	bool isvisible = graph->visible();
	if (!isvisible)
	{
		graph->setVisible(true);
		
		isvisible = true;
	}
	else
	{
		graph->setVisible(false);
		
		isvisible = false;
	}
	plot->replot(QCustomPlot::rpQueuedReplot);
}
void FMGWidget::set_date_change(QSharedPointer<QCPAxisTickerTime> timeTicker)
{
	QDate currentDate = QDate::currentDate();
	if (currentDate.day() != lastDay)
	{
		baseDateTime = QDateTime(currentDate, QTime(0, 0, 0));
		lastDay = currentDate.day();
		timeTicker->setTickOrigin(0);
	}
}
void FMGWidget::set_status_show_time_weather_six()
{
	double now_secs = QDateTime::currentDateTime().toSecsSinceEpoch();
	// 温度
	ui->widget_temp->graph(0)->addData(now_secs, g_perWeather.Temp);
	ui->widget_temp->xAxis->setRange(now_secs - index_seconds, now_secs);
	ui->widget_temp->graph(0)->removeDataBefore(now_secs - index_seconds);
	QCPRange yrang = ui->widget_temp->yAxis->range();
	ui->widget_temp->yAxis->rescale();
	QCPRange yrang1 = ui->widget_temp->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_temp->yAxis->scaleRange(1.2);

	ui->widget_temp->replot(QCustomPlot::rpQueuedReplot);


	// 湿度
	ui->widget_humidity->graph(0)->addData(now_secs, g_perWeather.Humidity);
	ui->widget_humidity->xAxis->setRange(now_secs - index_seconds, now_secs);
	ui->widget_humidity->graph(0)->removeDataBefore(now_secs - index_seconds);

	yrang = ui->widget_humidity->yAxis->range();
	ui->widget_humidity->yAxis->rescale();
	yrang1 = ui->widget_humidity->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_humidity->yAxis->scaleRange(1.2);

	ui->widget_humidity->replot(QCustomPlot::rpQueuedReplot);

	// 气压
	ui->widget_pressure->graph(0)->addData(now_secs, g_perWeather.AirPress);
	ui->widget_pressure->xAxis->setRange(now_secs - index_seconds, now_secs);
	ui->widget_pressure->graph(0)->removeDataBefore(now_secs - index_seconds);

	yrang = ui->widget_pressure->yAxis->range();
	ui->widget_pressure->yAxis->rescale();
	yrang1 = ui->widget_pressure->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_pressure->yAxis->scaleRange(1.2);

	ui->widget_pressure->replot(QCustomPlot::rpQueuedReplot);

	// 风速
	ui->widget_wind_speed->graph(0)->addData(now_secs, g_perWeather.WindSpeed);
	ui->widget_wind_speed->xAxis->setRange(now_secs - index_seconds, now_secs);
	ui->widget_wind_speed->graph(0)->removeDataBefore(now_secs - index_seconds);

	yrang = ui->widget_wind_speed->yAxis->range();
	ui->widget_wind_speed->yAxis->rescale();
	yrang1 = ui->widget_wind_speed->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_wind_speed->yAxis->scaleRange(1.2);

	ui->widget_wind_speed->replot(QCustomPlot::rpQueuedReplot);

	// 风向
	ui->widget_wind_direction->graph(0)->addData(now_secs, g_perWeather.WindDir);
	ui->widget_wind_direction->xAxis->setRange(now_secs - index_seconds, now_secs);
	ui->widget_wind_direction->graph(0)->removeDataBefore(now_secs - index_seconds);

	yrang = ui->widget_wind_direction->yAxis->range();
	ui->widget_wind_direction->yAxis->rescale();
	yrang1 = ui->widget_wind_direction->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_wind_direction->yAxis->scaleRange(1.2);

	ui->widget_wind_direction->replot(QCustomPlot::rpQueuedReplot);

	// 降雨量
	ui->widget_rain->graph(0)->addData(now_secs, g_perWeather.Rain10Minute);
	ui->widget_rain->xAxis->setRange(now_secs - index_seconds, now_secs);
	ui->widget_rain->graph(0)->removeDataBefore(now_secs- index_seconds);

	yrang = ui->widget_rain->yAxis->range();
	ui->widget_rain->yAxis->rescale();
	yrang1 = ui->widget_rain->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_rain->yAxis->scaleRange(1.2);

	ui->widget_rain->replot(QCustomPlot::rpQueuedReplot);
}

void FMGWidget::addSingleRow(LargeTableModel* model, QVector<QString> data)
{
	int rowCount = model->rowCount();

	model->addRow(data);
	if (model->rowCount() > 86400)
	{
		model->clear1rowData();
		rowCount = model->rowCount();
		rowCount--;
	}
	rowCount++;
}

void FMGWidget::set_tracer(QCustomPlot* plot, QCPItemTracer* tracer, QCPItemText* tracerLabel)
{
	// 游标
	plot->setMouseTracking(true); // 让游标自动随鼠标移动，若不想游标随鼠标动，则禁止
	tracer->setVisible(true);     // 设置可见性
	// tracer->setPen(QPen(QBrush(QColor(Qt::red)),Qt::DashLine));   //虚线游标
	tracer->setPen(QPen(Qt::red));             // 圆圈轮廓颜色
	tracer->setBrush(QBrush(Qt::red));         // 圆圈圈内颜色
	tracer->setStyle(QCPItemTracer::tsCircle); // 圆圈
	tracer->setSize(3);

	// 游标说明
	tracerLabel->setVisible(true);                                   // 设置可见性
	tracerLabel->setLayer("Overlay");                                // 设置图层为overlay，因为需要频繁刷新
	tracerLabel->setPen(QPen(Qt::white));                            // 设置游标说明颜色
	tracerLabel->setPositionAlignment(Qt::AlignLeft | Qt::AlignTop); // 左上
	tracerLabel->setFont(QFont(font().family(), 8));                 // 字体大小
	tracerLabel->setPadding(QMargins(4, 4, 4, 4));                   // 文字距离边框几个像素
	tracerLabel->position->setType(QCPItemPosition::ptAxisRectRatio);
	tracerLabel->position->setParentAnchor(tracer->position); // 设置标签自动随着游标移动

	// lambda表达式 mouseMoveEvent

	connect(plot, &QCustomPlot::mouseMove, [=](QMouseEvent* event)
		{
			double x = plot->xAxis->pixelToCoord(event->pos().x());
			int total_sec = static_cast<int>(x);
			int hours = total_sec / 3600;
			int minutes = (total_sec % 3600) / 60;
			int second = total_sec % 60;
			QString timeStr = QString("%1:%2:%3").arg(hours, 2, 10, QLatin1Char('0')).arg(minutes, 2, 10, QLatin1Char('0')).arg(second, 2, 10, QLatin1Char('0'));
			double y = plot->yAxis->pixelToCoord(event->pos().y());
			tracer->setGraphKey(x); // 将游标横坐标设置成刚获得的横坐标数据x
			tracer->position->setCoords(x, y);
			QDateTime time = QDateTime::fromSecsSinceEpoch(static_cast<qint64>(x));
			QString timestr = time.toString("HH:mm:ss");
			tracerLabel->setText(QString("X轴%1\nY轴%2").arg(timestr).arg(y));
			tracerLabel->setColor(Qt::white);
			qDebug() << "mouseMove:" << x << y;
			plot->replot(); });
}

void FMGWidget::set_k_status_show_time_status()
{
	double now_secs = QDateTime::currentDateTime().toSecsSinceEpoch();
	while (plot_data_K_v)
	{
		QThread::msleep(1);
	}
	// 原始数据监测 KV通道温度电压
	ui->widget_k_voltage->graph(0)->addData(now_secs, g_perDataBoard.RadioVolAverage[0]);
	ui->widget_k_voltage->graph(1)->addData(now_secs, g_perDataBoard.RadioVolAverage[1]);
	ui->widget_k_voltage->graph(2)->addData(now_secs, g_perDataBoard.RadioVolAverage[2]);
	ui->widget_k_voltage->graph(3)->addData(now_secs, g_perDataBoard.RadioVolAverage[3]);
	ui->widget_k_voltage->graph(4)->addData(now_secs, g_perDataBoard.RadioVolAverage[4]);
	ui->widget_k_voltage->graph(5)->addData(now_secs, g_perDataBoard.RadioVolAverage[5]);
	ui->widget_k_voltage->graph(6)->addData(now_secs, g_perDataBoard.RadioVolAverage[6]);
	ui->widget_k_voltage->graph(7)->addData(now_secs, g_perDataBoard.RadioVolAverage[7]);

	ui->widget_k_voltage->xAxis->setRange(now_secs - index_seconds, now_secs);
	for (int i = 0; i < 16; i++)
	{
		BrightTemp_value[i] = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioVolAverage[i], i, false);
	}
	
	if (time_K_l_flag)
	{
		QtConcurrent::run(this, &FMGWidget::set_k_show_time_light);
	}
	
	for (int i = 0; i < 8; i++)
	{
		ui->widget_k_voltage->graph(i)->removeDataBefore(now_secs- index_seconds);
	}

	
	QCPRange yrang = ui->widget_k_voltage->yAxis->range();
	ui->widget_k_voltage->yAxis->rescale();
	QCPRange yrang1 = ui->widget_k_voltage->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_k_voltage->yAxis->scaleRange(1.2);

	plot_data_K_v = 1;
	ui->widget_k_voltage->replot(QCustomPlot::rpQueuedReplot);
	plot_data_K_v = 0;

	QDateTime  currentDateTime = QDateTime::currentDateTime();
	// 将时间转换为字符格式
	QString currentDateTimeString = currentDateTime.toString("yyyy-MM-dd hh:mm:ss");

	QString data_k1 = QString::number(g_perDataBoard.RadioVolAverage[0]);
	QString data_k2 = QString::number(g_perDataBoard.RadioVolAverage[1]);
	QString data_k3 = QString::number(g_perDataBoard.RadioVolAverage[2]);
	QString data_k4 = QString::number(g_perDataBoard.RadioVolAverage[3]);
	QString data_k5 = QString::number(g_perDataBoard.RadioVolAverage[4]);
	QString data_k6 = QString::number(g_perDataBoard.RadioVolAverage[5]);
	QString data_k7 = QString::number(g_perDataBoard.RadioVolAverage[6]);
	QString data_k8 = QString::number(g_perDataBoard.RadioVolAverage[7]);
	QVector<QString> allData_k = { currentDateTimeString,data_k1,data_k2,data_k3,data_k4,data_k5,data_k6,data_k7,data_k8 };
	addSingleRow(model_k, allData_k);


	//计算时间差
	QDateTime rowTime_0 = model_k->rowTime(0);
	QDateTime m_baseTime = rowTime_0;
	int hoursDiff = m_baseTime.secsTo(currentDateTime);

	if (hoursDiff >=second_time)
	{
		model_k->clear1rowData();
	}


}

void FMGWidget::set_v_status_show_time_status()
{
	double now_secs = QDateTime::currentDateTime().toSecsSinceEpoch();
	while (plot_data_V_v)
	{
		QThread::msleep(1);
	}
	// v通道电压
	ui->widget_v_voltage->graph(0)->addData(now_secs, g_perDataBoard.RadioVolAverage[8]);
	ui->widget_v_voltage->graph(1)->addData(now_secs, g_perDataBoard.RadioVolAverage[9]);
	ui->widget_v_voltage->graph(2)->addData(now_secs, g_perDataBoard.RadioVolAverage[10]);
	ui->widget_v_voltage->graph(3)->addData(now_secs, g_perDataBoard.RadioVolAverage[11]);
	ui->widget_v_voltage->graph(4)->addData(now_secs, g_perDataBoard.RadioVolAverage[12]);
	ui->widget_v_voltage->graph(5)->addData(now_secs, g_perDataBoard.RadioVolAverage[13]);
	ui->widget_v_voltage->graph(6)->addData(now_secs, g_perDataBoard.RadioVolAverage[14]);
	ui->widget_v_voltage->graph(7)->addData(now_secs, g_perDataBoard.RadioVolAverage[15]);

	ui->widget_v_voltage->xAxis->setRange(now_secs - index_seconds, now_secs);

	if (time_V_l_flag)
	{
		QtConcurrent::run(this, &FMGWidget::set_v_show_time_light);
	}

	for (int i = 0; i < 8; i++)
	{
		ui->widget_v_voltage->graph(i)->removeDataBefore(now_secs - index_seconds);
	}

	QCPRange yrang = ui->widget_v_voltage->yAxis->range();
	ui->widget_v_voltage->yAxis->rescale();
	QCPRange yrang1 = ui->widget_v_voltage->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_v_voltage->yAxis->scaleRange(1.2);

	plot_data_V_v = 1;
	ui->widget_v_voltage->replot(QCustomPlot::rpQueuedReplot);
	plot_data_V_v = 0;

	QDateTime  currentDateTime = QDateTime::currentDateTime();
	// 将时间转换为字符格式
	QString currentDateTimeString = currentDateTime.toString("yyyy-MM-dd hh:mm:ss");

	QString data_v1 = QString::number(g_perDataBoard.RadioVolAverage[8]);
	QString data_v2 = QString::number(g_perDataBoard.RadioVolAverage[9]);
	QString data_v3 = QString::number(g_perDataBoard.RadioVolAverage[10]);
	QString data_v4 = QString::number(g_perDataBoard.RadioVolAverage[11]);
	QString data_v5 = QString::number(g_perDataBoard.RadioVolAverage[12]);
	QString data_v6 = QString::number(g_perDataBoard.RadioVolAverage[13]);
	QString data_v7 = QString::number(g_perDataBoard.RadioVolAverage[14]);
	QString data_v8 = QString::number(g_perDataBoard.RadioVolAverage[15]);
	QVector<QString> allData_v = { currentDateTimeString,data_v1,data_v2,data_v3,data_v4,data_v5,data_v6,data_v7,data_v8 };

	addSingleRow(model_v, allData_v);

	//计算时间差
	QDateTime rowTime_0 = model_v->rowTime(0);
	QDateTime m_baseTime = rowTime_0;
	int hoursDiff = m_baseTime.secsTo(currentDateTime);

	if (hoursDiff >=second_time)
	{
		model_v->clear1rowData();
	}
}

void FMGWidget::set_k_temp_status_show_time_status()
{
	double now_secs = QDateTime::currentDateTime().toSecsSinceEpoch();
	while (plot_data_K_t)
	{
		QThread::msleep(1);
	}

	// k通道温度
	ui->widget_k_temp->graph(0)->addData(now_secs, g_perDataBoard.fTemValue[0]);
	ui->widget_k_temp->graph(1)->addData(now_secs, g_perDataBoard.fTemValue[1]);
	ui->widget_k_temp->graph(2)->addData(now_secs, g_perDataBoard.fTemValue[2]);
	ui->widget_k_temp->xAxis->setRange(now_secs - index_seconds, now_secs);


	for (int i = 0; i < 3; i++)
	{
		ui->widget_k_temp->graph(i)->removeDataBefore(now_secs -index_seconds );
	}
	

	QCPRange yrang = ui->widget_k_temp->yAxis->range();
	ui->widget_k_temp->yAxis->rescale();
	QCPRange yrang1 = ui->widget_k_temp->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_k_temp->yAxis->scaleRange(1.2);

	plot_data_K_t = 1;
	ui->widget_k_temp->replot(QCustomPlot::rpQueuedReplot);
	plot_data_K_t = 0;

}

void FMGWidget::set_v_temp_status_show_time_status()
{
	double now_secs = QDateTime::currentDateTime().toSecsSinceEpoch();
	while (plot_data_V_t)
	{
		QThread::msleep(1);
	}
	// v通道温度

	ui->widget_v_temp->graph(0)->addData(now_secs, g_perDataBoard.fTemValue[3]);
	ui->widget_v_temp->graph(1)->addData(now_secs, g_perDataBoard.fTemValue[4]);
	ui->widget_v_temp->graph(2)->addData(now_secs, g_perDataBoard.fTemValue[5]);
	ui->widget_v_temp->xAxis->setRange(now_secs - index_seconds, now_secs);

	
	for (int i = 0; i < 3; i++)
	{
		ui->widget_v_temp->graph(i)->removeDataBefore(now_secs -index_seconds );
	}
	

	QCPRange yrang = ui->widget_v_temp->yAxis->range();
	ui->widget_v_temp->yAxis->rescale();
	QCPRange yrang1 = ui->widget_v_temp->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_v_temp->yAxis->scaleRange(1.2);

	plot_data_V_t = 1;
	ui->widget_v_temp->replot(QCustomPlot::rpQueuedReplot);
	plot_data_V_t = 0;
}

void FMGWidget::set_k_show_time_light()
{
	double now_secs = QDateTime::currentDateTime().toSecsSinceEpoch();
	while (plot_data_K_l)
	{
		QThread::msleep(1);
	}
	// k亮温数据监测

	ui->widget_k_light_temp->graph(0)->addData(now_secs, BrightTemp_value[0]);
	ui->widget_k_light_temp->graph(1)->addData(now_secs, BrightTemp_value[1]);
	ui->widget_k_light_temp->graph(2)->addData(now_secs, BrightTemp_value[2]);
	ui->widget_k_light_temp->graph(3)->addData(now_secs, BrightTemp_value[3]);
	ui->widget_k_light_temp->graph(4)->addData(now_secs, BrightTemp_value[4]);
	ui->widget_k_light_temp->graph(5)->addData(now_secs, BrightTemp_value[5]);
	ui->widget_k_light_temp->graph(6)->addData(now_secs, BrightTemp_value[6]);
	ui->widget_k_light_temp->graph(7)->addData(now_secs, BrightTemp_value[7]);
	ui->widget_k_light_temp->xAxis->setRange(now_secs - index_seconds, now_secs);

	for (int i = 0; i < 8; i++)
	{
		ui->widget_k_light_temp->graph(i)->removeDataBefore(now_secs -index_seconds );
	}
	

	QCPRange yrang = ui->widget_k_light_temp->yAxis->range();
	ui->widget_k_light_temp->yAxis->rescale();
	QCPRange yrang1 = ui->widget_k_light_temp->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_k_light_temp->yAxis->scaleRange(1.2);

	plot_data_K_l = 1;
	ui->widget_k_light_temp->replot(QCustomPlot::rpQueuedReplot);
	plot_data_K_l = 0;
}

void FMGWidget::set_v_show_time_light()
{
	double now_secs = QDateTime::currentDateTime().toSecsSinceEpoch();
	while (plot_data_V_l)
	{
		QThread::msleep(1);
	}
	// v亮温数据监测

	ui->widget_v_light_temp->graph(0)->addData(now_secs, BrightTemp_value[8]);
	ui->widget_v_light_temp->graph(1)->addData(now_secs, BrightTemp_value[9]);
	ui->widget_v_light_temp->graph(2)->addData(now_secs, BrightTemp_value[10]);
	ui->widget_v_light_temp->graph(3)->addData(now_secs, BrightTemp_value[11]);
	ui->widget_v_light_temp->graph(4)->addData(now_secs, BrightTemp_value[12]);
	ui->widget_v_light_temp->graph(5)->addData(now_secs, BrightTemp_value[13]);
	ui->widget_v_light_temp->graph(6)->addData(now_secs, BrightTemp_value[14]);
	ui->widget_v_light_temp->graph(7)->addData(now_secs, BrightTemp_value[15]);
	ui->widget_v_light_temp->xAxis->setRange(now_secs - index_seconds, now_secs);

	
	for (int i = 0; i < 8; i++)
	{
		ui->widget_v_light_temp->graph(i)->removeDataBefore(now_secs -index_seconds );
	}
	

	QCPRange yrang = ui->widget_v_light_temp->yAxis->range();
	ui->widget_v_light_temp->yAxis->rescale();
	QCPRange yrang1 = ui->widget_v_light_temp->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_v_light_temp->yAxis->scaleRange(1.2);

	plot_data_V_l = 1;
	ui->widget_v_light_temp->replot(QCustomPlot::rpQueuedReplot);
	plot_data_V_l = 0;
}

void FMGWidget::set_temp_show_time_light()
{
	double now_secs = QDateTime::currentDateTime().toSecsSinceEpoch();
	while (plot_data_T)
	{
		QThread::msleep(1);
	}
	// 常温源
	ui->widget_light_temp->graph(0)->addData(now_secs, g_perTemSource.fTemp_LowAverage);
	ui->widget_light_temp->xAxis->setRange(now_secs - index_seconds, now_secs);
	
	ui->widget_light_temp->graph(0)->removeDataBefore(now_secs -index_seconds );


	QCPRange yrang = ui->widget_light_temp->yAxis->range();
	ui->widget_light_temp->yAxis->rescale();
	QCPRange yrang1 = ui->widget_light_temp->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_light_temp->yAxis->scaleRange(1.2);

	plot_data_T = 1;
	ui->widget_light_temp->replot(QCustomPlot::rpQueuedReplot);
	plot_data_T = 0;
}

void FMGWidget::set_rain_show_time_light()
{
	double now_secs = QDateTime::currentDateTime().toSecsSinceEpoch();
	while (plot_data_rainfall)
	{
		QThread::msleep(1);
	}
	// 降雨量
	ui->widget_light_rain->graph(0)->addData(now_secs, g_perWeather.Rain10Minute);
	ui->widget_light_rain->xAxis->setRange(now_secs - index_seconds, now_secs);

	ui->widget_light_rain->graph(0)->removeDataBefore(now_secs -index_seconds );

	QCPRange yrang = ui->widget_light_rain->yAxis->range();
	ui->widget_light_rain->yAxis->rescale();
	QCPRange yrang1 = ui->widget_light_rain->yAxis->range();
	if (yrang1 != yrang)
		ui->widget_light_rain->yAxis->scaleRange(1.2);

	plot_data_rainfall = 1;
	ui->widget_light_rain->replot(QCustomPlot::rpQueuedReplot);
	plot_data_rainfall = 0;
}
void FMGWidget::set_pushButton_style(QPushButton* btn,bool style)
{
	
	if (!style)
	{
		btn->setStyleSheet("border-image: url(:/VectorIcon/eyesopen.png);");
		style = true;
	}
	else
	{
		btn->setStyleSheet("border-image: url(:/VectorIcon/eyesclose.png);");
		style = false;
	}
	
}

void FMGWidget::handleButtonClick()
{
	QPushButton* btn = qobject_cast<QPushButton*>(sender());
	QString btnName = btn->objectName();
	//k通道电压
	if (btnName == "K_v_1")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_voltage, 0);
		bool style = ui->widget_k_voltage->graph(0)->visible();
		set_pushButton_style(ui->pushButton_k_2224, style);
	}
	else if (btnName == "K_v_2")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_voltage, 1);
		bool style = ui->widget_k_voltage->graph(1)->visible();
		set_pushButton_style(ui->pushButton_k_2304, style);
	}
	else if (btnName == "K_v_3")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_voltage, 2);
		bool style = ui->widget_k_voltage->graph(2)->visible();
		set_pushButton_style(ui->pushButton_k_2384, style);
	}
	else if (btnName == "K_v_4")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_voltage, 3);
		bool style = ui->widget_k_voltage->graph(3)->visible();
		set_pushButton_style(ui->pushButton_k_2544, style);
	}
	else if (btnName == "K_v_5")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_voltage, 4);
		bool style = ui->widget_k_voltage->graph(4)->visible();
		set_pushButton_style(ui->pushButton_k_2624, style);
	}
	else if (btnName == "K_v_6")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_voltage, 5);
		bool style = ui->widget_k_voltage->graph(5)->visible();
		set_pushButton_style(ui->pushButton_k_2784, style);
	}
	else if (btnName == "K_v_7")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_voltage, 6);
		bool style = ui->widget_k_voltage->graph(6)->visible();
		set_pushButton_style(ui->pushButton_k_3000, style);
	}
	else if (btnName == "K_v_8")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_voltage, 7);
		bool style = ui->widget_k_voltage->graph(7)->visible();
		set_pushButton_style(ui->pushButton_k_3140, style);
	}
	else if (btnName == "K_v_stop")
	{
		if (time_K_v_flag == false)
		{
			time_K_v_flag = 1;

			ui->pushButton_k_stop->setText("暂停");
		}
		else
		{
			time_K_v_flag = 0;
			ui->pushButton_k_stop->setText("开始");
		}
	}
	else if (btnName == "K_v_show")
	{
		ui->widget_k_voltage->yAxis->rescale();
		ui->widget_k_voltage->replot(QCustomPlot::rpQueuedReplot);
	}
	else if (btnName == "K_v_clear")
	{
		while (plot_data_K_v == 1)
		{
			QThread::usleep(100);
		}
		plot_data_K_v = 1;
		for (int i = 0; i <= 7; i++)
		{
			ui->widget_k_voltage->graph(i)->data()->clear();
		}
		ui->widget_k_voltage->replot(QCustomPlot::rpQueuedReplot);
		plot_data_K_v = 0;
	}
	//v通道电压
	else if (btnName == "V_v_1")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_voltage, 0);
		bool style = ui->widget_v_voltage->graph(0)->visible();
		set_pushButton_style(ui->pushButton_v_5126, style);
	}
	else if (btnName == "V_v_2")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_voltage, 1);
		bool style = ui->widget_v_voltage->graph(1)->visible();
		set_pushButton_style(ui->pushButton_v_5228, style);
	}
	else if (btnName == "V_v_3")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_voltage, 2);
		bool style = ui->widget_v_voltage->graph(2)->visible();
		set_pushButton_style(ui->pushButton_v_5386, style);
	}
	else if (btnName == "V_v_4")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_voltage, 3);
		bool style = ui->widget_v_voltage->graph(3)->visible();
		set_pushButton_style(ui->pushButton_v_5494, style);
	}
	else if (btnName == "V_v_5")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_voltage, 4);
		bool style = ui->widget_v_voltage->graph(4)->visible();
		set_pushButton_style(ui->pushButton_v_5550, style);
	}
	else if (btnName == "V_v_6")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_voltage, 5);
		bool style = ui->widget_v_voltage->graph(5)->visible();
		set_pushButton_style(ui->pushButton_v_5666, style);
	}
	else if (btnName == "V_v_7")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_voltage, 6);
		bool style = ui->widget_v_voltage->graph(6)->visible();
		set_pushButton_style(ui->pushButton_v_5730, style);
	}
	else if (btnName == "V_v_8")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_voltage, 7);
		bool style = ui->widget_v_voltage->graph(7)->visible();
		set_pushButton_style(ui->pushButton_v_5800, style);
	}
	else if (btnName == "V_v_stop")
	{
		if (time_V_v_flag == false)
		{
			time_V_v_flag = 1;
			ui->pushButton_v_stop->setText("暂停");
		}
		else
		{
			time_V_v_flag = 0;
			ui->pushButton_v_stop->setText("开始");
		}
	}
	else if (btnName == "V_v_show")
	{
		ui->widget_v_voltage->yAxis->rescale();
		ui->widget_v_voltage->replot(QCustomPlot::rpQueuedReplot);
	}
	else if (btnName == "V_v_clear")
	{
		while (plot_data_V_v == 1)
		{
			QThread::usleep(100);
		}
		plot_data_V_v = 1;
		for (int i = 0; i <= 7; i++)
		{
			ui->widget_v_voltage->graph(i)->data()->clear();
		}
		ui->widget_v_voltage->replot(QCustomPlot::rpQueuedReplot);
		plot_data_V_v = 0;
	}

	//k通道温度
	else if (btnName == "K_t_1")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_temp, 0);
		bool style = ui->widget_k_temp->graph(0)->visible();
		set_pushButton_style(ui->pushButton_k_temp1, style);
	}
	else if (btnName == "K_t_2")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_temp, 1);
		bool style = ui->widget_k_temp->graph(1)->visible();
		set_pushButton_style(ui->pushButton_k_temp2, style);
	}
	else if (btnName == "K_t_3")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_temp, 2);
		bool style = ui->widget_k_temp->graph(2)->visible();
		set_pushButton_style(ui->pushButton_k_temp3, style);
	}
	else if (btnName == "K_t_stop")
	{
		if (time_K_t_flag == false)
		{
			time_K_t_flag = 1;
			ui->pushButton_k_temp_stop->setText("暂停");
		}
		else
		{
			time_K_t_flag = 0;
			ui->pushButton_k_temp_stop->setText("开始");
		}
	}
	else if (btnName == "K_t_show")
	{
		ui->widget_k_temp->yAxis->rescale();
		ui->widget_k_temp->replot(QCustomPlot::rpQueuedReplot);
	}
	else if (btnName == "K_t_clear")
	{
		while (plot_data_K_t == 1)
		{
			QThread::usleep(100);
		}
		plot_data_K_t = 1;
		for (int i = 0; i < 3; i++)
		{
			ui->widget_k_temp->graph(i)->data()->clear();
		}
		ui->widget_k_temp->replot(QCustomPlot::rpQueuedReplot);
		plot_data_K_t = 0;
	}
	//v通道温度
	else if (btnName == "V_t_1")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_temp, 0);
		bool style = ui->widget_v_temp->graph(0)->visible();
		set_pushButton_style(ui->pushButton_v_temp1, style);
	}
	else if (btnName == "V_t_2")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_temp, 1);
		bool style = ui->widget_v_temp->graph(1)->visible();
		set_pushButton_style(ui->pushButton_v_temp2, style);
	}
	else if (btnName == "V_t_3")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_temp, 2);
		bool style = ui->widget_v_temp->graph(2)->visible();
		set_pushButton_style(ui->pushButton_v_temp3, style);
	}
	else if (btnName == "V_t_stop")
	{
		if (time_V_t_flag == false)
		{
			time_V_t_flag = 1;
			ui->pushButton_v_temp_stop->setText("暂停");
		}
		else
		{
			time_V_t_flag = 0;
			ui->pushButton_v_temp_stop->setText("开始");
		}
	}
	else if (btnName == "V_t_show")
	{
		ui->widget_v_temp->yAxis->rescale();
		ui->widget_v_temp->replot(QCustomPlot::rpQueuedReplot);
	}
	else if (btnName == "V_t_clear")
	{
		while (plot_data_V_t == 1)
		{
			QThread::usleep(100);
		}
		plot_data_V_t = 1;
		for (int i = 0; i < 3; i++)
		{
			ui->widget_v_temp->graph(i)->data()->clear();
		}
		ui->widget_v_temp->replot(QCustomPlot::rpQueuedReplot);
		plot_data_V_t = 0;
	}
	//k亮温通道电压
	else if (btnName == "K_LT_v_1")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_light_temp, 0);
		bool style = ui->widget_k_light_temp->graph(0)->visible();
		set_pushButton_style(ui->pushButton_k_light_2224, style);
	}
	else if (btnName == "K_LT_v_2")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_light_temp, 1);
		bool style = ui->widget_k_light_temp->graph(1)->visible();
		set_pushButton_style(ui->pushButton_k_light_2304, style);
	}
	else if (btnName == "K_LT_v_3")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_light_temp, 2);
		bool style = ui->widget_k_light_temp->graph(2)->visible();
		set_pushButton_style(ui->pushButton_k_light_2384, style);
	}
	else if (btnName == "K_LT_v_4")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_light_temp, 3);
		bool style = ui->widget_k_light_temp->graph(3)->visible();
		set_pushButton_style(ui->pushButton_k_light_2544, style);
	}
	else if (btnName == "K_LT_v_5")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_light_temp, 4);
		bool style = ui->widget_k_light_temp->graph(4)->visible();
		set_pushButton_style(ui->pushButton_k_light_2624, style);
	}
	else if (btnName == "K_LT_v_6")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_light_temp, 5);
		bool style = ui->widget_k_light_temp->graph(5)->visible();
		set_pushButton_style(ui->pushButton_k_light_2784, style);
	}
	else if (btnName == "K_LT_v_7")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_light_temp, 6);
		bool style = ui->widget_k_light_temp->graph(6)->visible();
		set_pushButton_style(ui->pushButton_k_light_3000, style);
	}
	else if (btnName == "K_LT_v_8")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_k_light_temp, 7);
		bool style = ui->widget_k_light_temp->graph(7)->visible();
		set_pushButton_style(ui->pushButton_k_light_3140, style);
	}
	else if (btnName == "K_LT_v_stop")
	{
		if (time_K_l_flag == false)
		{
			time_K_l_flag = 1;
			ui->pushButton_k_light_stop->setText("暂停");
		}
		else
		{
			time_K_l_flag = 0;
			ui->pushButton_k_light_stop->setText("开始");
		}
	}
	else if (btnName == "K_LT_v_show")
	{
		ui->widget_k_light_temp->yAxis->rescale();
		ui->widget_k_light_temp->replot(QCustomPlot::rpQueuedReplot);
	}
	else if (btnName == "K_LT_v_clear")
	{
		while (plot_data_K_l == 1)
		{
			QThread::usleep(100);
		}
		plot_data_K_l = 1;
		for (int i = 0; i <= 7; i++)
		{
			ui->widget_k_light_temp->graph(i)->data()->clear();
		}
		ui->widget_k_light_temp->replot(QCustomPlot::rpQueuedReplot);
		plot_data_K_l = 0;
	}

	//v亮温通道电压
	else if (btnName == "V_LT_v_1")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_light_temp, 0);
		bool style = ui->widget_v_light_temp->graph(0)->visible();
		set_pushButton_style(ui->pushButton_v_light_5126, style);
	}
	else if (btnName == "V_LT_v_2")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_light_temp, 1);
		bool style = ui->widget_v_light_temp->graph(1)->visible();
		set_pushButton_style(ui->pushButton_v_light_5228, style);
	}
	else if (btnName == "V_LT_v_3")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_light_temp, 2);
		bool style = ui->widget_v_light_temp->graph(2)->visible();
		set_pushButton_style(ui->pushButton_v_light_5386, style);
	}
	else if (btnName == "V_LT_v_4")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_light_temp, 3);
		bool style = ui->widget_v_light_temp->graph(3)->visible();
		set_pushButton_style(ui->pushButton_v_light_5494, style);
	}
	else if (btnName == "V_LT_v_5")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_light_temp, 4);
		bool style = ui->widget_v_light_temp->graph(4)->visible();
		set_pushButton_style(ui->pushButton_v_light_5550, style);
	}
	else if (btnName == "V_LT_v_6")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_light_temp, 5);
		bool style = ui->widget_v_light_temp->graph(5)->visible();
		set_pushButton_style(ui->pushButton_v_light_5666, style);
	}
	else if (btnName == "V_LT_v_7")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_light_temp, 6);
		bool style = ui->widget_v_light_temp->graph(6)->visible();
		set_pushButton_style(ui->pushButton_v_light_5730, style);
	}
	else if (btnName == "V_LT_v_8")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_v_light_temp, 7);
		bool style = ui->widget_v_light_temp->graph(7)->visible();
		set_pushButton_style(ui->pushButton_v_light_5800, style);
	}
	else if (btnName == "V_LT_v_stop")
	{
		if (time_V_l_flag == false)
		{
			time_V_l_flag = 1;
			ui->pushButton_v_light_stop->setText("暂停");
		}
		else
		{
			time_V_l_flag = 0;
			ui->pushButton_v_light_stop->setText("开始");
		}
	}
	else if (btnName == "V_LT_v_show")
	{
		ui->widget_v_light_temp->yAxis->rescale();
		ui->widget_v_light_temp->replot(QCustomPlot::rpQueuedReplot);
	}
	else if (btnName == "V_LT_v_clear")
	{
		while (plot_data_V_l == 1)
		{
			QThread::usleep(100);
		}
		plot_data_V_l = 1;
		for (int i = 0; i <= 7; i++)
		{
			ui->widget_v_light_temp->graph(i)->data()->clear();
		}
		ui->widget_v_light_temp->replot(QCustomPlot::rpQueuedReplot);
		plot_data_V_l = 0;
	}
	//降雨量、温度
	else if (btnName == "L_r")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_light_rain, 0);
		bool style = ui->widget_light_rain->graph(0)->visible();
		set_pushButton_style(ui->pushButton_light_rain, style);
	}
	else if (btnName == "L_r_stop")
	{
		if (time_Rain_flag == false)
		{
			time_Rain_flag = 1;
			ui->pushButton_light_rain_stop->setText("暂停");
		}
		else
		{
			time_Rain_flag = 0;
			ui->pushButton_light_rain_stop->setText("开始");
		}
	}
	else if (btnName == "L_r_clear")
	{
		while (plot_data_rainfall == 1)
		{
			QThread::usleep(100);
		}
		plot_data_rainfall = 1;
		ui->widget_light_rain->graph(0)->data()->clear();
		ui->widget_light_rain->replot(QCustomPlot::rpQueuedReplot);
		plot_data_rainfall = 0;
	}
	else if (btnName == "L_r_all_show")
	{
		ui->widget_light_rain->yAxis->rescale();
		ui->widget_light_rain->replot(QCustomPlot::rpQueuedReplot);
	}
	else if (btnName == "L_t")
	{
		QtConcurrent::run(this, &FMGWidget::set_Graph_index, ui->widget_light_temp, 0);
		bool style = ui->widget_light_temp->graph(0)->visible();
		set_pushButton_style(ui->pushButton_light_temp, style);
	}
	else if (btnName == "L_t_stop")
	{
		if (time_TTemp_flag == false)
		{
			time_TTemp_flag = 1;
			ui->pushButton_light_temp_stop->setText("暂停");
		}
		else
		{
			time_TTemp_flag = 0;
			ui->pushButton_light_temp_stop->setText("开始");
		}
	}
	else if (btnName == "L_t_clear")
	{
		ui->widget_light_temp->yAxis->rescale();
		ui->widget_light_temp->replot(QCustomPlot::rpQueuedReplot);
	}
	else if (btnName == "L_t_all_show")
	{
		while (plot_data_T == 1)
		{
			QThread::usleep(100);
		}
		plot_data_T = 1;
		ui->widget_light_temp->graph(0)->data()->clear();
		ui->widget_light_temp->replot(QCustomPlot::rpQueuedReplot);
		plot_data_T = 0;
	}

}

void FMGWidget::stop_plot()
{
	if (!time_status)
	{
		time_show_status->stop();
		time_status = true;
		ui->pushButton_k_stop->setText("开始");
		ui->pushButton_v_stop->setText("开始");
		ui->pushButton_k_temp_stop->setText("开始");
		ui->pushButton_v_temp_stop->setText("开始");
		ui->pushButton_k_light_stop->setText("开始");
		ui->pushButton_v_light_stop->setText("开始");
		ui->pushButton_light_temp->setText("开始");
	}
	else
	{
		time_show_status->start(1000);
		time_status = false;
		ui->pushButton_k_stop->setText("暂停");
		ui->pushButton_v_stop->setText("暂停");
		ui->pushButton_k_temp_stop->setText("暂停");
		ui->pushButton_v_temp_stop->setText("暂停");
		ui->pushButton_k_light_stop->setText("暂停");
		ui->pushButton_v_light_stop->setText("暂停");
		ui->pushButton_light_rain_stop->setText("开始");
		ui->pushButton_light_rain_stop->setText("暂停");
		ui->pushButton_light_temp->setText("暂停");
	}
}


void FMGWidget::closeEvent(QCloseEvent* event)
{
	// time_show_status->stop();
}

bool FMGWidget::eventFilter(QObject* watched, QEvent* event)
{
	if (event->type() == QEvent::MouseButtonRelease)
	{
		if (auto* btn = qobject_cast<QPushButton*>(watched))
		{
			btn->setChecked(!btn->isChecked());
		}
	}

	if (event->type() == QEvent::Enter && watched->inherits("QCustomPlot"))
	{
		QCustomPlot* plot_event = qobject_cast<QCustomPlot*>(watched);

		if (plot_event == ui->widget_k_voltage)
		{
			tracer_k_voltage->setVisible(true);
			tracerLabe_k_voltage->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);

		}
		else if (plot_event == ui->widget_k_temp)
		{
			tracer_k_temp->setVisible(true);
			tracerLabe_k_temp->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_v_voltage)
		{
			tracer_v_voltage->setVisible(true);
			tracerLabe_v_voltage->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_v_temp)
		{
			tracer_v_temp->setVisible(true);
			tracerLabe_v_temp->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		//气象六要素
		else if (plot_event == ui->widget_humidity)
		{
			tracer_humidity->setVisible(true);
			tracerLabe_humidity->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_pressure)
		{
			tracer_pressure->setVisible(true);
			tracerLabe_pressure->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_rain)
		{
			tracer_rain->setVisible(true);
			tracerLabe_rain->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_temp)
		{
			tracer_temp->setVisible(true);
			tracerLabe_temp->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_wind_direction)
		{
			tracer_wind_direction->setVisible(true);
			tracerLabe_wind_direction->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_wind_speed)
		{
			tracer_wind_speed->setVisible(true);
			tracerLabe_wind_speed->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		//K通道亮温
		else if (plot_event == ui->widget_k_light_temp)
		{
			tracer_k_light_temp->setVisible(true);
			tracerLabe_k_light_temp->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		//v通道亮温
		else if (plot_event == ui->widget_v_light_temp)
		{
			tracer_v_light_temp->setVisible(true);
			tracerLabe_v_light_temp->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}

		//降雨量
		else if (plot_event == ui->widget_light_rain)
		{
			tracer_light_rain->setVisible(true);
			tracerLabe_light_rain->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}

		//常温源
		else if (plot_event == ui->widget_light_temp)
		{
			tracer_light_temp->setVisible(true);
			tracerLabe_light_temp->setVisible(true);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
	}

	else if (event->type() == QEvent::Leave && watched->inherits("QCustomPlot"))
	{
		QCustomPlot* plot_event = qobject_cast<QCustomPlot*>(watched);

		if (plot_event == ui->widget_k_voltage)
		{
			tracer_k_voltage->setVisible(false);
			tracerLabe_k_voltage->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);

		}
		else if (plot_event == ui->widget_k_temp)
		{
			tracer_k_temp->setVisible(false);
			tracerLabe_k_temp->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_v_voltage)
		{
			tracer_v_voltage->setVisible(false);
			tracerLabe_v_voltage->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_v_temp)
		{
			tracer_v_temp->setVisible(false);
			tracerLabe_v_temp->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		//气象六要素
		else if (plot_event == ui->widget_humidity)
		{
			tracer_humidity->setVisible(false);
			tracerLabe_humidity->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_pressure)
		{
			tracer_pressure->setVisible(false);
			tracerLabe_pressure->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_rain)
		{
			tracer_rain->setVisible(false);
			tracerLabe_rain->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_temp)
		{
			tracer_temp->setVisible(false);
			tracerLabe_temp->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_wind_direction)
		{
			tracer_wind_direction->setVisible(false);
			tracerLabe_wind_direction->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		else if (plot_event == ui->widget_wind_speed)
		{
			tracer_wind_speed->setVisible(false);
			tracerLabe_wind_speed->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		//K通道亮温
		else if (plot_event == ui->widget_k_light_temp)
		{
			tracer_k_light_temp->setVisible(false);
			tracerLabe_k_light_temp->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
		//v通道亮温
		else if (plot_event == ui->widget_v_light_temp)
		{
			tracer_v_light_temp->setVisible(false);
			tracerLabe_v_light_temp->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}

		//降雨量
		else if (plot_event == ui->widget_light_rain)
		{
			tracer_light_rain->setVisible(false);
			tracerLabe_light_rain->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}

		//常温源
		else if (plot_event == ui->widget_light_temp)
		{
			tracer_light_temp->setVisible(false);
			tracerLabe_light_temp->setVisible(false);

			plot_event->replot(QCustomPlot::rpQueuedReplot);
		}
	}


	return QWidget::eventFilter(watched, event);
}


void FMGWidget::set_LV1_data_update(bool value)
{
	LV1_data_update_flag = value;
}

void FMGWidget::set_calibration_data() //定标数据初始化
{
	//定标数据初始化
	QVector<QString> k_calibration_Tn = { "Tn",QString::number(calibrationValue_value.Alpha_param[0]),
		QString::number(calibrationValue_value.Alpha_param[0]),
		QString::number(calibrationValue_value.Alpha_param[1]),
		QString::number(calibrationValue_value.Alpha_param[2]),
		QString::number(calibrationValue_value.Alpha_param[3]),
		QString::number(calibrationValue_value.Alpha_param[4]),
		QString::number(calibrationValue_value.Alpha_param[5]),
		QString::number(calibrationValue_value.Alpha_param[6]),
		QString::number(calibrationValue_value.Alpha_param[7])
	};
	QVector<QString> k_calibration_c = { "C",QString::number(calibrationValue_value.gainCoef_C[0]),
		QString::number(calibrationValue_value.gainCoef_C[0]),
		QString::number(calibrationValue_value.gainCoef_C[1]),
		QString::number(calibrationValue_value.gainCoef_C[2]),
		QString::number(calibrationValue_value.gainCoef_C[3]),
		QString::number(calibrationValue_value.gainCoef_C[4]),
		QString::number(calibrationValue_value.gainCoef_C[5]),
		QString::number(calibrationValue_value.gainCoef_C[6]),
		QString::number(calibrationValue_value.gainCoef_C[7])
	};

	QVector<QString> k_calibration_Tsys = { "C",QString::number(calibrationValue_value.sysBtNoi_Tsys[0]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[0]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[1]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[2]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[3]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[4]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[5]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[6]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[7])
	};
	
	model_ca1->replaceRow(0, k_calibration_Tn);
	model_ca1->replaceRow(1, k_calibration_c);
	model_ca1->replaceRow(2, k_calibration_Tsys);

	

	
	QVector<QString> v_calibration_Tn = { "Tn",QString::number(calibrationValue_value.Alpha_param[0]),
		QString::number(calibrationValue_value.Alpha_param[8]),
		QString::number(calibrationValue_value.Alpha_param[9]),
		QString::number(calibrationValue_value.Alpha_param[10]),
		QString::number(calibrationValue_value.Alpha_param[11]),
		QString::number(calibrationValue_value.Alpha_param[12]),
		QString::number(calibrationValue_value.Alpha_param[13]),
		QString::number(calibrationValue_value.Alpha_param[14]),
		QString::number(calibrationValue_value.Alpha_param[15])
	};
	QVector<QString> v_calibration_c = { "C",QString::number(calibrationValue_value.gainCoef_C[0]),
		QString::number(calibrationValue_value.gainCoef_C[8]),
		QString::number(calibrationValue_value.gainCoef_C[9]),
		QString::number(calibrationValue_value.gainCoef_C[10]),
		QString::number(calibrationValue_value.gainCoef_C[11]),
		QString::number(calibrationValue_value.gainCoef_C[12]),
		QString::number(calibrationValue_value.gainCoef_C[13]),
		QString::number(calibrationValue_value.gainCoef_C[14]),
		QString::number(calibrationValue_value.gainCoef_C[15])
	};

	QVector<QString> v_calibration_Tsys = { "C",QString::number(calibrationValue_value.sysBtNoi_Tsys[0]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[8]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[9]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[10]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[11]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[12]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[13]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[14]),
		QString::number(calibrationValue_value.sysBtNoi_Tsys[15])
	};
	model_ca2->replaceRow(0, v_calibration_Tn);
	model_ca2->replaceRow(1, v_calibration_c);
	model_ca2->replaceRow(2, v_calibration_Tsys);



	
}

void FMGWidget::on_comboBox_k_real_data(int index)
{
	if (index == 0)
	{
		second_time = 5 * 60;
	}
	else if (index == 1)
	{
		second_time = 10 * 60;
	}
	else if (index == 2)
	{
		second_time = 30 * 3600;
	}
	else if (index == 3)
	{
		second_time = 1 * 3600;
	}
}

void FMGWidget::on_comboBox_v_real_data(int index)
{
	if (index == 0)
	{
		second_time = 5 * 60;
	}
	else if (index == 1)
	{
		second_time = 10 * 60;
	}
	else if (index == 2)
	{
		second_time = 30 * 3600;
	}
	else if (index == 3)
	{
		second_time = 1 * 3600;
	}
}