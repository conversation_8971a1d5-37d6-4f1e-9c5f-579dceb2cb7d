﻿#include "lv1_data_storage.h"
#include "include/relativehumidityprofile.h"  //廓线反演类
#include "include/StructRecvBusinessData.h"
#include "include/temperatureprofile.h"
#include "include/watervapordensityprofile.h"
Lv1_data_storage::Lv1_data_storage(QString file_path, FILENAME file_name, QObject* parent)
{
    Q_UNUSED(parent);
    filepath = file_path;
    filename = file_name;

    file = nullptr;
}

/*
Generate_new_Lv1_Bright_temperature_data
作用：LV1数据存入日文件。
参数要求：
Record、DateTime由函数内部生成;
其他参数需用户输入

返回值：true :保存成功
       false :保存失败
*/
bool Lv1_data_storage::Generate_new_Lv1_Bright_temperature_data(Lv_Basic_parameters basic_parameters, Lv1_Bright_temperature_data bright_temperature)
{
    // 创建目录结构
    struct FILENAME temp = filename;
    temp.pf1ag = "Z";
    temp.productidentifier = "UPAR";
    if (use_beijing_utc)
        temp.yyyyMMddhhmmss = QDateTime::currentDateTime().toString("yyyyMMddHHmmss");
    else
        temp.yyyyMMddhhmmss = QDateTime::currentDateTimeUtc().toString("yyyyMMddHHmmss");
    temp.deviceidentification = "YMWR";
    temp.frequency = "D";
    temp.type = "txt";
    temp.datatype = "RAW";
    temp.ftype = "O";

    QString filenamestr = Generate_file_name(temp);
    QString filepath_dir;
    if (use_beijing_utc)
        filepath_dir = filepath + QDateTime::currentDateTime().toString("yyyy-MM-dd") + "/";
    else
        filepath_dir = filepath + QDateTime::currentDateTimeUtc().toString("yyyy-MM-dd") + "/";
    QDir dir(filepath_dir);
    if (!dir.exists())
        dir.mkpath(filepath_dir);

    bool isexitsfile = false;
    foreach(const QFileInfo & fileinfo, dir.entryInfoList(QDir::Files))
    {
        if (comparefilenames(fileinfo.fileName(), filenamestr))
        {

            if (cur_day_filename != fileinfo.fileName())
            {
                if (file != nullptr)
                    delete file;
                file = new QFile(fileinfo.path() + "/" + fileinfo.fileName());
                if (!file->open(QIODevice::ReadOnly))
                {
                    return false;
                }
                cur_day_filename = fileinfo.fileName();
                // 通过有多少行判断
                QTextStream in(file);
                int linecount = 0;

                while (!in.atEnd())
                {
                    in.readLine();
                    linecount++;
                }
                Record_number = linecount - 2;

                file->close();
            }

            if (file != nullptr)
                delete file;
            file = new QFile(fileinfo.path() + "/" + fileinfo.fileName());
            if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
            {
                return false;
            }

            isexitsfile = true;
        }
    }
    if (!isexitsfile)
    {
        // 创建文件
        if (file != nullptr)
            delete file;
        file = new QFile(filepath_dir + filenamestr);
        if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
        {
            return false;
        }
        cur_day_filename = filenamestr;
        Record_number = 1; // 行号置1

        //Insert_new_Lv1_convert_lv2_Basic_parameters(basic_parameters);//lv1-lv2基础数据转换（高度）
        Insert_new_Lv_Basic_parameters(basic_parameters);
    }

    //Insert_new_Lv1_convert_lv2_data(bright_temperature);//lv1-lv2数据转换
    Insert_new_Lv1_Bright_temperature_data(bright_temperature);

    return true;
}

bool Lv1_data_storage::Insert_new_Lv_Basic_parameters(struct Lv_Basic_parameters basic_parameters)
{
    if (!file->isOpen())
    {
        if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
        {
            return false; // 如果
        }
    }

    QByteArray linedata;
    QString line1 = basic_parameters.MWR + ',' + Control_decimal_bit(basic_parameters.Data_format_version_number, 2, true);
    if (!isPureAscii(line1))
    {
        file->close();
        return false;
    }
    linedata = line1.toLatin1();
    linedata.append("\r\n");

    if (file->write(linedata) == -1)
    {
        file->close();
        return false;
    }

    linedata.clear();
    QString line2 = basic_parameters.Station_number + ',' + Control_decimal_bit(basic_parameters.longitude, 4, true) + ',' + Control_decimal_bit(basic_parameters.dimension, 4, true) + ',' + Control_decimal_bit(basic_parameters.altitude, 1, true) + ',' + basic_parameters.Equipment_type + ',' + Control_decimal_bit(basic_parameters.dimension, 0, true) + ',' + Control_decimal_bit(basic_parameters.altitude, 0, true);
    if (!isPureAscii(line2))
    {
        file->close();
        return false;
    }
    linedata = line2.toLatin1();
    linedata.append("\r\n");

    if (file->write(linedata) == -1)
    {
        file->close();
        return false;
    }
    linedata.clear();
    QString table_header = "Record,DateTime,SurTem(℃),SurHum(%),SurPre(hPa),Tir(℃),Rain,QCFlag,Az(deg),El(deg),Ch 22.24,Ch 23.04,Ch 23.84,Ch 25.440,Ch 26.240,Ch 27.840,Ch 31.400,Ch 51.260,Ch 52.280,Ch 53.860,Ch 54.940,Ch 56.660,Ch 57.300,Ch 58.800,QCFlag_BT";
    linedata = table_header.toLatin1();
    linedata.append("\r\n");

    if (file->write(linedata) == -1)
    {
        file->close();
        return false;
    }
    file->close();
    return true;
}

bool Lv1_data_storage::Insert_new_Lv1_convert_lv2_Basic_parameters(Lv_Basic_parameters basic_parameters)
{
    if (!file->isOpen())
    {
        if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
        {
            return false; // 如果
        }
    }

    QByteArray linedata;
    QString line1 = basic_parameters.MWR + ',' + Control_decimal_bit(basic_parameters.Data_format_version_number, 2, true);
    if (!isPureAscii(line1))
    {
        file->close();
        return false;
    }
    linedata = line1.toLatin1();
    linedata.append("\r\n");

    if (file->write(linedata) == -1)
    {
        file->close();
        return false;
    }

    linedata.clear();
    QString line2 = basic_parameters.Station_number + ',' + Control_decimal_bit(basic_parameters.longitude, 4, true) + ',' + Control_decimal_bit(basic_parameters.dimension, 4, true) + ',' + Control_decimal_bit(basic_parameters.altitude, 1, true) + ',' + basic_parameters.Equipment_type + ',' + Control_decimal_bit(basic_parameters.dimension, 0, true) + ',' + Control_decimal_bit(basic_parameters.altitude, 0, true);
    if (!isPureAscii(line2))
    {
        file->close();
        return false;
    }
    linedata = line2.toLatin1();
    linedata.append("\r\n");

    if (file->write(linedata) == -1)
    {
        file->close();
        return false;
    }
    linedata.clear();
    QString table_header = "Record,DateTime,10,SurTem(℃),SurHum(%),SurPre(hPa),Tir(℃),Rain,CloudBase(km),Vint(mm),Lqint(mm),0km,0.025km,0.05km,0.075km,0.1km,0.125km,0.15km,0.175km,0.2km,0.225km,0.25km,0.275km,0.3km,0.325km,0.35km,0.375km,0.4km,0.425km,0.45km,0.475km,0.5km,0.55km,0.6km,0.65km,0.7km,0.75km,0.8km,0.85km,0.9km,0.95km,1km,1.05km,1.1km,1.15km,1.2km,1.25km,1.3km,1.35km,1.4km,1.45km,1.5km,1.55km,1.6km,1.65km,1.7km,1.75km,1.8km,1.85km,1.9km,1.95km,2km,2.25km,2.5km,2.75km,3km,3.25km,3.5km,3.75km,4km,4.25km,4.5km,4.75km,5km,5.25km,5.5km,5.75km,6km,6.25km,6.5km,6.75km,7km,7.25km,7.5km,7.75km,8km,8.25km,8.5km,8.75km,9km,9.25km,9.5km,9.75km,10km,QCflag";
    linedata = table_header.toLatin1();
    linedata.append("\r\n");

    if (file->write(linedata) == -1)
    {
        file->close();
        return false;
    }
    file->close();
    return true;
}

bool Lv1_data_storage::Insert_new_Lv1_Bright_temperature_data(Lv1_Bright_temperature_data bright_temperature)
{

    if (!file->isOpen())
    {
        if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
        {
            return false; // 如果
        }
    }

    QByteArray linedata;
    QString line = QString::number(Record_number++) + ','
        //+bright_temperature.DateTime +','
        + QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss") + ',' + Control_decimal_bit(bright_temperature.SurTem, 2, true) + ',' + Control_decimal_bit(bright_temperature.SurHum, 2, true) + ',' + Control_decimal_bit(bright_temperature.SurPre, 2, true) + ',' + Control_decimal_bit(bright_temperature.Tir, 2, true) + ',' + deal_int_data(bright_temperature.Rain) + ',' + deal_int_data(bright_temperature.QCFlag) + ',' + Control_decimal_bit(bright_temperature.Azimuth, 3, true) + ',' + Control_decimal_bit(bright_temperature.Elevation, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq1, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq2, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq3, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq4, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq5, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq6, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq7, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq8, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq9, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq10, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq11, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq12, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq13, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq14, 3, true) + ',' + deal_qstring_data(bright_temperature.QCFlag_BT);
    if (!isPureAscii(line))
    {
        file->close();
        return false;
    }
    linedata = line.toLatin1();
    linedata.append("\r\n");

    if (file->write(linedata) == -1)
    {
        file->close();
        return false;
    }
    file->close();
    return true;
}
bool Lv1_data_storage::Insert_new_Lv1_convert_lv2_data(Lv1_Bright_temperature_data bright_temperature)
{
    //lv1->lv2
    QVector<struct Lv2_Meteorological_product_data> lv2data;
    for (int i = 0; i < 3; i++)
    {
        struct Lv2_Meteorological_product_data lv2data_temp;
        lv2data_temp.Record = bright_temperature.Record;
        lv2data_temp.DateTime = bright_temperature.DateTime;
        lv2data_temp.SurTem = bright_temperature.SurTem;
        lv2data_temp.SurHum = bright_temperature.SurHum;
        lv2data_temp.SurPre = bright_temperature.SurPre;
        lv2data_temp.Tir = bright_temperature.Tir;
        lv2data_temp.Rain = bright_temperature.Rain;
        lv2data_temp.CloudBase = std::numeric_limits<double>::lowest();
        lv2data_temp.Vint = std::numeric_limits<double>::lowest();
        lv2data_temp.Lqint = std::numeric_limits<double>::lowest();
        lv2data_temp.QCflag = bright_temperature.QCFlag;
        lv2data.append(lv2data_temp);
    }
    QVector<double> ch_1_14_temp = { bright_temperature.Ch_Freq1,bright_temperature.Ch_Freq2,bright_temperature.Ch_Freq3,bright_temperature.Ch_Freq4,bright_temperature.Ch_Freq5,bright_temperature.Ch_Freq6,bright_temperature.Ch_Freq7,
                                    bright_temperature.Ch_Freq8,bright_temperature.Ch_Freq9,bright_temperature.Ch_Freq10,bright_temperature.Ch_Freq11,bright_temperature.Ch_Freq12,bright_temperature.Ch_Freq13,bright_temperature.Ch_Freq14 };
#pragma warning(suppress : 4996)
    std::vector ch_1_14 = ch_1_14_temp.toStdVector();//14通道数据
    //temp.SurHum湿度13   temp.SurPre12 气压  temp.SurTem温度11

    //算法部分代码
    ProfileInversionModel::StructRecvBusinessData data;
    data.channelBrightnessTemperature = ch_1_14;// 14通道亮温
    data.groundStationTemperature = (int16_t)(bright_temperature.SurTem * 100.0);  // 地面温度
    data.groundStationRelativeHumidity = (uint16_t)(bright_temperature.SurHum * 100.0); // 地面相对湿度
    data.groundStationPressure = (uint32_t)(bright_temperature.SurPre * 100.0);//地面气压
    vector<ProfileInversionModel::StructRecvBusinessData> inputList = { data };


    ProfileInversionModel::RelativeHumidityProfile processor;  // 相对湿度
    auto results1 = processor.readDate(inputList);   // 反演廓线

    ProfileInversionModel::TemperatureProfile sadas;
    auto results2 = sadas.readDate(inputList);   // 反演廓线

    ProfileInversionModel::WaterVaporDensityProfile WaterVaporDensity;
    auto results3 = WaterVaporDensity.readDate(inputList);   // 反演廓线

    //温度数据
    std::vector<double> ch83_SurTem = results2[0];
    if (ch83_SurTem.size() > 83)
    {
        for (int i = 0; i < 83; i++)
            lv2data[0].H[i] = ch83_SurTem[i];
    }
    else
    {
        for (int i = 0; i < ch83_SurTem.size(); i++)
            lv2data[0].H[i] = ch83_SurTem[i];
    }
    lv2data[0].DataType = 11;
    //水汽数据
    std::vector<double> ch83_SurPre = results3[0];
    if (ch83_SurPre.size() > 83)
    {
        for (int i = 0; i < 83; i++)
            lv2data[1].H[i] = ch83_SurPre[i];
    }
    else
    {
        for (int i = 0; i < ch83_SurPre.size(); i++)
            lv2data[1].H[i] = ch83_SurPre[i];
    }
    lv2data[1].DataType = 12;
    //湿度数据
    std::vector<double> ch83_SurHum = results1[0];
    if (ch83_SurHum.size() > 83)
    {
        for (int i = 0; i < 83; i++)
            lv2data[2].H[i] = ch83_SurHum[i];
    }
    else
    {
        for (int i = 0; i < ch83_SurHum.size(); i++)
            lv2data[2].H[i] = ch83_SurHum[i];
    }
    lv2data[2].DataType = 13;


    if (!file->isOpen())
    {
        if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
        {
            return false; // 如果
        }
    }

    QByteArray linedata;
    for (int i = 0; i < lv2data.count(); ++i)
    {
        linedata.clear();
        QString header = QString::number(Record_number++) + ',' + QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss") + ',' + deal_int_data(lv2data[i].DataType) + ',' + Control_decimal_bit(lv2data[i].SurTem, 2, true) + ',' + Control_decimal_bit(lv2data[i].SurHum, 2, true) + ',' + Control_decimal_bit(lv2data[i].SurPre, 2, true) + ',' + Control_decimal_bit(lv2data[i].Tir, 2, true) + ',' + deal_int_data(lv2data[i].Rain) + ',' + Control_decimal_bit(lv2data[i].CloudBase, 2, true) + ',' + Control_decimal_bit(lv2data[i].Vint, 2, true) + ',' + Control_decimal_bit(lv2data[i].Lqint, 2, true) + ',';
        QString h_data;
        for (int n = 0; n < 83; n++)
        {
            h_data += Control_decimal_bit(lv2data[i].H[n], 3, true) + ',';
        }

        QString line = header + h_data + deal_int_data(lv2data[i].QCflag);

        if (!isPureAscii(line))
        {
            file->close();
            return false;
        }
        linedata = line.toLatin1();
        linedata.append("\r\n");

        if (file->write(linedata) == -1)
        {
            file->close();
            return false;
        }
    }
    file->close();
    return true;
}

bool Lv1_data_storage::Generate_new_Lv1_Minutes_file(Lv_Basic_parameters basic_parameters, Lv1_Bright_temperature_data bright_temperature)
{
    // 创建目录结构

    QString filepath_dir;
    if (use_beijing_utc)
        filepath_dir = filepath + QDateTime::currentDateTime().toString("yyyy-MM-dd") + "/";
    else
        filepath_dir = filepath + QDateTime::currentDateTimeUtc().toString("yyyy-MM-dd") + "/";

    QDir dir(filepath_dir);
    if (!dir.exists())
        dir.mkpath(filepath_dir);

    struct FILENAME temp = filename;
    temp.pf1ag = "Z";
    temp.productidentifier = "UPAR";
    if (use_beijing_utc)
        temp.yyyyMMddhhmmss = QDateTime::currentDateTime().toString("yyyyMMddHHmmss");
    else
        temp.yyyyMMddhhmmss = QDateTime::currentDateTimeUtc().toString("yyyyMMddHHmmss");
    temp.deviceidentification = "YMWR";
    temp.frequency = "M";
    temp.type = "txt";
    temp.datatype = "RAW";
    temp.ftype = "O";
    QFile* file = new QFile(filepath_dir + Generate_file_name(temp));
    if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
    {
        return false;
    }
    Record_number = 1;

    QByteArray linedata;
    QString line1 = basic_parameters.MWR + ',' + Control_decimal_bit(basic_parameters.Data_format_version_number, 2, true);
    if (!isPureAscii(line1))
    {
        file->close();
        return false;
    }
    linedata = line1.toLatin1();
    linedata.append("\r\n");

    if (file->write(linedata) == -1)
    {
        file->close();
        return false;
    }

    linedata.clear();
    QString line2 = basic_parameters.Station_number + ',' + Control_decimal_bit(basic_parameters.longitude, 4, true) + ',' + Control_decimal_bit(basic_parameters.dimension, 4, true) + ',' + Control_decimal_bit(basic_parameters.altitude, 1, true) + ',' + basic_parameters.Equipment_type + ',' + Control_decimal_bit(basic_parameters.dimension, 0, true) + ',' + Control_decimal_bit(basic_parameters.altitude, 0, true);
    if (!isPureAscii(line2))
    {
        file->close();
        return false;
    }
    linedata = line2.toLatin1();
    linedata.append("\r\n");

    if (file->write(linedata) == -1)
    {
        file->close();
        return false;
    }

    linedata.clear();
    //QString table_header = "Record,DateTime,SurTem(℃),SurHum(%),SurPre(hPa),Tir(℃),Rain,QCFlag,Az(deg),El(deg),Ch 22.24,Ch 23.04,Ch 23.84,Ch 25.440,Ch 26.240,Ch 27.840,Ch 31.400,Ch 51.260,Ch 52.280,Ch 53.860,Ch 54.940,Ch 56.660,Ch 57.300,Ch 58.800,QCFlag_BT";
    QString table_header = "Record,DateTime,10,SurTem(℃),SurHum(%),SurPre(hPa),Tir(℃),Rain,CloudBase(km),Vint(mm),Lqint(mm),0km,0.025km,0.05km,0.075km,0.1km,0.125km,0.15km,0.175km,0.2km,0.225km,0.25km,0.275km,0.3km,0.325km,0.35km,0.375km,0.4km,0.425km,0.45km,0.475km,0.5km,0.55km,0.6km,0.65km,0.7km,0.75km,0.8km,0.85km,0.9km,0.95km,1km,1.05km,1.1km,1.15km,1.2km,1.25km,1.3km,1.35km,1.4km,1.45km,1.5km,1.55km,1.6km,1.65km,1.7km,1.75km,1.8km,1.85km,1.9km,1.95km,2km,2.25km,2.5km,2.75km,3km,3.25km,3.5km,3.75km,4km,4.25km,4.5km,4.75km,5km,5.25km,5.5km,5.75km,6km,6.25km,6.5km,6.75km,7km,7.25km,7.5km,7.75km,8km,8.25km,8.5km,8.75km,9km,9.25km,9.5km,9.75km,10km,QCflag";
    linedata = table_header.toLatin1();
    linedata.append("\r\n");

    if (file->write(linedata) == -1)
    {
        file->close();
        return false;
    }

    linedata.clear();
    QString line = QString::number(Record_number++) + ','
        //+bright_temperature.DateTime +','
        + QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss") + ',' + Control_decimal_bit(bright_temperature.SurTem, 2, true) + ',' + Control_decimal_bit(bright_temperature.SurHum, 2, true) + ',' + Control_decimal_bit(bright_temperature.SurPre, 2, true) + ',' + Control_decimal_bit(bright_temperature.Tir, 2, true) + ',' + deal_int_data(bright_temperature.Rain) + ',' + deal_int_data(bright_temperature.QCFlag) + ',' + Control_decimal_bit(bright_temperature.Azimuth, 3, true) + ',' + Control_decimal_bit(bright_temperature.Elevation, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq1, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq2, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq3, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq4, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq5, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq6, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq7, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq8, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq9, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq10, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq11, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq12, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq13, 3, true) + ',' + Control_decimal_bit(bright_temperature.Ch_Freq14, 3, true) + ',' + deal_qstring_data(bright_temperature.QCFlag_BT);
    if (!isPureAscii(line))
    {
        file->close();
        return false;
    }
    linedata = line.toLatin1();
    linedata.append("\r\n");

    if (file->write(linedata) == -1)
    {
        file->close();
        return false;
    }
    file->close();
    return true;
}

void Lv1_data_storage::set_file_path(QString path)
{
    filepath = path;
}

void Lv1_data_storage::set_file_name(FILENAME name)
{
    filename = name;
}

void Lv1_data_storage::set_Minutes_file_en(bool en)
{
    Minutes_file_en = en;
}

void Lv1_data_storage::set_Day_file_en(bool en)
{
    Day_file_en = en;
}

void Lv1_data_storage::set_beijing_or_utc(bool choose)
{
    use_beijing_utc = choose;
}

void Lv1_data_storage::save_data(Lv_Basic_parameters basic_parameters, Lv1_Bright_temperature_data bright_temperature)
{
    if (Day_file_en)
    {
        bool re = Generate_new_Lv1_Bright_temperature_data(basic_parameters, bright_temperature);
        if (re)
        {
            qDebug() << "Lv1 day succeed";
        }
        else
        {
            qDebug() << "Lv1 day failure";
        }
    }

    if (Minutes_file_en)
    {
        bool re = Generate_new_Lv1_Minutes_file(basic_parameters, bright_temperature);
        if (re)
        {
            qDebug() << "Lv1 Minutes succeed";
        }
        else
        {
            qDebug() << "Lv1 Minutes failure";
        }
    }
}

QString Lv1_data_storage::Generate_file_name(FILENAME file_name)
{
    return file_name.pf1ag + "_" + file_name.productidentifier + "_" + file_name.of1ag + "_" + file_name.originator + "_" + file_name.yyyyMMddhhmmss + "_" + file_name.ftype + "_" + file_name.deviceidentification + "_" + file_name.equipmenttype + "_" + file_name.datatype + "_" + file_name.frequency + "." + file_name.type;
}

bool Lv1_data_storage::isPureAscii(const QString& str)
{
    for (const QChar& ch : str)
    {
        if (ch.unicode() > 0x7f) // ASCII范围0x00~0x7f
        {
            return false;
        }
    }
    return true;
}

QString Lv1_data_storage::Control_decimal_bit(double data, int bit, bool status)//double 类型
{
    if (data == std::numeric_limits<double>::lowest())
    {
        return "-";
    }
    if (status)
    {
        return QString::number(data, 'f', bit);
    }
    else
    {
        double temp = std::trunc(data * std::pow(10, bit)) / std::pow(10, bit);
        return QString::number(temp, 'f', bit);
    }
}

QString Lv1_data_storage::deal_int_data(int data)//int 类型
{
    if (data == std::numeric_limits<int>::lowest())
    {
        return "-";
    }
    return QString::number(data);
}

QString Lv1_data_storage::deal_qstring_data(QString data)//QString 类型
{
    if (data.isEmpty())
        return "-";
    return data;
}

bool Lv1_data_storage::comparefilenames(const QString& filename1, const QString& filename2)
{
    QString processedbasename1, extension1;
    QString processedbasename2, extension2;
    bool time1valid, time2valid;

    bool valid1 = processFileName(filename1, time1valid, processedbasename1, extension1);
    bool valid2 = processFileName(filename2, time2valid, processedbasename2, extension2);

    if (!valid1 || !valid2 || !time1valid || !time2valid)
    {
        return false;
    }
    if (extension1 != extension2)
    {
        return false;
    }

    return (processedbasename1 == processedbasename2);
}

bool Lv1_data_storage::processFileName(const QString& filename, bool& timevalid, QString& processedbasename, QString& extension)
{
    QFileInfo fileinfo(filename);
    QString basename = fileinfo.baseName();
    extension = fileinfo.suffix();

    QStringList parts = basename.split('_');
    if (parts.size() < 5)
    {
        timevalid = false;
        return false;
    }

    // 获取时间字段
    QString timestr = parts.value(4);
    if (timestr.length() != 14)
    {
        timevalid = false;
        return false;
    }

    // 验证时间合法性
    QDateTime dt = QDateTime::fromString(timestr, "yyyyMMddHHmmss");
    if (!dt.isValid())
    {
        timevalid = false;
        return false;
    }
    timevalid = true;

    // 替换时分秒为000000
    QString newtime = timestr.left(8) + "000000";
    parts[4] = newtime;
    processedbasename = parts.join("_");
    return true;
}
