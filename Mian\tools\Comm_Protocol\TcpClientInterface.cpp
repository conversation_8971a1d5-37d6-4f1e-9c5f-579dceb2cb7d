﻿#include "TcpClientInterface.h"
#include <QHostAddress>
#include <QDebug>

TcpClientInterface::TcpClientInterface(QObject *parent)
    : QObject(parent), m_tcpSocket(nullptr)
{
    m_tcpSocket = new QTcpSocket(this);
    connect(m_tcpSocket, &QTcpSocket::connected, this, &TcpClientInterface::handleConnected);
    connect(m_tcpSocket, &QTcpSocket::disconnected, this, &TcpClientInterface::handleDisconnected);
    connect(m_tcpSocket, &QTcpSocket::readyRead, this, &TcpClientInterface::handleReadyRead);
    connect(m_tcpSocket, SIGNAL(error(QAbstractSocket::SocketError)),
            this, SLOT(handleError(QAbstractSocket::SocketError)));
}

TcpClientInterface::~TcpClientInterface()
{
    disconnectFromHost();
    delete m_tcpSocket;
}

bool TcpClientInterface::connectToHost(const QString &host, quint16 port)
{
    if (m_tcpSocket->state() != QAbstractSocket::UnconnectedState)
    {
        m_tcpSocket->abort();
    }

    m_tcpSocket->connectToHost(host, port);
    if (!m_tcpSocket->waitForConnected(100))
    {
        emit errorOccurred(tr("连接服务器失败: %1").arg(m_tcpSocket->errorString()));
        return false;
    }

    return true;
}

void TcpClientInterface::disconnectFromHost()
{
    if (m_tcpSocket->state() != QAbstractSocket::UnconnectedState)
    {
        m_tcpSocket->disconnectFromHost();
        if (m_tcpSocket->state() != QAbstractSocket::UnconnectedState)
        {
            m_tcpSocket->waitForDisconnected(1000);
        }
    }
}

bool TcpClientInterface::isConnected() const
{
    return m_tcpSocket->state() == QAbstractSocket::ConnectedState;
}

qint64 TcpClientInterface::sendData(const QByteArray &data)
{
    if (!isConnected())
    {
        emit errorOccurred(tr("未连接到服务器"));
        return -1;
    }

    if (data.isEmpty())
    {
        emit errorOccurred(tr("发送数据为空"));
        return 0;
    }

    qint64 bytesWritten = m_tcpSocket->write(data);
    if (bytesWritten == -1)
    {
        emit errorOccurred(tr("发送数据失败: %1").arg(m_tcpSocket->errorString()));
    }
    else if (bytesWritten < data.size())
    {
        emit errorOccurred(tr("发送数据不完整: %1/%2 字节")
                               .arg(bytesWritten)
                               .arg(data.size()));
    }
    else if (!m_tcpSocket->waitForBytesWritten(1000))
    {
        emit errorOccurred(tr("发送数据超时"));
    }

    return bytesWritten;
}

QByteArray TcpClientInterface::readData(int timeout)
{
    if (!isConnected())
    {
        emit errorOccurred(tr("未连接到服务器"));
        return QByteArray();
    }

    if (timeout > 0 && !m_tcpSocket->waitForReadyRead(timeout))
    {
        return QByteArray();
    }

    return m_tcpSocket->readAll();
}

void TcpClientInterface::handleConnected()
{
    emit connected();
}

void TcpClientInterface::handleDisconnected()
{
    emit disconnected();
}

void TcpClientInterface::handleReadyRead()
{
    QByteArray data = m_tcpSocket->readAll();
    if (!data.isEmpty())
    {
        emit dataReceived(data);
    }
}

void TcpClientInterface::handleError(QAbstractSocket::SocketError error)
{
    QString errorStr;
    switch (error)
    {
    case QAbstractSocket::ConnectionRefusedError:
        errorStr = tr("连接被拒绝");
        break;
    case QAbstractSocket::RemoteHostClosedError:
        errorStr = tr("远程主机关闭连接");
        break;
    case QAbstractSocket::HostNotFoundError:
        errorStr = tr("主机未找到");
        break;
    case QAbstractSocket::SocketAccessError:
        errorStr = tr("套接字访问错误");
        break;
    case QAbstractSocket::SocketResourceError:
        errorStr = tr("套接字资源错误");
        break;
    case QAbstractSocket::SocketTimeoutError:
        errorStr = tr("套接字操作超时");
        break;
    case QAbstractSocket::NetworkError:
        errorStr = tr("网络错误");
        break;
    case QAbstractSocket::UnsupportedSocketOperationError:
        errorStr = tr("不支持的套接字操作");
        break;
    case QAbstractSocket::UnknownSocketError:
    default:
        errorStr = tr("未知套接字错误");
        break;
    }

    emit errorOccurred(errorStr);
}