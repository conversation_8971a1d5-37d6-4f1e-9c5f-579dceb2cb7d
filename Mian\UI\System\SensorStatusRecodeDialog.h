﻿#ifndef SENSORSTATUSRECODEDIALOG_H
#define SENSORSTATUSRECODEDIALOG_H

#include <QDialog>
#include <QLineEdit>
namespace Ui {
    class SensorStatusRecodeDialog;
}

class SensorStatusRecodeDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SensorStatusRecodeDialog(QWidget* parent = nullptr);
    ~SensorStatusRecodeDialog();

private slots:

    void on_pushButton_query_clicked();
    void slot_auto_delect_data();

private:
    Ui::SensorStatusRecodeDialog* ui;
    QTimer* auto_delect_data;
};

#endif // SENSORSTATUSRECODEDIALOG_H
