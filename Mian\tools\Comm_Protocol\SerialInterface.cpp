﻿#include "SerialInterface.h"
#include <QDebug>

SerialInterface::SerialInterface(QObject *parent)
    : QObject(parent), m_serialPort(nullptr)
{
    m_serialPort = new QSerialPort();
    // 连接串口信号和槽
    connect(m_serialPort, &QSerialPort::readyRead, this, &SerialInterface::handleReadyRead);
    connect(m_serialPort, &QSerialPort::errorOccurred, this, &SerialInterface::handleError);
}

SerialInterface::~SerialInterface()
{
    if (m_serialPort)
    {
        if (m_serialPort->isOpen())
        {
            m_serialPort->close();
        }
        disconnect(m_serialPort, nullptr, this, nullptr);
        delete m_serialPort;
        m_serialPort = nullptr;
    }
}

bool SerialInterface::openPort(const QString &portName,
                               QSerialPort::BaudRate baudRate,
                               QSerialPort::DataBits dataBits,
                               QSerialPort::Parity parity,
                               QSerialPort::StopBits stopBits,
                               QSerialPort::FlowControl flowControl)
{
    if (m_serialPort->isOpen())
    {
        m_serialPort->close();
    }

    m_serialPort->setPortName(portName);
    m_serialPort->setBaudRate(baudRate);
    m_serialPort->setDataBits(dataBits);
    m_serialPort->setParity(parity);
    m_serialPort->setStopBits(stopBits);
    m_serialPort->setFlowControl(flowControl);

    if (!m_serialPort->open(QIODevice::ReadWrite))
    {
        emit errorOccurred(tr("无法打开串口 %1: %2")
                               .arg(portName)
                               .arg(m_serialPort->errorString()));
        return false;
    }

    return true;
}

void SerialInterface::closePort()
{
    if (m_serialPort->isOpen())
    {
        m_serialPort->close();
    }
}

bool SerialInterface::isOpen() const
{
    return m_serialPort->isOpen();
}

qint64 SerialInterface::writeData(const QByteArray &data)
{
    if (!m_serialPort->isOpen())
    {
        emit errorOccurred(tr("串口未打开"));
        return -1;
    }

    if (data.isEmpty())
    {
        emit errorOccurred(tr("写入数据为空"));
        return 0;
    }

    qint64 bytesWritten = m_serialPort->write(data);
    if (bytesWritten == -1)
    {
        emit errorOccurred(tr("写入串口失败: %1")
                               .arg(m_serialPort->errorString()));
    }
    else if (bytesWritten < data.size())
    {
        emit errorOccurred(tr("写入数据不完整: %1/%2 字节")
                               .arg(bytesWritten)
                               .arg(data.size()));
    }
    else if (!m_serialPort->waitForBytesWritten(1000))
    {
        emit errorOccurred(tr("写入串口超时"));
    }

    return bytesWritten;
}

QByteArray SerialInterface::readData(int timeout)
{
    if (!m_serialPort->isOpen())
    {
        emit errorOccurred(tr("串口未打开"));
        return QByteArray();
    }

    if (timeout > 0 && !m_serialPort->waitForReadyRead(timeout))
    {
        return QByteArray();
    }

    return m_serialPort->readAll();
}

QStringList SerialInterface::availablePorts()
{
    QStringList ports;
    foreach (const QSerialPortInfo &info, QSerialPortInfo::availablePorts())
    {
        ports << info.portName();
    }
    return ports;
}

void SerialInterface::handleReadyRead()
{
    QByteArray data = m_serialPort->readAll();
    if (!data.isEmpty())
    {
        emit dataReceived(data);
    }
}

void SerialInterface::handleError(QSerialPort::SerialPortError error)
{
    if (error == QSerialPort::NoError)
    {
        return;
    }

    QString errorStr;
    switch (error)
    {
    case QSerialPort::DeviceNotFoundError:
        errorStr = tr("设备未找到");
        break;
    case QSerialPort::PermissionError:
        errorStr = tr("权限不足");
        break;
    case QSerialPort::OpenError:
        errorStr = tr("设备已打开");
        break;
    case QSerialPort::NotOpenError:
        errorStr = tr("设备未打开");
        break;
    case QSerialPort::WriteError:
        errorStr = tr("写入错误");
        break;
    case QSerialPort::ReadError:
        errorStr = tr("读取错误");
        break;
    case QSerialPort::ResourceError:
        errorStr = tr("资源错误");
        break;
    case QSerialPort::UnsupportedOperationError:
        errorStr = tr("不支持的操作");
        break;
    case QSerialPort::TimeoutError:
        errorStr = tr("操作超时");
        break;
    case QSerialPort::UnknownError:
    default:
        errorStr = tr("未知错误");
        break;
    }

    emit errorOccurred(errorStr);
}