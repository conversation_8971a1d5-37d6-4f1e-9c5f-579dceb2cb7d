﻿/*主界面样式表*/
MainWindow QWidget#widget
{background-color: rgb(87, 106, 140);}

MainWindow QMenuBar
{color:black;background-color:rgb(207, 214, 229);}
MainWindow QMenuBar::item:selected
{background-color:rgb(253, 244, 191);}
MainWindow QMenuBar::item:pressed
{background-color:rgb(255, 242, 157);}

MainWindow QMenu
{color:black;background-color:rgb(207, 214, 229);;}
MainWindow QMenu::item:selected
{background-color:rgb(255, 242, 157)}

MainWindow QToolBar
{background-color:rgb(207, 214, 229);border:rgb(207, 214, 229);}
MainWindow QToolBar::separator
{width:2px;background:rgb(207, 214, 229);}

MainWindow QToolBar QToolButton
{background-color:rgb(207, 214, 229);border:rgb(207, 214, 229);}
MainWindow QToolBar#toolBar::separator
{width:2px;background:rgb(207, 214, 229);}

MainWindow QToolBar QToolButton:hover
{background-color:rgb(253, 244, 191);}

MainWindow QLable
{color:black;}
MainWindow QStatusBar
{
background-color: rgb(0, 170, 255);
}

MainWindow QToolBar
{
spacing:10px;
}
HandCalibrationDialog
{background-color: rgb(87, 106, 140);}
HandCalibrationDialog QPushButton
{background-color: rgb(87, 106, 140);color:black;border: 2px solid #ffffff;}
HandCalibrationDialog QLabel
{color: rgb(0, 0, 0);}

HandCalibrationDialog QGroupBox
{color: rgb(255, 255, 0);}

/*FTP数据样式表*/
FTPDataDialog
{background-color: rgb(87, 106, 140);}
FTPDataDialog QPushButton
{background-color: rgb(87, 106, 140);color:black;border: 2px solid #ffffff;}
HandCalibrationDialog QLabel
{color: rgb(0, 0, 0);}
FTPDataDialog QLabel
{color:black;}
FTPDataDialog QGroupBox
{color: rgb(0, 0, 0);}
FTPDataDialog QCheckBox
{color: rgb(0, 0, 0);}
FTPDataDialog QLineEdit
{background-color:rgb(87, 106, 140);border:none;color:black;}


FTPDialog
{background-color: rgb(87, 106, 140);}
FTPDialog QPushButton
{background-color: rgb(87, 106, 140);color:;border: 2px solid #ffffff;}
FTPDialog QTableView
{background-color:rgb(87, 106, 140);color:black;}
FTPDialog QTableView:item
{background-color:rgb(87, 106, 140);color:black;}
FTPDialog QTableView:item:selected
{background-color:rgb(87, 106, 140);color:black;}
FTPDialog QHeaderView::section
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
FTPDialog QHeaderView::section:vertical
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
FTPDialog QTableCornerButton::section
{background-color:rgb(87, 106, 140);border: 2px solid;}

/*网络连接样式表*/
classWidgetComm
{background-color: rgb(87, 106, 140);}
classWidgetComm QPushButton
{background-color: rgb(87, 106, 140);color:black;border: 2px solid #ffffff;}
classWidgetComm QTextEdit
{background-color: rgb(87, 106, 140);color:;}

/*系统定标*/
ControlSetParameterDialog
{background-color: rgb(87, 106, 140);}
ControlSetParameterDialog QPushButton
{background-color: rgb(87, 106, 140);color:white;border: 2px solid #ffffff;}
ControlSetParameterDialog QLabel
{color: rgb(0, 0, 0);}
ControlSetParameterDialog QTableWidget
{background-color:rgb(87, 106, 140);color:black;}
ControlSetParameterDialog QTableWidget:item
{background-color:rgb(87, 106, 140);color:black;}
ControlSetParameterDialog QTableWidget:item:selected
{background-color:rgb(87, 106, 140);color:black;}
ControlSetParameterDialog QHeaderView::section
{background-color:rgb(87, 106, 140);color:black;}
ControlSetParameterDialog QHeaderView::section:vertical
{background-color:rgb(87, 106, 140);color:black;}
ControlSetParameterDialog QTableCornerButton::section
{background-color:rgb(87, 106, 140);border: 2px solid;}

/*参数设置*/
FMGParameterDialog
{background-color: rgb(87, 106, 140);}
FMGParameterDialog QPushButton
{background-color: rgb(87, 106, 140);color:black;border: 2px solid #ffffff;}
FMGParameterDialog QLabel
{color: rgb(0, 0, 0);}
FMGParameterDialog QGroupBox
{color: black;}
FMGParameterDialog QTableWidget
{background-color:rgb(87, 106, 140);color:black;}
FMGParameterDialog QTableWidget:item
{background-color:rgb(87, 106, 140);color:black;}
FMGParameterDialog QTableWidget:item:selected
{background-color:rgb(87, 106, 140);color:black;}
FMGParameterDialog QHeaderView::section
{background-color:rgb(87, 106, 140);color:black;}
FMGParameterDialog QHeaderView::section:vertical
{background-color:rgb(87, 106, 140);color:black;}
FMGParameterDialog QTableCornerButton::section
{background-color:rgb(87, 106, 140);border: 2px solid;}
FMGParameterDialog QStackedWidget
{background-color: rgb(87, 106, 140);}
FMGParameterDialog QCheckBox
{color: black;}

/*故障管理*/
BreakDownDialog
{background-color:rgb(87, 106, 140);}
BreakDownDialog QLabel
{color: rgb(0, 0, 0);}
BreakDownDialog QPushButton
{background-color:rgb(87, 106, 140);color:white;border:2px solid #ffffff;}
BreakDownDialog QLineEdit
{background-color:rgb(87, 106, 140);border:none;color:;}
BreakDownDialog QStackedWidget
{background-color:rgb(87, 106, 140);border:2px solid #ffffff;}
BreakDownDialog QTableView
{background-color:rgb(87, 106, 140);color:black;}
BreakDownDialog QTableView:item
{background-color:rgb(87, 106, 140);color:black;}
BreakDownDialog QTableView:item:selected
{background-color:rgb(87, 106, 140);color:black;}
BreakDownDialog QHeaderView::section
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
BreakDownDialog QHeaderView::section:vertical
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
BreakDownDialog QTableCornerButton::section
{background-color:rgb(87, 106, 140);border: 2px solid;}


/*上传监控*/
UploadMonitorDialog
{background-color:rgb(87, 106, 140);}
UploadMonitorDialog QLabel
{color: rgb(0, 0, 0);}
UploadMonitorDialog QPushButton
{background-color:rgb(87, 106, 140);color:white;border:2px solid #ffffff;}
UploadMonitorDialog QLineEdit
{background-color:rgb(226, 226, 226);border:none;color:black;}
UploadMonitorDialog QStackedWidget
{background-color:rgb(87, 106, 140);border:2px solid #ffffff;}
UploadMonitorDialog QTableView
{background-color:rgb(87, 106, 140);color:black;}
UploadMonitorDialog QTableView:item
{background-color:rgb(87, 106, 140);color:black;}
UploadMonitorDialog QTableView:item:selected
{background-color:rgb(87, 106, 140);color:white;}
UploadMonitorDialog QHeaderView::section
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
UploadMonitorDialog QHeaderView::section:vertical
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
UploadMonitorDialog QTableCornerButton::section
{background-color:rgb(87, 106, 140);border: 2px solid;}

/*传感器状态界面*/
SensorStatusRecodeDialog
{background-color:rgb(87, 106, 140);}
SensorStatusRecodeDialog QLabel
{color: rgb(0, 0, 0);}
SensorStatusRecodeDialog QPushButton
{background-color:rgb(87, 106, 140);color:white;border:2px solid #ffffff;}
SensorStatusRecodeDialog QLineEdit
{background-color:rgb(226, 226, 226);border:none;color:black;}
SensorStatusRecodeDialog QStackedWidget
{background-color:rgb(87, 106, 140);border:2px solid #ffffff;}
SensorStatusRecodeDialog QTableView
{background-color:rgb(87, 106, 140);color:black;}
SensorStatusRecodeDialog QTableView:item
{background-color:rgb(87, 106, 140);color:black;}
SensorStatusRecodeDialog QTableView:item:selected
{background-color:rgb(87, 106, 140);color:black;}
SensorStatusRecodeDialog QHeaderView::section
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
SensorStatusRecodeDialog QHeaderView::section:vertical
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
SensorStatusRecodeDialog QTableCornerButton::section
{background-color:rgb(87, 106, 140);border: 2px solid;}

/*登录界面*/
SystemRegister
{background-color:rgb(87, 106, 140);}
SystemRegister QLabel
{color: rgb(0, 0, 0);}
SystemRegister QPushButton
{background-color:rgb(87, 106, 140);color:black;border:2px solid #ffffff;}
SystemRegister QLineEdit
{background-color:rgb(226, 226, 226);border:none;color:black;}



/*历史界面样式表*/
HistoryDialog QTreeWidget
{background-color: rgb(87, 106, 140);color:black;}
HistoryDialog QTreeWidget QHeaderView::section
{background-color: rgb(87, 106, 140);color:black;}
HistoryDialog QTreeWidget:item:selected
{background-color:rgb(0, 170, 255);}

HistoryDialog QTreeWidget::branch:open:has-children
{
image:url(:/VectorIcon/downarrows.png);
}
HistoryDialog QTreeWidget::branch:closed:has-children
{
image:url(:/VectorIcon/stop7.png);
}

HistoryDialog
{background-color: rgb(87, 106, 140);}

/*传感器界面样式表*/
SensorStatusDialog
{background-color: rgb(87, 106, 140);}
SensorStatusDialog QTabWidget
{background-color:rgb(87, 106, 140);color:black;}
SensorStatusDialog QWidget
{background-color:rgb(87, 106, 140);color:black;}
SensorStatusDialog QLable
{background-color:rgb(87, 106, 140);color:black;}
SensorStatusDialog QTabBar::tab
{background-color: rgb(87, 106, 140);color:white;border: 2px solid #ffffff;}
SensorStatusDialog QTabBar::tab:selected
{background-color: rgb(87, 106, 140);color:white;border: 2px solid #ffffff;}

/*日志界面样式表*/
LogDialog
{background-color: rgb(87, 106, 140);}
LogDialog QTableView
{background-color:rgb(87, 106, 140);}
LogDialog QTableView:item
{background-color:rgb(87, 106, 140);}
LogDialog QTableView:item:selected
{background-color:rgb(87, 106, 140);}

LogDialog QHeaderView::section
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}

LogDialog QHeaderView::section:vertical
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
LogDialog QTableCornerButton::section
{background-color:rgb(87, 106, 140);border: 2px solid;}

/*日常维护界面样式表*/
DailyMaintenanceDialog
{background-color: rgb(87, 106, 140);}
DailyMaintenanceDialog QLabel
{color: rgb(0, 0, 0);}
DailyMaintenanceDialog QGroupBox
{color: rgb(0, 0, 0);background-color: rgb(87, 106, 140);}
DailyMaintenanceDialog QPushButton
{background-color: rgb(87, 106, 140);color:white;border: 2px solid #ffffff;}
DailyMaintenanceDialog QLineEdit
{background-color: rgb(226, 226, 226);border:none;color:black;}
DailyMaintenanceDialog#groupBox_maintain_message
{color: rgb(0, 0, 0);background-color: rgb(87, 106, 140);}

/*系统维护界面样式表*/
SystemMaintenanceDialog
{background-color:rgb(87, 106, 140);}
SystemMaintenanceDialog QLabel
{color:rgb(0, 0, 0);}
SystemMaintenanceDialog QPushButton
{background-color:rgb(87, 106, 140);color:white;border:2px solid #ffffff;}
SystemMaintenanceDialog QLineEdit
{background-color:rgb(226, 226, 226);border:none;color:black;}
SystemMaintenanceDialog QTextEdit
{background-color:rgb(226, 226, 226);border:none;color:black;}
SystemMaintenanceDialog QStackedWidget
{background-color:rgb(87, 106, 140);border:2px solid #ffffff;}

SystemMaintenanceDialog QTableView
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
SystemMaintenanceDialog QTableView:item
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
SystemMaintenanceDialog QTableView:item:selected
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
SystemMaintenanceDialog QHeaderView::section
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
SystemMaintenanceDialog QHeaderView::section:vertical
{background-color:rgb(87, 106, 140);color:rgb(0,0,0);}
SystemMaintenanceDialog QTableCornerButton::section
{background-color:rgb(87, 106, 140);border: 2px solid;}


/*系统参数配置界面样式表*/
SystemConfigurationDialog
{background-color: rgb(74, 92, 124);}
SystemConfigurationDialog QLabel
{color: rgb(0, 0, 0);}
SystemConfigurationDialog QGroupBox
{color: rgb(0, 0, 0);}
SystemConfigurationDialog QPushButton
{background-color: rgb(49, 68, 102);color:white;border: 2px solid #ffffff;}
SystemConfigurationDialog QStackedWidget
{background-color: rgb(87, 106, 140);}
SystemConfigurationDialog QCheckBox
{color: rgb(0, 0, 0);}

/*数据配置路径按钮*/
SystemConfigurationDialog QPushButton#pushButton_bright_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_bright_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_bright_set::hover
{border:1px solid white;}

SystemConfigurationDialog QPushButton#pushButton_data
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_data::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_data::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_ftp_file_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_ftp_file_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_ftp_file_set::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_network_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_network_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_network_set::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_save_data_path
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_save_data_path::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_save_data_path::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_skin_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_skin_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_skin_set::hover
{border:1px solid white;}



SystemConfigurationDialog QPushButton#pushButton_bright_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_bright_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_bright_set::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_bright_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_bright_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_bright_set::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_view_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_view_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_view_set::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_station_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_station_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_station_set::hover
{border:1px solid white;}

