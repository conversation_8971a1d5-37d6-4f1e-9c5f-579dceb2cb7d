The system is: Windows - 10.0.19045 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: D:/Program Files (x86)/Microsoft Visual Studio/2019/Community/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/cl.exe 
Build flags: 
Id flags:  

The output was:
0
用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
版权所有(C) Microsoft Corporation。保留所有权利。

CMakeCCompilerId.c
Microsoft (R) Incremental Linker Version 14.29.30159.0
Copyright (C) Microsoft Corporation.  All rights reserved.

/out:CMakeCCompilerId.exe 
CMakeCCompilerId.obj 


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.exe"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.obj"

The C compiler identification is MSVC, found in "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/failtest/out/build/x64-Debug/CMakeFiles/3.20.21032501-MSVC_2/CompilerIdC/CMakeCCompilerId.exe"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: D:/Program Files (x86)/Microsoft Visual Studio/2019/Community/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/cl.exe 
Build flags: 
Id flags:  

The output was:
0
用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
版权所有(C) Microsoft Corporation。保留所有权利。

CMakeCXXCompilerId.cpp
Microsoft (R) Incremental Linker Version 14.29.30159.0
Copyright (C) Microsoft Corporation.  All rights reserved.

/out:CMakeCXXCompilerId.exe 
CMakeCXXCompilerId.obj 


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.exe"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.obj"

The CXX compiler identification is MSVC, found in "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/failtest/out/build/x64-Debug/CMakeFiles/3.20.21032501-MSVC_2/CompilerIdCXX/CMakeCXXCompilerId.exe"

Detecting C compiler ABI info compiled with the following output:
Change Dir: E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/failtest/out/build/x64-Debug/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe cmTC_ec2f5 && [1/2] Building C object CMakeFiles\cmTC_ec2f5.dir\CMakeCCompilerABI.c.obj

[2/2] Linking C executable cmTC_ec2f5.exe




Detecting CXX compiler ABI info compiled with the following output:
Change Dir: E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/failtest/out/build/x64-Debug/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe cmTC_48f5e && [1/2] Building CXX object CMakeFiles\cmTC_48f5e.dir\CMakeCXXCompilerABI.cpp.obj

[2/2] Linking CXX executable cmTC_48f5e.exe




