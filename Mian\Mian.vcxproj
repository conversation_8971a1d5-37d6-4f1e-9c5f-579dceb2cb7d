﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{BA1EC56B-9998-4507-A2E6-4E408DECF8B5}</ProjectGuid>
    <Keyword>QtVS_v304</Keyword>
    <WindowsTargetPlatformVersion Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">10.0.19041.0</WindowsTargetPlatformVersion>
    <WindowsTargetPlatformVersion Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">10.0</WindowsTargetPlatformVersion>
    <QtMsBuild Condition="'$(QtMsBuild)'=='' OR !Exists('$(QtMsBuild)\qt.targets')">$(MSBuildProjectDirectory)\QtMsBuild</QtMsBuild>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Condition="Exists('$(QtMsBuild)\qt_defaults.props')">
    <Import Project="$(QtMsBuild)\qt_defaults.props" />
  </ImportGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'" Label="QtSettings">
    <QtInstall>5.14.2_msvc2017_64</QtInstall>
    <QtModules>charts;concurrent;core;gui;network;opengl;printsupport;serialport;sql;svg;widgets;xml</QtModules>
    <QtBuildConfig>debug</QtBuildConfig>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'" Label="QtSettings">
    <QtInstall>5.14.2_msvc2017_64</QtInstall>
    <QtModules>charts;concurrent;core;gui;network;opengl;printsupport;serialport;sql;svg;widgets;xml</QtModules>
    <QtBuildConfig>release</QtBuildConfig>
  </PropertyGroup>
  <Target Name="QtMsBuildNotFound" BeforeTargets="CustomBuild;ClCompile" Condition="!Exists('$(QtMsBuild)\qt.targets') or !Exists('$(QtMsBuild)\qt.props')">
    <Message Importance="High" Text="QtMsBuild: could not locate qt.targets, qt.props; project may not build correctly." />
  </Target>
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Label="Shared" />
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(QtMsBuild)\Qt.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(QtMsBuild)\Qt.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PublicIncludeDirectories>$(ProjectDir)uic\</PublicIncludeDirectories>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PublicIncludeDirectories>$(ProjectDir)uic\</PublicIncludeDirectories>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>$(ProjectDir)include;$(ProjectDir)include\eigen-3.4.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;QT_DEPRECATED_WARNINGS;QT_DLL;QWT_DLL;ADS_STATIC;ADS_SHARED_EXPORT;QCUSTOMPLOT_USE_OPENGL;NDEBUG;QT_NO_DEBUG;WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalLibraryDirectories>$(ProjectDir)lib\freeglut\MSVC;$(ProjectDir)lib\qtadvanceddockingd\MSVC;$(ProjectDir)lib\qwt\MSVC;$(ProjectDir)lib\hdf5;$(ProjectDir)lib</AdditionalLibraryDirectories>
      <AdditionalDependencies>freeglut.lib;OpenGL32.lib;qwt.lib;hdf5.lib;hdf5_cpp.lib;ProfileInversionModel.lib;shell32.lib;libcurl.lib;libssl.lib;libcrypto.lib;ws2_32.lib;wldap32.lib;advapi32.lib;crypt32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <QtUic />
    <QtUic>
      <QtUicDir>$(ProjectDir)uic\</QtUicDir>
    </QtUic>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>$(ProjectDir)include;$(ProjectDir)include\eigen-3.4.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <AdditionalLibraryDirectories>$(ProjectDir)lib\freeglut\MSVC;$(ProjectDir)lib\qtadvanceddockingd\MSVC;$(ProjectDir)lib\qwt\MSVC;$(ProjectDir)lib\hdf5;$(ProjectDir)lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>freeglut.lib;OpenGL32.lib;qwt.lib;hdf5.lib;hdf5_cpp.lib;ProfileInversionModel.lib;shell32.lib;libcurl.lib;libssl.lib;libcrypto.lib;ws2_32.lib;wldap32.lib;advapi32.lib;crypt32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <QtUic>
      <QtUicDir>$(ProjectDir)uic\</QtUicDir>
    </QtUic>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'" Label="Configuration">
    <ClCompile>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>false</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'" Label="Configuration">
    <ClCompile>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>false</ConformanceMode>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="main.cpp" />
    <ClCompile Include="mainwindow.cpp" />
    <ClCompile Include="qcustomplot.cpp" />
    <ClCompile Include="tools\BrightCalcClass.cpp" />
    <ClCompile Include="tools\changesystimer.cpp" />
    <ClCompile Include="tools\Comm_Protocol\CommInterface.cpp" />
    <ClCompile Include="tools\Comm_Protocol\SerialInterface.cpp" />
    <ClCompile Include="tools\Comm_Protocol\TcpClientInterface.cpp" />
    <ClCompile Include="tools\Comm_Protocol\UdpInterface.cpp" />
    <ClCompile Include="tools\Configdata\CControlSetParameterData.cpp" />
    <ClCompile Include="tools\Configdata\CFMGParameterData.cpp" />
    <ClCompile Include="tools\Configdata\CSystemConfigData.cpp" />
    <ClCompile Include="tools\Configdata\printscreen.cpp" />
    <ClCompile Include="tools\Data_storage\qtsqlitedb.cpp" />
    <ClCompile Include="tools\Data_storage\sensor_status_record.cpp" />
    <ClCompile Include="tools\Data_storage\sqlite_faultrecord_data.cpp" />
    <ClCompile Include="tools\Data_storage\sqlite_ftpuploadrecord_data.cpp" />
    <ClCompile Include="tools\Data_storage\sqlite_systemmaintenance_data.cpp" />
    <ClCompile Include="tools\Data_storage\sqlite_user_data.cpp" />
    <ClCompile Include="tools\Data_storage\txt_routinemaintenance_data.cpp" />
    <ClCompile Include="tools\Data_storage\txt_staterecord_data.cpp" />
    <ClCompile Include="tools\data_treating\Calibrate_data_storage.cpp" />
    <ClCompile Include="tools\data_treating\device_status_data_storage.cpp" />
    <ClCompile Include="tools\data_treating\lv1_data_storage.cpp" />
    <ClCompile Include="tools\data_treating\lv2_data_storage.cpp" />
    <ClCompile Include="tools\data_treating\microwave_radiometer_data.cpp" />
    <ClCompile Include="tools\ftpClient\CurlHandle.cpp" />
    <ClCompile Include="tools\ftpClient\FTPClient.cpp" />
    <ClCompile Include="tools\ftpClient\FTPClientInterface.cpp" />
    <ClCompile Include="tools\ftpClient\FTPconfig.cpp" />
    <ClCompile Include="tools\Global.cpp" />
    <ClCompile Include="tools\header\ads_globals.cpp" />
    <ClCompile Include="tools\header\AutoHideDockContainer.cpp" />
    <ClCompile Include="tools\header\AutoHideSideBar.cpp" />
    <ClCompile Include="tools\header\AutoHideTab.cpp" />
    <ClCompile Include="tools\header\DockAreaTabBar.cpp" />
    <ClCompile Include="tools\header\DockAreaTitleBar.cpp" />
    <ClCompile Include="tools\header\DockAreaWidget.cpp" />
    <ClCompile Include="tools\header\DockComponentsFactory.cpp" />
    <ClCompile Include="tools\header\DockContainerWidget.cpp" />
    <ClCompile Include="tools\header\DockFocusController.cpp" />
    <ClCompile Include="tools\header\DockingStateReader.cpp" />
    <ClCompile Include="tools\header\DockManager.cpp" />
    <ClCompile Include="tools\header\DockOverlay.cpp" />
    <ClCompile Include="tools\header\DockSplitter.cpp" />
    <ClCompile Include="tools\header\DockWidget.cpp" />
    <ClCompile Include="tools\header\DockWidgetTab.cpp" />
    <ClCompile Include="tools\header\ElidingLabel.cpp" />
    <ClCompile Include="tools\header\FloatingDockContainer.cpp" />
    <ClCompile Include="tools\header\FloatingDragPreview.cpp" />
    <ClCompile Include="tools\header\IconProvider.cpp" />
    <ClCompile Include="tools\header\PushButton.cpp" />
    <ClCompile Include="tools\header\ResizeHandle.cpp" />
    <ClCompile Include="tools\ObservationController.cpp" />
    <ClCompile Include="tools\QCustomButton.cpp" />
    <ClCompile Include="tools\Qwt_image\DirectoryScanner.cpp" />
    <ClCompile Include="tools\Qwt_image\labeledspectrogram.cpp" />
    <ClCompile Include="tools\Qwt_image\plot.cpp" />
    <ClCompile Include="tools\Qwt_image\spectrogramdata.cpp" />
    <ClCompile Include="tools\Qwt_image\ThreadPool.cpp" />
    <ClCompile Include="tools\XC_DataHanding\xcProtocolDJFS.cpp" />
    <ClCompile Include="tools\XC_DataHanding\xcProtocol_DataExchange.cpp" />
    <ClCompile Include="tools\XC_DataHanding\xcSupportFunction.cpp" />
    <ClCompile Include="tools\XC_DataHanding\xcSysVar.cpp" />
    <ClCompile Include="UI\comm\classwidgetcomm.cpp" />
    <ClCompile Include="UI\ControlSetParameterDialog.cpp" />
    <ClCompile Include="UI\DialogIap.cpp" />
    <ClCompile Include="UI\flowlayout.cpp" />
    <ClCompile Include="UI\HistoryDialog.cpp" />
    <ClCompile Include="UI\LogDialog.cpp" />
    <ClCompile Include="UI\SensorStatusDialog.cpp" />
    <ClCompile Include="UI\Set\FMGParameterDialog.cpp" />
    <ClCompile Include="UI\Set\FMGWidget.cpp" />
    <ClCompile Include="UI\Set\FTPDataDialog.cpp" />
    <ClCompile Include="UI\Set\FTPDialog.cpp" />
    <ClCompile Include="UI\SystemConfiguration\CColorCardDialog.cpp" />
    <ClCompile Include="UI\SystemConfiguration\HandCalibrationDialog.cpp" />
    <ClCompile Include="UI\SystemConfiguration\SystemConfigurationDialog.cpp" />
    <ClCompile Include="UI\SystemRegister.cpp" />
    <ClCompile Include="UI\System\BreakDownDialog.cpp" />
    <ClCompile Include="UI\System\DailyMaintenanceDialog.cpp" />
    <ClCompile Include="UI\System\SensorStatusRecodeDialog.cpp" />
    <ClCompile Include="UI\System\SystemMaintenanceDialog.cpp" />
    <ClCompile Include="UI\System\UploadMonitorDialog.cpp" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="mainwindow.h" />
  </ItemGroup>
  <ItemGroup>
    <QtUic Include="mainwindow.ui" />
    <QtUic Include="UI\comm\classwidgetcomm.ui" />
    <QtUic Include="UI\ControlSetParameterDialog.ui" />
    <QtUic Include="UI\HistoryDialog.ui" />
    <QtUic Include="UI\LogDialog.ui" />
    <QtUic Include="UI\SensorStatusDialog.ui" />
    <QtUic Include="UI\Set\FMGParameterDialog.ui" />
    <QtUic Include="UI\Set\FMGWidget.ui" />
    <QtUic Include="UI\Set\FTPDataDialog.ui" />
    <QtUic Include="UI\Set\FTPDialog.ui" />
    <QtUic Include="UI\SystemConfiguration\CColorCardDialog.ui" />
    <QtUic Include="UI\SystemConfiguration\DialogButtonRight.ui" />
    <QtUic Include="UI\SystemConfiguration\HandCalibrationDialog.ui" />
    <QtUic Include="UI\SystemConfiguration\SystemConfigurationDialog.ui" />
    <QtUic Include="UI\SystemRegister.ui" />
    <QtUic Include="UI\System\BreakDownDialog.ui" />
    <QtUic Include="UI\System\DailyMaintenanceDialog.ui" />
    <QtUic Include="UI\System\SensorStatusRecodeDialog.ui" />
    <QtUic Include="UI\System\SystemMaintenanceDialog.ui" />
    <QtUic Include="UI\System\UploadMonitorDialog.ui" />
  </ItemGroup>
  <ItemGroup>
    <QtRcc Include="res.qrc" />
    <QtRcc Include="tools\header\ads.qrc" />
    <QtRcc Include="UI\Set\FMGParameterDialog.qrc" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Comm_Protocol\CommInterface.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Comm_Protocol\SerialInterface.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Comm_Protocol\TcpClientInterface.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Comm_Protocol\UdpInterface.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Configdata\CControlSetParameterData.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Configdata\CFMGParameterData.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Configdata\CSystemConfigData.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Configdata\printscreen.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Data_storage\CustomSqlTableModel.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Data_storage\largetablemodel.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Data_storage\qtsqlitedb.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Data_storage\sensor_status_record.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Data_storage\sqlite_faultrecord_data.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Data_storage\sqlite_ftpuploadrecord_data.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Data_storage\sqlite_systemmaintenance_data.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Data_storage\sqlite_user_data.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Data_storage\txt_routinemaintenance_data.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\Data_storage\txt_staterecord_data.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="tools\data_treating\Calibrate_data_storage.h" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="UI\SystemConfiguration\HandCalibrationDialog.h" />
    <QtMoc Include="UI\SystemConfiguration\CColorCardDialog.h" />
    <QtMoc Include="UI\SystemConfiguration\SystemConfigurationDialog.h" />
    <QtMoc Include="qcustomplot.h" />
    <ClInclude Include="UI\flowlayout.h" />
    <QtMoc Include="UI\SystemRegister.h" />
    <QtMoc Include="UI\SensorStatusDialog.h" />
    <QtMoc Include="UI\LogDialog.h" />
    <QtMoc Include="UI\HistoryDialog.h" />
    <QtMoc Include="UI\DialogIap.h" />
    <QtMoc Include="UI\ControlSetParameterDialog.h" />
    <QtMoc Include="UI\System\UploadMonitorDialog.h" />
    <QtMoc Include="UI\System\SystemMaintenanceDialog.h" />
    <QtMoc Include="UI\System\SensorStatusRecodeDialog.h" />
    <QtMoc Include="UI\System\DailyMaintenanceDialog.h" />
    <QtMoc Include="UI\System\BreakDownDialog.h" />
    <QtMoc Include="tools\BrightCalcClass.h" />
    <ClInclude Include="tools\Global.h" />
    <QtMoc Include="UI\Set\FTPDialog.h" />
    <QtMoc Include="UI\Set\FTPDataDialog.h" />
    <QtMoc Include="UI\Set\FMGWidget.h" />
    <QtMoc Include="UI\Set\FMGParameterDialog.h" />
    <QtMoc Include="UI\comm\classwidgetcomm.h" />
    <QtMoc Include="tools\QCustomButton.h" />
    <QtMoc Include="tools\ObservationController.h" />
    <QtMoc Include="tools\changesystimer.h" />
    <ClInclude Include="tools\data_treating\cpublic_struct.h" />
    <ClInclude Include="tools\ftpClient\CurlHandle.h" />
    <ClInclude Include="tools\ftpClient\FTPClient.h" />
    <ClInclude Include="tools\ftpClient\FTPconfig.h" />
    <ClInclude Include="tools\header\DockComponentsFactory.h" />
    <ClInclude Include="tools\header\DockingStateReader.h" />
    <ClInclude Include="tools\header\IconProvider.h" />
    <ClInclude Include="tools\Qwt_image\customfilemodel.h" />
    <ClInclude Include="tools\Qwt_image\labeledspectrogram.h" />
    <ClInclude Include="tools\Qwt_image\spectrogramdata.h" />
    <ClInclude Include="tools\XC_DataHanding\xcCtrlProcess.h" />
    <ClInclude Include="tools\XC_DataHanding\xcDefine.h" />
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralAirFan.h" />
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralAngleMotor.h" />
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralBase.h" />
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralDataBoard.h" />
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralGPS.h" />
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralInfrared.h" />
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralServer.h" />
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralTemSource.h" />
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralWeather.h" />
    <ClInclude Include="tools\XC_DataHanding\xcProtocolDJFS.h" />
    <ClInclude Include="tools\XC_DataHanding\xcProtocol_DataExchange.h" />
    <ClInclude Include="tools\XC_DataHanding\xcSupportFunction.h" />
    <ClInclude Include="tools\XC_DataHanding\xcSysVar.h" />
    <ClInclude Include="tools\XC_DataHanding\xcType.h" />
    <QtMoc Include="tools\Qwt_image\ThreadPool.h" />
    <QtMoc Include="tools\Qwt_image\plot.h" />
    <QtMoc Include="tools\Qwt_image\DirectoryScanner.h" />
    <QtMoc Include="tools\header\ResizeHandle.h" />
    <QtMoc Include="tools\header\PushButton.h" />
    <QtMoc Include="tools\header\FloatingDragPreview.h" />
    <QtMoc Include="tools\header\FloatingDockContainer.h" />
    <QtMoc Include="tools\header\ElidingLabel.h" />
    <QtMoc Include="tools\header\DockWidgetTab.h" />
    <QtMoc Include="tools\header\DockWidget.h" />
    <QtMoc Include="tools\header\DockSplitter.h" />
    <QtMoc Include="tools\header\DockOverlay.h" />
    <QtMoc Include="tools\header\DockManager.h" />
    <QtMoc Include="tools\header\DockFocusController.h" />
    <QtMoc Include="tools\header\DockContainerWidget.h" />
    <QtMoc Include="tools\header\DockAreaWidget.h" />
    <QtMoc Include="tools\header\DockAreaTitleBar_p.h" />
    <QtMoc Include="tools\header\DockAreaTitleBar.h" />
    <QtMoc Include="tools\header\DockAreaTabBar.h" />
    <QtMoc Include="tools\header\AutoHideTab.h" />
    <QtMoc Include="tools\header\AutoHideSideBar.h" />
    <QtMoc Include="tools\header\AutoHideDockContainer.h" />
    <QtMoc Include="tools\header\ads_globals.h" />
    <QtMoc Include="tools\ftpClient\FTPClientInterface.h" />
    <QtMoc Include="tools\data_treating\microwave_radiometer_data.h" />
    <QtMoc Include="tools\data_treating\lv2_data_storage.h" />
    <QtMoc Include="tools\data_treating\lv1_data_storage.h" />
    <QtMoc Include="tools\data_treating\device_status_data_storage.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Condition="Exists('$(QtMsBuild)\qt.targets')">
    <Import Project="$(QtMsBuild)\qt.targets" />
  </ImportGroup>
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>