﻿/*主界面样式表*/
MainWindow QWidget#widget
{background-color: rgb(62, 62, 62);}
/*界面菜单栏按钮样式表*/
MainWindow QMenuBar
{color:white;background-color:black;}
MainWindow QMenuBar::item:selected
{background-color:rgb(161, 161, 161)}
MainWindow QMenuBar::item:pressed
{background-color:black}
/*界面菜单栏样式表*/
MainWindow QMenu
{color:white;background-color:black;}
MainWindow QMenu::item:selected
{background-color:rgb(161, 161, 161)}
/*界面工具栏按钮样式表*/
MainWindow QToolBar
{background-color:black;border:rgb(85, 170, 255);}
MainWindow QToolBar::separator
{width:2px;background:rgb(161, 161, 161);}
/*界面菜单栏下拉按钮样式表*/
MainWindow QToolBar QToolButton
{background-color:black;border:rgb(85, 170, 255);color:white;}
MainWindow QToolBar#toolBar::separator
{width:2px;background:rgb(161, 161, 161);}

MainWindow QToolBar QToolButton:hover
{border:2px solid blue;}

MainWindow QLable
{color:white;}


MainWindow QStatusBar
{
background-color: rgb(0, 170, 255);
}

MainWindow QToolBar
{
spacing:10px;
}
/*FTP数据样式表*/
FTPDataDialog
{background-color: rgb(62, 62, 62);}
FTPDataDialog QPushButton
{background-color: rgb(62, 62, 62);color:white;border: 2px solid #ffffff;}
FTPDataDialog QLabel
{color:white;}
FTPDataDialog QGroupBox
{color: rgb(255, 255, 255);}
FTPDataDialog QCheckBox
{color: rgb(255, 255, 255);}
FTPDataDialog QLineEdit
{background-color:rgb(62, 62, 62);border:none;color:white;}


HandCalibrationDialog
{background-color: rgb(62, 62, 62);}
HandCalibrationDialog QPushButton
{background-color: rgb(62, 62, 62);color:white;border: 2px solid #ffffff;}
HandCalibrationDialog QLabel
{color: rgb(255, 255, 255);}

HandCalibrationDialog QGroupBox
{color: rgb(255, 255, 255);}


FTPDialog
{background-color: rgb(62, 62, 62);}
FTPDialog QPushButton
{background-color: rgb(62, 62, 62);color:white;border: 2px solid #ffffff;}
FTPDialog QTableView
{background-color:rgb(62, 62, 62);color:white;}
FTPDialog QTableView:item
{background-color:rgb(62, 62, 62);color:white;}
FTPDialog QTableView:item:selected
{background-color:rgb(85,170,255);color:white;}
FTPDialog QHeaderView::section
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
FTPDialog QHeaderView::section:vertical
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
FTPDialog QTableCornerButton::section
{background-color:rgb(62, 62, 62);border: 2px solid;}

/*网络连接样式表*/
classWidgetComm
{background-color: rgb(62, 62, 62);}
classWidgetComm QPushButton
{background-color: rgb(62, 62, 62);color:white;border: 2px solid #ffffff;}
classWidgetComm QTextEdit
{background-color: rgb(62, 62, 62);color:white;}

/*系统定标*/
ControlSetParameterDialog
{background-color: rgb(62, 62, 62);}
ControlSetParameterDialog QPushButton
{background-color: rgb(62, 62, 62);color:white;border: 2px solid #ffffff;}
ControlSetParameterDialog QLabel
{color: rgb(255, 255, 255);}
ControlSetParameterDialog QTableWidget
{background-color:rgb(62, 62, 62);color:white;}
ControlSetParameterDialog QTableWidget:item
{background-color:rgb(62, 62, 62);color:white;}
ControlSetParameterDialog QTableWidget:item:selected
{background-color:rgb(85,170,255);color:white;}
ControlSetParameterDialog QHeaderView::section
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
ControlSetParameterDialog QHeaderView::section:vertical
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
ControlSetParameterDialog QTableCornerButton::section
{background-color:rgb(62, 62, 62);border: 2px solid;}

/*参数设置*/
FMGParameterDialog
{background-color: rgb(62, 62, 62);}
FMGParameterDialog QPushButton
{background-color: rgb(62, 62, 62);color:white;border: 2px solid #ffffff;}
FMGParameterDialog QLabel
{color:white;}
FMGParameterDialog QGroupBox
{color: rgb(255, 255, 255);}
FMGParameterDialog QTableWidget
{background-color:rgb(62, 62, 62);color:white;}
FMGParameterDialog QTableWidget:item
{background-color:rgb(62, 62, 62);color:white;}
FMGParameterDialog QTableWidget:item:selected
{background-color:rgb(85,170,255);color:white;}
FMGParameterDialog QHeaderView::section
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
FMGParameterDialog QHeaderView::section:vertical
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
FMGParameterDialog QTableCornerButton::section
{background-color:rgb(62, 62, 62);border: 2px solid;}
FMGParameterDialog QStackedWidget
{background-color: rgb(62, 62, 62);}
FMGParameterDialog QCheckBox
{color: rgb(255, 255, 255);}

/*故障管理*/
BreakDownDialog
{background-color:rgb(55, 55, 56);}
BreakDownDialog QLabel
{color:rgb(255, 255, 255);}
BreakDownDialog QPushButton
{background-color:rgb(62, 62, 62);color:white;border:2px solid #ffffff;}
BreakDownDialog QLineEdit
{background-color:rgb(62, 62, 62);border:none;color:white;}
BreakDownDialog QStackedWidget
{background-color:rgb(55, 55, 56);border:2px solid #ffffff;}
BreakDownDialog QTableView
{background-color:rgb(62, 62, 62);color:white;}
BreakDownDialog QTableView:item
{background-color:rgb(62, 62, 62);color:white;}
BreakDownDialog QTableView:item:selected
{background-color:rgb(85,170,255);color:white;}
BreakDownDialog QHeaderView::section
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
BreakDownDialog QHeaderView::section:vertical
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
BreakDownDialog QTableCornerButton::section
{background-color:rgb(87, 106, 140);border: 2px solid;}


/*上传监控*/
UploadMonitorDialog
{background-color:rgb(55, 55, 56);}
UploadMonitorDialog QLabel
{color:rgb(255, 255, 255);}
UploadMonitorDialog QPushButton
{background-color:rgb(62, 62, 62);color:white;border:2px solid #ffffff;}
UploadMonitorDialog QLineEdit
{background-color:rgb(62, 62, 62);border:none;color:white;}
UploadMonitorDialog QStackedWidget
{background-color:rgb(55, 55, 56);border:2px solid #ffffff;}
UploadMonitorDialog QTableView
{background-color:rgb(62, 62, 62);color:white;}
UploadMonitorDialog QTableView:item
{background-color:rgb(62, 62, 62);color:white;}
UploadMonitorDialog QTableView:item:selected
{background-color:rgb(85,170,255);color:white;}
UploadMonitorDialog QHeaderView::section
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
UploadMonitorDialog QHeaderView::section:vertical
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
UploadMonitorDialog QTableCornerButton::section
{background-color:rgb(87, 106, 140);border: 2px solid;}

/*传感器状态界面*/
SensorStatusRecodeDialog
{background-color:rgb(55, 55, 56);}
SensorStatusRecodeDialog QLabel
{color:rgb(255, 255, 255);}
SensorStatusRecodeDialog QPushButton
{background-color:rgb(62, 62, 62);color:white;border:2px solid #ffffff;}
SensorStatusRecodeDialog QLineEdit
{background-color:rgb(62, 62, 62);border:none;color:white;}
SensorStatusRecodeDialog QStackedWidget
{background-color:rgb(55, 55, 56);border:2px solid #ffffff;}
SensorStatusRecodeDialog QTableView
{background-color:rgb(62, 62, 62);color:white;}
SensorStatusRecodeDialog QTableView:item
{background-color:rgb(62, 62, 62);color:white;}
SensorStatusRecodeDialog QTableView:item:selected
{background-color:rgb(85,170,255);color:white;}
SensorStatusRecodeDialog QHeaderView::section
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
SensorStatusRecodeDialog QHeaderView::section:vertical
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
SensorStatusRecodeDialog QTableCornerButton::section
{background-color:rgb(87, 106, 140);border: 2px solid;}

/*登录界面*/
SystemRegister
{background-color:rgb(55, 55, 56);}
SystemRegister QLabel
{color:rgb(255, 255, 255);}
SystemRegister QPushButton
{background-color:rgb(62, 62, 62);color:white;border:2px solid #ffffff;}
SystemRegister QLineEdit
{background-color:rgb(62, 62, 62);border:none;color:white;}

/*历史界面样式表*/
HistoryDialog QTreeWidget
{background-color: rgb(62, 62, 62);color:white;}
HistoryDialog QTreeWidget QHeaderView::section
{background-color: rgb(62, 62, 62);color:white;}
HistoryDialog QTreeWidget:item:selected
{background-color:rgb(0, 170, 255);}

HistoryDialog QTreeWidget::branch:open:has-children
{
image:url(:/VectorIcon/downarrows.png);
}
HistoryDialog QTreeWidget::branch:closed:has-children
{
image:url(:/VectorIcon/stop7.png);
}

HistoryDialog
{background-color: rgb(62, 62, 62);}

/*传感器界面样式表*/
SensorStatusDialog
{background-color: rgb(62, 62, 62);}
/*界面树状表格样式表*/
SensorStatusDialog QTabWidget
{background-color:rgb(62, 62, 62);color:white;}
SensorStatusDialog QWidget
{background-color:rgb(62, 62, 62);color:white;}
SensorStatusDialog QLable
{background-color:rgb(62, 62, 62);color:white;}
SensorStatusDialog QTabBar::tab
{background-color: rgb(62, 62, 62);color:white;border: 2px solid #ffffff;}
SensorStatusDialog QTabBar::tab:selected
{background-color: rgb(62, 62, 62);color:white;border: 2px solid #ffffff;}

/*日志界面样式表*/
LogDialog
{background-color: rgb(62, 62, 62);}
LogDialog QTableView
{background-color:rgb(62, 62, 62);}
LogDialog QTableView:item
{background-color:rgb(62, 62, 62);}
LogDialog QTableView:item:selected
{background-color:rgb(62, 62, 62);}

LogDialog QHeaderView::section
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}

LogDialog QHeaderView::section:vertical
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
LogDialog QTableCornerButton::section
{background-color:rgb(62, 62, 62);border: 2px solid;}

/*日常维护界面样式表*/
DailyMaintenanceDialog
{background-color: rgb(0, 0, 0);}
DailyMaintenanceDialog QLabel
{color: rgb(255, 255, 255);}
DailyMaintenanceDialog QGroupBox
{color: rgb(255, 255, 255);background-color: rgb(40, 40, 40);}
DailyMaintenanceDialog QPushButton
{background-color: rgb(62, 62, 62);color:white;border: 2px solid #ffffff;}
DailyMaintenanceDialog QLineEdit
{background-color: rgb(62, 62, 62);border:none;color:white;}
DailyMaintenanceDialog#groupBox_maintain_message
{color: rgb(255, 255, 255);background-color: rgb(62, 62, 62);}

/*系统维护界面样式表*/
SystemMaintenanceDialog
{background-color:rgb(55, 55, 56);}
SystemMaintenanceDialog QLabel
{color:rgb(255, 255, 255);}
SystemMaintenanceDialog QPushButton
{background-color:rgb(62, 62, 62);color:white;border:2px solid #ffffff;}
SystemMaintenanceDialog QLineEdit
{background-color:rgb(62, 62, 62);border:none;color:white;}
SystemMaintenanceDialog QTextEdit
{background-color:rgb(62, 62, 62);border:none;color:white;}
SystemMaintenanceDialog QStackedWidget
{background-color:rgb(55, 55, 56);border:2px solid #ffffff;}

SystemMaintenanceDialog QTableView
{background-color:rgb(62, 62, 62);color:white;}
SystemMaintenanceDialog QTableView:item
{background-color:rgb(62, 62, 62);color:white;}
SystemMaintenanceDialog QTableView:item:selected
{background-color:rgb(85,170,255);color:white;}
SystemMaintenanceDialog QHeaderView::section
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
SystemMaintenanceDialog QHeaderView::section:vertical
{background-color:rgb(62, 62, 62);color:rgb(255,255,255);}
SystemMaintenanceDialog QTableCornerButton::section
{background-color:rgb(87, 106, 140);border: 2px solid;}


/*系统参数配置界面样式表*/
SystemConfigurationDialog
{background-color: rgb(0, 0, 0);}
SystemConfigurationDialog QLabel
{color: rgb(255, 255, 255);}
SystemConfigurationDialog QGroupBox
{color: rgb(255, 255, 255);}
SystemConfigurationDialog QPushButton
{background-color: rgb(62, 62, 62);color:white;border: 2px solid #ffffff;}
SystemConfigurationDialog QStackedWidget
{background-color: rgb(62, 62, 62);}
SystemConfigurationDialog QCheckBox
{color: rgb(255, 255, 255);}

/*数据配置路径按钮*/
SystemConfigurationDialog QPushButton#pushButton_bright_set
{background-color: rgb(0, 0, 0);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_bright_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(64, 166, 0, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none}
SystemConfigurationDialog#pushButton_bright_set::hover
{border:1px solid white;}

SystemConfigurationDialog QPushButton#pushButton_data
{background-color: rgb(0, 0, 0);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_data::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(64, 166, 0, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none}
SystemConfigurationDialog#pushButton_data::hover
{border:1px solid white;}

SystemConfigurationDialog QPushButton#pushButton_ftp_file_set
{background-color: rgb(0, 0, 0);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_ftp_file_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(64, 166, 0, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none}
SystemConfigurationDialog QPushButton#pushButton_ftp_file_set::hover
{border:1px solid white;}

SystemConfigurationDialog QPushButton#pushButton_network_set
{background-color: rgb(0, 0, 0);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_network_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(64, 166, 0, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none}
SystemConfigurationDialog QPushButton#pushButton_network_set::hover
{border:1px solid white;}

SystemConfigurationDialog QPushButton#pushButton_save_data_path
{background-color: rgb(0, 0, 0);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_save_data_path::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(64, 166, 0, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none}
SystemConfigurationDialog QPushButton#pushButton_save_data_path::hover
{border:1px solid white;}

SystemConfigurationDialog QPushButton#pushButton_skin_set
{background-color: rgb(0, 0, 0);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_skin_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(64, 166, 0, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none}
SystemConfigurationDialog QPushButton#pushButton_skin_set::hover
{border:1px solid white;}

SystemConfigurationDialog QPushButton#pushButton_view_set
{background-color: rgb(0, 0, 0);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_view_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(64, 166, 0, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none}
SystemConfigurationDialog QPushButton#pushButton_view_set::hover
{border:1px solid white;}

SystemConfigurationDialog QPushButton#pushButton_station_set
{background-color: rgb(0, 0, 0);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_station_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(64, 166, 0, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none}
SystemConfigurationDialog QPushButton#pushButton_station_set::hover
{border:1px solid white;}
