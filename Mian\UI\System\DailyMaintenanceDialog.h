﻿#ifndef DAILYMAINTENANCEDIALOG_H
#define DAILYMAINTENANCEDIALOG_H

#include <QDialog>
#include "tools/Global.h"
namespace Ui
{
    class DailyMaintenanceDialog;
}

class DailyMaintenanceDialog : public QDialog
{
    Q_OBJECT

public:
    explicit DailyMaintenanceDialog(QWidget* parent = nullptr);
    ~DailyMaintenanceDialog();
public slots:
    void set_DailyMaintenanceDialog_styleSheet(int);
private slots:
    void on_pushButton_add_clicked();

    void on_pushButton_find_clicked();

    void on_pushButton_delect_clicked();


private:
    Ui::DailyMaintenanceDialog* ui;
    QTimer* auto_delect_data;
};

#endif // DAILYMAINTENANCEDIALOG_H
