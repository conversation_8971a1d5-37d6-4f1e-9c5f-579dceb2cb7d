The system is: Windows - 10.0.19045 - AMD64
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: D:/Program Files (x86)/Microsoft Visual Studio/2019/Community/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/cl.exe 
Build flags: 
Id flags:  

The output was:
0
用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
版权所有(C) Microsoft Corporation。保留所有权利。

CMakeCXXCompilerId.cpp
Microsoft (R) Incremental Linker Version 14.29.30159.0
Copyright (C) Microsoft Corporation.  All rights reserved.

/out:CMakeCXXCompilerId.exe 
CMakeCXXCompilerId.obj 


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.exe"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.obj"

The CXX compiler identification is MSVC, found in "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/blas/out/build/x64-Debug/CMakeFiles/3.20.21032501-MSVC_2/CompilerIdCXX/CMakeCXXCompilerId.exe"

Detecting CXX compiler ABI info compiled with the following output:
Change Dir: E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/blas/out/build/x64-Debug/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe cmTC_bae05 && [1/2] Building CXX object CMakeFiles\cmTC_bae05.dir\CMakeCXXCompilerABI.cpp.obj

[2/2] Linking CXX executable cmTC_bae05.exe




