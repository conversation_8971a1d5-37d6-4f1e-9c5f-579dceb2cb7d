﻿#ifndef TXT_STATERECORD_DATA_H
#define TXT_STATERECORD_DATA_H

#include <QObject>
#include <QTableView>
#include <QStandardItemModel>
#include <QHeaderView>
#include <QFile>
#include <QTextStream>
#include <QFileInfo>
#include <QMessageBox>
#include <QSortFilterProxyModel>
#include <QDateTime>
#include <QDir>

class txt_staterecord_data : public QObject
{
    Q_OBJECT
public:
    struct StateRecord_SQ_GROUP
    {

        QString content; // 内容
        QString time;    // 时间
    };
    explicit txt_staterecord_data(QObject *parent = nullptr);
    ~txt_staterecord_data();

    QVector<int> ratios = {1, 2}; // 设置模型各列显示比例
    int cur_row;                  // 记录最后一行
    QTableView *tableview;        // 存储使用模型的view

    QVector<struct StateRecord_SQ_GROUP> data;         // 存储查询数据库的结果
    QVector<struct StateRecord_SQ_GROUP> current_data; // 存储新添加的日志数据，还未添加到数据库
    QString currentFolder;                             // 记录当前文件路径
    // 使用QStandardItemModel模型
    QStandardItemModel *StandardItemModel; // 该模型可以设置单元项的字体样式
    bool StandardItemModel_status = false;
    QSet<int> m_nonEditableColumns;                                  // 记录不可编辑列
    void use_QStandardItemModel(QTableView *view, QString filepath); //  模型初始化

    QVector<StateRecord_SQ_GROUP> temp_data;                                                                 // 存储新添加的日志数据，还未添加到数据库
    bool insertdata(struct StateRecord_SQ_GROUP data);                                                       // 插入数据
    bool save_StandardItemModelTotxt(QString time_type, const QVector<QString> &title = QVector<QString>()); // 保存StandardItemModel中的日志数据到txt文件
    void deleteOldFiles(QString currentFolder,QString time);                                                                       // 删除早于指定时间的文件 时间格式yyyy-MM-dd HH:mm:ss
    void clear_StandardItemModel_data();                                                                     // 清空模型中的数据
    bool set_column_color(QVector<QColor> c);                                                                // 设置最后一行的颜色
    bool set_column_color(int row, int column, const QColor &c = QColor(255, 255, 255));                     // 设置指定项的颜色
    bool update_filepath(QString filepath);                                                                  // 更新文件存储路径
    void set_column_edit(int column, bool en);                                                               // 设置指定列编辑状态
signals:

private:
    QMetaObject::Connection coon;
};

#endif // TXT_STATERECORD_DATA_H
