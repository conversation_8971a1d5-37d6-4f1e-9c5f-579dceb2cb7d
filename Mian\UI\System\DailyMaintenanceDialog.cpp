#include "DailyMaintenanceDialog.h"
#include "ui_DailyMaintenanceDialog.h"
#include "tools/Global.h"
DailyMaintenanceDialog::DailyMaintenanceDialog(QWidget* parent) : QDialog(parent),
ui(new Ui::DailyMaintenanceDialog)
{
    ui->setupUi(this);
    this->setWindowTitle("日常维修");
    this->setWindowFlags(this->windowFlags() | Qt::WindowMaximizeButtonHint);
    this->setWindowFlags(Qt::Window);
    ui->dateTimeEdit_start_time->setCalendarPopup(true);
    ui->dateTimeEdit_start_time->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_start_time->setDateTime(QDateTime::currentDateTime());

    ui->dateTimeEdit_stop_time->setCalendarPopup(true);
    ui->dateTimeEdit_stop_time->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_stop_time->setDateTime(QDateTime::currentDateTime());

    ui->dateTimeEdit_maintain_time->setCalendarPopup(true);
    ui->dateTimeEdit_maintain_time->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_maintain_time->setDateTime(QDateTime::currentDateTime());

    ui->dateTimeEdit_delect->setCalendarPopup(true);
    ui->dateTimeEdit_delect->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_delect->setDateTime(QDateTime::currentDateTime());

    set_DailyMaintenanceDialog_styleSheet(systemconfigfdata->systemdata.set_skin);
    RoutineMaintenance_data_txt = new txt_routinemaintenance_data();
    RoutineMaintenance_data_txt->use_QStandardItemModel(ui->treeView, "日常维护/");

    auto_delect_data = new QTimer();
    connect(auto_delect_data, SIGNAL(timeout()), this, SLOT(on_pushButton_delect_clicked()));

    auto_delect_data->start(1000);

}

DailyMaintenanceDialog::~DailyMaintenanceDialog()
{
    delete ui;
    delete RoutineMaintenance_data_txt;
}

void DailyMaintenanceDialog::set_DailyMaintenanceDialog_styleSheet(int index)
{

}

void DailyMaintenanceDialog::on_pushButton_add_clicked()
{
    QDateTime DateStr1 = ui->dateTimeEdit_maintain_time->dateTime();
    QString timeStr1 = DateStr1.toString("yyyy-MM-dd HH:mm:ss");

    txt_routinemaintenance_data::RoutineMaintenance_SQ_GROUP data;
    data.type = ui->comboBox_maintain_type->currentText();
    data.personnel = ui->lineEdit_maintain_people->text();
    data.content = ui->textEdit_routinue->toPlainText();
    data.u_time = timeStr1;
    data.title = ui->lineEdit_maintain_title->text();
    RoutineMaintenance_data_txt->save_RoutineMaintenance_dataTotxt(data, "beijing");
}

void DailyMaintenanceDialog::on_pushButton_find_clicked()
{
    QDateTime DateStr1 = ui->dateTimeEdit_start_time->dateTime();
    QString timeStr1 = DateStr1.toString("yyyy-MM-dd HH:mm:ss");
    QDateTime DateStr2 = ui->dateTimeEdit_stop_time->dateTime();
    QString timeStr2 = DateStr2.toString("yyyy-MM-dd HH:mm:ss");
    RoutineMaintenance_data_txt->select_usetime_StandardItemModel(timeStr1, timeStr2);
}

void DailyMaintenanceDialog::on_pushButton_delect_clicked()
{
    //删除所有早于当前时间之前的子目录
    QDateTime month_file = QDateTime::currentDateTime().addDays(-(systemconfigfdata.systemdata.daily_maintenance_day));
    //QDateTime DateStr1 = ui->dateTimeEdit_delect->dateTime();
    QString timeStr1 = month_file.toString("yyyy-MM-dd HH:mm:ss");
    RoutineMaintenance_data_txt->deleteOldFiles(timeStr1);
}


