#include "plot.h"
#include "tools/Global.h"
#include <Qwt/qwt_picker_machine.h>
Plot::Plot(int Spectrogram_number_t, QWidget *parent) : QwtPlot(parent),
                                                        d_alpha(255)
{
    // 初始化Spectrogram对象，及相关数据存储对象
    Spectrogram_number = Spectrogram_number_t;
    for (int i = 0; i < Spectrogram_number_t; i++)
    {
        QwtPlotSpectrogram *temp;
        d_spectrogram.append(temp);
        d_spectrogram[i] = new LabeledSpectrogram(); //
        d_spectrogram[i]->setRenderHint(QwtPlotItem::RenderAntialiased, true);
        d_spectrogram[i]->setCachePolicy(QwtPlotRasterItem::PaintCache); // 启用缓存
        d_spectrogram[i]->setRenderThreadCount(5);                       // 自动使用最优线程数
        d_spectrogram[i]->setCachePolicy(QwtPlotRasterItem::PaintCache); // 启用渲染缓存

        Data_smoothing_parameter.append(0);
        QVector<double> time, heights;
        m_times.append(time);
        m_heights.append(heights);
        QVector<QVector<double>> temps;
        m_temps.append(temps);
    }

    setMouseTracking(true);          // 启用无按键状态下的连续鼠标位置追踪​​
    zoomer = new MyZoomer(canvas()); // canvas()用于获取绘图区域的画布对象
    // panner = new QwtPlotPanner( canvas() );
    // 该信号用于传递鼠标所在时间线的产品数据
    connect(zoomer, static_cast<void (MyZoomer::*)(int, double)>(&MyZoomer::cur_x_y),
            this, [this](int xVal, double yVal)
            {
        //qDebug() << "坐标值:" << xVal << yVal;

        xVal = xVal/120;
        if(Spectrogram_number ==1 )
        {
            for(int i=0;i<m_times[0].size();i++)
            {
                if(m_times[0][i] == xVal*120)
                {
                    emit cur_x_y1(m_times[0][i],m_heights[0],m_temps[0][i]);
                    return;
                }
            }
        }

        else
        {
            return ;
            for(int i=0;i<m_times[1].size();i++)
            {
                if(m_times[1][i] == xVal*120)
                {
                    emit cur_x_y1(m_times[1][i],m_heights[1],m_temps[1][i]);
                    return;
                }
            }
        } });

    // 配置y轴左侧坐标，默认范围0~100
    rightAxis = axisWidget(QwtPlot::yRight);
    rightAxis->setColorBarEnabled(true);
    setAxisScale(QwtPlot::yRight, 0, 100);
    enableAxis(QwtPlot::yRight);

    plotLayout()->setAlignCanvasToScales(true); // 控制 ​​绘图画布（Canvas）与坐标轴刻度线的对齐方式
    setColorMap(Plot::RGBMap);                  // 设置绘图颜色映射方式

    // 禁用自动缩放并强制设置X轴范围
    setAxisAutoScale(QwtPlot::xBottom, false);
    timeScaleDraw = new TimeScaleDraw();
    // 创建并设置自定义坐标轴绘制器
    setAxisScaleDraw(QwtPlot::xBottom, timeScaleDraw);

    // setAxisScaleEngine(QwtPlot::xBottom,new FixedMajorScaleEngine(3600));
    setAxisScale(QwtPlot::xBottom, 0, 86400, 7200); // 24小时，步长1小时
    setAxisScale(QwtPlot::yLeft, 0, 10, 1);         // 24小时，步长1小时

    // 创建缩放器对象（用于显示跟踪标签）

    //*/ --------------------- 禁用所有缩放交互 ---------------------
    // 清空所有鼠标和键盘的缩放模式
    zoomer->setMousePattern(QwtEventPattern::MouseSelect1, Qt::NoButton); // 禁用左键
    zoomer->setMousePattern(QwtEventPattern::MouseSelect2, Qt::NoButton); // 禁用右键组合键
    zoomer->setMousePattern(QwtEventPattern::MouseSelect3, Qt::NoButton); // 禁用右键复位

    // 禁用键盘快捷键（如方向键缩放）
    zoomer->setKeyPattern(QwtEventPattern::KeySelect1, Qt::Key_unknown);
    zoomer->setKeyPattern(QwtEventPattern::KeySelect2, Qt::Key_unknown);

    // --------------------- 保留跟踪标签功能 ---------------------
    zoomer->setTrackerMode(QwtPlotPicker::AlwaysOn); // 始终显示跟踪标签

    // --------------------- 固定视图范围（可选） ---------------------
    // 设置缩放基准范围为固定范围（禁止任何缩放操作）
    zoomer->setZoomBase(QRectF(0, 0, 86400, 10)); // X轴0~86400秒，Y轴0~10*/

    // --------------------- 禁用平移器（若存在） ---------------------
    // 删除或注释掉QwtPlotPanner的创建代码
    // QwtPlotPanner *panner = new QwtPlotPanner(canvas());

    yleftscaledraw = new yleftScaleDraw();
    setAxisScaleDraw(QwtPlot::yLeft, yleftscaledraw);
}

void Plot::addRealtimeData(int num, QDateTime time, const QVector<double> &heights, const QVector<double> &temps)
{
    if (num >= Spectrogram_number)
        if (heights.isEmpty() || temps.isEmpty())
            return;

    if (time.date() != uptime.date())
    {
        uptime = time;
        clearSpectrogram();
    }
    // 1. 检查高度层和温度数据长度是否一致
    if (heights.size() != temps.size())
    {
        qWarning() << "错误: 高度层数量与温度数据长度不匹配!";
        return;
    }
    uptime = time;
    // 2. 更新高度层数据（如果需要动态更新高度层）
    m_heights[num] = heights;

    // 3. 添加新时间点和温度数据
    m_times[num].append(time.time().hour() * 3600 + time.time().minute() * 60 + time.time().second());

    QVector<double> temp_mu;
    temp_mu.clear();
    for (int i = 0; i < temps.size(); i++)
    {
        temp_mu.append(temps[i] * Multiple);
    }
    m_temps[num].append(temp_mu);

    // 4. 更新光谱图数据
    d_spectrogram[num]->setData(new SpectrogramData(m_times[num], m_heights[num], m_temps[num], Data_smoothing_parameter[num])); // setdata内部自己释放不用管
    d_spectrogram[num]->attach(this);                                                                                            // 将图添加到当前窗口

    const QwtInterval zInterval = d_spectrogram[num]->data()->interval(Qt::ZAxis); // 从光谱图的数据中获取 Z 轴（颜色轴）的数值范围（例如 [最小值, 最大值]）。
    if (num >= 1)
        return;

    // 设置颜色条和坐标轴

    rightAxis->setColorBarEnabled(true);
    if (zInterval.maxValue() - zInterval.minValue() < 1)
    {
        setAxisScale(QwtPlot::yRight, 0, 1);
    }
    else
    {
        double min = std::floor(zInterval.minValue() / 7.0) * 7.0;
        double max = std::ceil(zInterval.maxValue() / 7.0) * 7.0;

        QList<double> ticks;
        for (double v = min; v <= max; v += (max - min) / 7)
        {
            ticks.append(v);
        }

        QwtScaleDiv scalediv(min, max);
        scalediv.setTicks(QwtScaleDiv::MajorTick, ticks);
        setAxisScaleDiv(QwtPlot::yRight, scalediv);
    }
    // setAxisScale(QwtPlot::yRight, zInterval.minValue(), zInterval.maxValue());
    enableAxis(QwtPlot::yRight);

    plotLayout()->setAlignCanvasToScales(true);
    setColorMap(Plot::RGBMap);
}

void Plot::set_Data_smoothing_parameter(int data)
{

    if (Spectrogram_number == 1)
    {
        Data_smoothing_parameter[0] = data;
        // 4. 更新光谱图数据
        d_spectrogram[0]->setData(new SpectrogramData(m_times[0], m_heights[0], m_temps[0], Data_smoothing_parameter[0])); // setdata内部自己释放不用管
        d_spectrogram[0]->attach(this);                                                                                    // 将图添加到当前窗口
    }
    else
    {
        Data_smoothing_parameter[1] = data;
        // 4. 更新光谱图数据
        d_spectrogram[1]->setData(new SpectrogramData(m_times[1], m_heights[1], m_temps[1], Data_smoothing_parameter[1])); // setdata内部自己释放不用管
        d_spectrogram[1]->attach(this);                                                                                    // 将图添加到当前窗口
    }

    replot();

    //        // 设置颜色条和坐标轴
    //        rightAxis->setTitle("Intensity");
    //        rightAxis->setColorBarEnabled(true);
    //        if(zInterval.maxValue()-zInterval.minValue()<1)
    //        {
    //            setAxisScale(QwtPlot::yRight, 0, 1);
    //        }
    //        else
    //        setAxisScale(QwtPlot::yRight, zInterval.minValue(), zInterval.maxValue());
    //        enableAxis(QwtPlot::yRight);

    //        plotLayout()->setAlignCanvasToScales(true);
    //        setColorMap(Plot::RGBMap);
}

QVector<QVector<double>> Plot::get_m_times_data()
{
    return m_times;
}

QVector<QVector<double>> Plot::get_m_heights_data()
{
    return m_heights;
}

QVector<QVector<QVector<double>>> Plot::get_m_temps_data()
{
    return m_temps;
}

void Plot::set_label_enable(bool en)
{

    if (Spectrogram_number == 1)
    {
        if (auto *labeled = dynamic_cast<LabeledSpectrogram *>(d_spectrogram[0]))
        {
            labeled->set_label_enable(en);
            replot();
        }
        else
        {
            return;
        }
    }
    else
    {
        if (auto *labeled = dynamic_cast<LabeledSpectrogram *>(d_spectrogram[1]))
        {
            labeled->set_label_enable(en);
            replot();
        }
        else
        {
            return;
        }
    }
}

void Plot::set_label_Density(int data)
{

    if (Spectrogram_number == 1)
    {
        if (auto *labeled = dynamic_cast<LabeledSpectrogram *>(d_spectrogram[0]))
        {
            labeled->set_label_Density(data);
            replot();
        }
        else
        {
            return;
        }
    }
    else
    {
        if (auto *labeled = dynamic_cast<LabeledSpectrogram *>(d_spectrogram[1]))
        {
            labeled->set_label_Density(data);
            replot();
        }
        else
        {
            return;
        }
    }
}

void Plot::set_x_rang(double min, double max, double stepSize)
{
    setAxisScale(QwtPlot::xBottom, min, max, stepSize);
    replot();
}

void Plot::set_x_right(double data)
{
    QwtScaleDiv xscle = axisScaleDiv(QwtPlot::xBottom);
    double min = xscle.lowerBound();
    double max = xscle.upperBound();
    // max-min
    double step = 7200;
    QList<double> major = xscle.ticks(QwtScaleDiv::MajorTick);
    if (major.size() >= 2)
    {
        step = major[1] - major[0];
    }
    double new_min = 0;
    double new_max = 0;

    if ((86400 - max) >= (max - min))
    {
        new_min = min + ((max - min) * data);
        new_max = max + ((max - min) * data);
    }
    else
    {
        new_min = 86400 - (max - min);
        new_max = 86400;
    }

    setAxisScale(QwtPlot::xBottom, new_min, new_max, step);
    replot();
}

void Plot::set_x_left(double data)
{
    QwtScaleDiv xscle = axisScaleDiv(QwtPlot::xBottom);
    double min = xscle.lowerBound();
    double max = xscle.upperBound();
    // max-min
    double step = 7200;
    QList<double> major = xscle.ticks(QwtScaleDiv::MajorTick);
    if (major.size() >= 2)
    {
        step = major[1] - major[0];
    }

    double new_min = 0;
    double new_max = 0;
    if ((min - 0) >= (max - min))
    {
        new_min = min - ((max - min) * data);
        new_max = max - ((max - min) * data);
    }
    else
    {
        new_min = 0;
        new_max = max - min;
    }

    setAxisScale(QwtPlot::xBottom, new_min, new_max, step);
    replot();
}

void Plot::set_y_rang(double min, double max, double stepSize)
{
    setAxisScale(QwtPlot::yLeft, min, max, stepSize);
    replot();
}

void Plot::set_y_up(double data)
{
    QwtScaleDiv xscle = axisScaleDiv(QwtPlot::yLeft);
    double min = xscle.lowerBound();
    double max = xscle.upperBound();
    // max-min
    double step = 1;
    QList<double> major = xscle.ticks(QwtScaleDiv::MajorTick);
    if (major.size() >= 2)
    {
        step = major[1] - major[0];
    }
    double new_min = 0;
    double new_max = 0;

    if ((10 - max) >= (max - min))
    {
        new_min = min + ((max - min) * data);
        new_max = max + ((max - min) * data);
    }
    else
    {
        new_min = 10 - (max - min);
        new_max = 10;
    }

    setAxisScale(QwtPlot::yLeft, new_min, new_max, step);
    replot();
}

void Plot::set_y_down(double data)
{
    QwtScaleDiv xscle = axisScaleDiv(QwtPlot::yLeft);
    double min = xscle.lowerBound();
    double max = xscle.upperBound();
    // max-min
    double step = 1;
    QList<double> major = xscle.ticks(QwtScaleDiv::MajorTick);
    if (major.size() >= 2)
    {
        step = major[1] - major[0];
    }

    double new_min = 0;
    double new_max = 0;
    if ((min - 0) >= (max - min))
    {
        new_min = min - ((max - min) * data);
        new_max = max - ((max - min) * data);
    }
    else
    {
        new_min = 0;
        new_max = max - min;
    }

    setAxisScale(QwtPlot::yLeft, new_min, new_max, step);
    replot();
}

void Plot::contextMenuEvent(QContextMenuEvent *event)
{
    QMenu menu(this);

    menu.addAction("温度");
    menu.addAction("水汽");
    menu.addAction("湿度");
    menu.addAction("温度_湿度");

    if (systemconfigfdata.systemdata.set_skin == 0)
    {

        menu.setStyleSheet("background-color: rgb(30, 30, 30, 100);color: rgb(255, 255, 255);"
                           "border-right:2px solid #aaaaaa; "
                           "border-bottom:2px solid #aaaaaa;"
                           "border-left:2px solid #aaaaaa;"
                           "border-top:2px solid #aaaaaa; "
                           "border-radius:5px;"
                           "font: 10pt "
                           "Agency FB"
                           ";"
                           "selection-background-color: rgb(100, 40, 40);");
    }
    else if (systemconfigfdata.systemdata.set_skin == 1)
    {
        menu.setStyleSheet("background-color: rgb(87, 106, 140);color: rgb(0,0,0);"
                           "border-right:2px solid rgb(200,221,244); "
                           "border-bottom:2px solid rgb(200,221,244);"
                           "border-left:2px solid rgb(200,221,244);"
                           "border-top:2px solid rgb(200,221,244);"
                           "border-radius:5px;"
                           "font: 10pt "
                           "Agency FB"
                           ";"
                           "selection-background-color: rgb(200,221,244);");
    }
    connect(&menu, &QMenu::triggered, [this](QAction *action)
            {
                emit spectrogram_change(action->text());

                qDebug() << "123";
            });
    menu.exec(event->globalPos());
}

void Plot::set_title(QString text)
{
    QwtScaleWidget *top = axisWidget(QwtPlot::xTop);
    top->setTitle(text);
}

void Plot::set_LinearColorMapRGB_en(bool en)
{
    LinearColorMapRGB_en = en;
}

void Plot::set_LinearColorMapRGB(QColor start_c, QColor end_c, QVector<QPair<double, QColor>> data)
{
    startColor = start_c;
    endColor = end_c;
    LinearColorMapRGB_data = data;
}

void Plot::set_label_color(QColor data)
{
    QPalette palette = axisWidget(QwtPlot::yLeft)->palette();
    palette.setColor(QPalette::Text, data);       // 文本颜色设为绿色
#pragma warning(suppress : 4996)
    palette.setColor(QPalette::Foreground, data); // 文本颜色设为绿色
    axisWidget(QwtPlot::yLeft)->setPalette(palette);

    QPalette palette2 = axisWidget(QwtPlot::yRight)->palette();
    palette2.setColor(QPalette::Text, data);       // 文本颜色设为绿色
#pragma warning(suppress : 4996)
    palette2.setColor(QPalette::Foreground, data); // 文本颜色设为绿色
    axisWidget(QwtPlot::yRight)->setPalette(palette2);

    QPalette palette1 = axisWidget(QwtPlot::xBottom)->palette();
#pragma warning(suppress : 4996)
    palette1.setColor(QPalette::Foreground, data); // 影响刻度线颜色
#pragma warning(suppress : 4996)s
    palette1.setColor(QPalette::Text, data);       // 影响刻度线颜色
    axisWidget(QwtPlot::xBottom)->setPalette(palette1);
}

void Plot::set_yright_uint(QString name)
{

    yrightscaledraw = new yrightScaleDraw(name);
    setAxisScaleDraw(QwtPlot::yRight, yrightscaledraw);
}

void Plot::set_mode(QVector<int> d1, QVector<int> d2)
{
    if (d1.count() != Spectrogram_number || d2.count() != Spectrogram_number)
        return;
    choose1.clear();
    choose2.clear();
    for (int i = 0; i < Spectrogram_number; i++)
    {

        int temp1, temp2;
        choose1.append(temp1);
        choose2.append(temp2);
        choose1[i] = d1[i];
        choose2[i] = d2[i];
    }
}

void Plot::set_Multiple(int data)
{
    Multiple = data;
}

void Plot::set_contourLevels(QList<double> data)
{
    // for(int i=0;i<Spectrogram_number;i++)
    if (Spectrogram_number == 1)
        d_spectrogram[0]->setContourLevels(data);
    else
        d_spectrogram[1]->setContourLevels(data);
}

void Plot::set_Combination(bool en)
{
    Combination = en;
    if (Spectrogram_number > 1)
    {
        d_spectrogram[1]->setDisplayMode(QwtPlotSpectrogram::ImageMode, false);
        d_spectrogram[1]->setDefaultContourPen(QPen(Qt::black, 0));
    }
}

void Plot::set_Spectrogram_number(int num)
{
    for (int i = 0; i < Spectrogram_number; i++)
    {
        delete d_spectrogram[i];
    }
    d_spectrogram.clear();
    m_times.clear();
    m_heights.clear();
    m_temps.clear();
    Data_smoothing_parameter.clear();
    Spectrogram_number = num;
    for (int i = 0; i < num; i++)
    {
        QwtPlotSpectrogram *temp;
        d_spectrogram.append(temp);
        d_spectrogram[i] = new LabeledSpectrogram(); //
        d_spectrogram[i]->setRenderHint(QwtPlotItem::RenderAntialiased, true);
        d_spectrogram[i]->setCachePolicy(QwtPlotRasterItem::PaintCache); // 启用缓存
        d_spectrogram[i]->setRenderThreadCount(5);                       // 自动使用最优线程数
        d_spectrogram[i]->setCachePolicy(QwtPlotRasterItem::PaintCache); // 启用渲染缓存

        Data_smoothing_parameter.append(0);
        QVector<double> time, heights;
        m_times.append(time);
        m_heights.append(heights);
        QVector<QVector<double>> temps;
        m_temps.append(temps);
    }
}

void Plot::showContour(bool on)
{

    if (Spectrogram_number == 1)
    {
        d_spectrogram[0]->setDisplayMode(QwtPlotSpectrogram::ContourMode, on);
        d_spectrogram[0]->setDefaultContourPen(QPen(Qt::black, 0));
    }
    else
    {
        d_spectrogram[1]->setDisplayMode(QwtPlotSpectrogram::ContourMode, on);
        d_spectrogram[1]->setDefaultContourPen(QPen(Qt::black, 0));
    }

    replot();
}

void Plot::showSpectrogram(bool on)
{

    if (Spectrogram_number == 1)
    {
        d_spectrogram[0]->setDisplayMode(QwtPlotSpectrogram::ImageMode, on);
        // d_spectrogram[0]->setDefaultContourPen( QPen( Qt::black, 0 )  );
    }
    else
    {
        //        d_spectrogram[0]->setDisplayMode( QwtPlotSpectrogram::ImageMode, !on );
        //        d_spectrogram[0]->setDefaultContourPen( QPen( Qt::black, 0 )  );
        d_spectrogram[0]->setDisplayMode(QwtPlotSpectrogram::ImageMode, on);
        d_spectrogram[1]->setDisplayMode(QwtPlotSpectrogram::ImageMode, false);
        // d_spectrogram[1]->setDefaultContourPen( QPen( Qt::black, 0 )  );
    }

    replot();
}

void Plot::setColorMap(int type)
{
    QwtScaleWidget *axis = axisWidget(QwtPlot::yRight);
    QwtInterval zInterval;

    if (d_spectrogram[0] && d_spectrogram[0]->data())
    {
        zInterval = d_spectrogram[0]->data()->interval(Qt::ZAxis);
        if (zInterval.maxValue() - zInterval.minValue() < 1)
        {
            zInterval.setMaxValue(1);
            zInterval.setMinValue(0);
        }
    }
    else
    {
        zInterval = QwtInterval(0, 100); // 默认范围
    }

    d_mapType = type;

    int alpha = d_alpha;
    switch (type)
    {
    case Plot::HueMap:
    {
        d_spectrogram[0]->setColorMap(new HueColorMap());
        axis->setColorMap(zInterval, new HueColorMap());
        break;
    }
    case Plot::AlphaMap:
    {
        alpha = 255;
        d_spectrogram[0]->setColorMap(new AlphaColorMap());
        axis->setColorMap(zInterval, new AlphaColorMap());
        break;
    }
    case Plot::IndexMap:
    {
        d_spectrogram[0]->setColorMap(new LinearColorMapIndexed());
        axis->setColorMap(zInterval, new LinearColorMapIndexed());
        break;
    }
    case Plot::RGBMap:
    default:
    {
        if (LinearColorMapRGB_en)
        {

            d_spectrogram[0]->setColorMap(new LinearColorMapRGB(startColor, endColor, LinearColorMapRGB_data));
            axis->setColorMap(zInterval, new LinearColorMapRGB(startColor, endColor, LinearColorMapRGB_data));
        }
        else
        {
            d_spectrogram[0]->setColorMap(new LinearColorMapRGB());
            axis->setColorMap(zInterval, new LinearColorMapRGB());
        }
    }
    }
    d_spectrogram[0]->setAlpha(alpha);

    replot();
}

void Plot::setAlpha(int alpha)
{
    // setting an alpha value doesn't make sense in combination
    // with a color map interpolating the alpha value

    d_alpha = alpha;
    if (d_mapType != Plot::AlphaMap)
    {
        d_spectrogram[0]->setAlpha(alpha);
        replot();
    }
}

void Plot::clearSpectrogram()
{

    for (int index = 0; index < Spectrogram_number; index++)
    {
        m_times[index].clear();
        m_heights[index].clear();
        m_temps[index].clear();

        d_spectrogram[index]->setData(new SpectrogramData(m_times[index], m_heights[index], m_temps[index], Data_smoothing_parameter[index])); // setdata内部自己释放不用管
    }

    replot();
}

void Plot::open_file(QString filepath)
{

    for (int index = 0; index < Spectrogram_number; index++)
    {
        m_times[index].clear();
        m_heights[index].clear();
        m_temps[index].clear();

        QFile file(filepath);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
        {
            QMessageBox::critical(this, "错误", "无法打开文件");
            return;
        }

        QTextStream in(&file);
        int lineNum = 0;
        while (!in.atEnd())
        {
            QString line = in.readLine();
            QStringList fields = line.split(",", QString::SkipEmptyParts);

            // 解析高度数据（第3行）
            if (lineNum == 2 && fields.size() > 11)
            {
                for (int i = 11; i < fields.size() - 1; ++i)
                {
                    QString heightStr = fields[i].replace("km", "");
                    bool ok;
                    double height = heightStr.toDouble(&ok);
                    if (ok)
                        m_heights[index].append(height);
                }
            }
            // 解析温度数据（从第4行开始）
            else if (lineNum > choose1[index] && fields.size() > 11 && fields[2] == QString::number(choose2[index]))
            {
                // 解析时间
                QTime time = QTime::fromString(fields[1].mid(11, 8), "hh:mm:ss");
                if (time.isValid())
                {
                    m_times[index].append(time.hour() * 3600 + time.minute() * 60 + time.second());
                }

                // 解析温度值
                QVector<double> tempRow;
                for (int i = 11; i < fields.size() - 1; ++i)
                {
                    bool ok;
                    double temp = fields[i].toDouble(&ok);
                    if (ok)
                        tempRow.append(temp * Multiple);
                }
                if (m_heights[index].size() != tempRow.size())
                {
                    m_times[index].remove(m_times[index].size() - 1);
                }
                else
                {

                    m_temps[index].append(tempRow);
                }
            }
            lineNum++;
        }

        file.close();
        d_spectrogram[index]->setData(new SpectrogramData(m_times[index], m_heights[index], m_temps[index], Data_smoothing_parameter[index])); // setdata内部自己释放不用管
        d_spectrogram[index]->attach(this);
    }
    // 设置光谱图数据

    // 创建新数据
    // m_spectrogramData = new SpectrogramData(m_times, m_heights, m_temps);

    const QwtInterval zInterval = d_spectrogram[0]->data()->interval(Qt::ZAxis); // 从光谱图的数据中获取 Z 轴（颜色轴）的数值范围（例如 [最小值, 最大值]）。

    // 获取数据范围
    const QwtInterval xInterval = d_spectrogram[0]->data()->interval(Qt::XAxis);
    const QwtInterval yInterval = d_spectrogram[0]->data()->interval(Qt::YAxis);

    // 设置颜色条和坐标轴
    // rightAxis->setTitle("Intensity");
    rightAxis->setColorBarEnabled(true);
    if (zInterval.maxValue() - zInterval.minValue() < 1)
    {
        setAxisScale(QwtPlot::yRight, 0, 1);
    }
    else
    {
        double min = std::floor(zInterval.minValue() / 7.0) * 7.0;
        double max = std::ceil(zInterval.maxValue() / 7.0) * 7.0;

        QList<double> ticks;
        for (double v = min; v <= max; v += (max - min) / 7)
        {
            ticks.append(v);
        }

        QwtScaleDiv scalediv(min, max);
        scalediv.setTicks(QwtScaleDiv::MajorTick, ticks);
        setAxisScaleDiv(QwtPlot::yRight, scalediv);
    }
    // setAxisScale(QwtPlot::yRight, zInterval.minValue(), zInterval.maxValue());
    enableAxis(QwtPlot::yRight);

    plotLayout()->setAlignCanvasToScales(true);
    setColorMap(Plot::RGBMap);

    //      // 禁用自动缩放并强制设置X轴范围
    //        setAxisAutoScale(QwtPlot::xBottom, false);

    //    // 创建并设置自定义坐标轴绘制器
    //        setAxisScaleDraw(QwtPlot::xBottom, timeScaleDraw);

    //       // setAxisScaleEngine(QwtPlot::xBottom,new FixedMajorScaleEngine(3600));
    //      setAxisScale(QwtPlot::xBottom, 0, 86400); // 24小时，步长1小时

    //      // 创建并配置缩放器
    //      zoomer->setMousePattern(QwtEventPattern::MouseSelect2, Qt::RightButton, Qt::ControlModifier);
    //      zoomer->setMousePattern(QwtEventPattern::MouseSelect3, Qt::RightButton);

    //      // 正确设置缩放基准范围
    //      zoomer->setZoomBase(QRectF(
    //          xInterval.minValue(),
    //          yInterval.minValue(),
    //          xInterval.width(),
    //          yInterval.width()
    //      ));

    //    panner->setAxisEnabled( QwtPlot::yRight, false ); // 禁用右侧轴平移
    //    panner->setMouseButton( Qt::MidButton );// 中键拖拽平移

    //    // Avoid jumping when labels with more/less digits
    //    // appear/disappear when scrolling vertically
    //    // 防止坐标轴标签跳动
    //    const QFontMetrics fm( axisWidget( QwtPlot::yLeft )->font() );
    //    QwtScaleDraw *sd = axisScaleDraw( QwtPlot::yLeft );
    //    sd->setMinimumExtent( fm.width( "100.00" ) );

    //    const QColor c( Qt::darkBlue );
    //    zoomer->setRubberBandPen( c );// 缩放选框颜色
    //    zoomer->setTrackerPen( c );// 坐标跟踪文本颜色
}
