﻿#include "FTPDataDialog.h"

#include "tools/Global.h"
/**
 * @brief FTPDataDialog 构造函数
 *
 * 初始化FTP配置对话框。
 *
 * @param parent 父窗口指针
 */
FTPDataDialog::FTPDataDialog(QWidget *parent) : QDialog(parent),
                                                ui(new Ui::FTPDataDialog)
{
    ui->setupUi(this);
    this->setWindowTitle("FTP配置");
    this->setFixedSize(702, 847);

    connect(ui->pushButton_inspect, &QPushButton::clicked, this, &FTPDataDialog::slot_pushButton_inspect_clicked);
    connect(ui->pushButton_confirm, &QPushButton::clicked, this, &FTPDataDialog::slot_pushButton_ok_clicked);
    connect(ui->pushButton_cancel, &QPushButton::clicked, this, [this]()
            { this->close(); });
}

FTPDataDialog::~FTPDataDialog()
{
    delete ui;
}

/**
 * @brief 加载FTP站点配置信息
 *
 * 该函数根据传入的FTP站点配置信息，将配置信息加载到对应的界面元素中，并禁用FTP站点名称的输入框。
 *
 * @param ftpconfigValue FTP站点配置信息
 */
void FTPDataDialog::load_data_fun(FTPSiteConfig ftpconfigValue)
{
    ui->lineEdit_ftp_name->setText(ftpconfigValue.siteName);
    ui->lineEdit_ftp_ip->setText(ftpconfigValue.host);
    ui->lineEdit_ftp_port->setText(QString::number(ftpconfigValue.port));
    ui->lineEdit_ftp_user->setText(ftpconfigValue.username);
    ui->lineEdit_ftp_password->setText(ftpconfigValue.password);
    ui->lineEdit_ftp_long_download_catalogue->setText(ftpconfigValue.remoteDir);
    ui->spinBox_check_out->setValue(ftpconfigValue.timeout);
    ui->spinBox_upload_out->setValue(ftpconfigValue.updateTimeOut);
    ui->spinBox_buchuan_num->setValue(ftpconfigValue.repCnt);
    ui->spinBox_buchuan_day->setValue(ftpconfigValue.repDay);
    ui->checkBox_upload->setChecked(ftpconfigValue.seqUpload);
    ui->checkBox_buchuan->setChecked(ftpconfigValue.repSwitchData);
    ui->checkBox_lv1->setChecked(ftpconfigValue.lv1Data);
    ui->checkBox_lv2->setChecked(ftpconfigValue.lv2Data);
    ui->checkBox_sensor->setChecked(ftpconfigValue.sensorData);
    ui->checkBox_clib->setChecked(ftpconfigValue.calibrationData);
    ui->lineEdit_ftp_name->setEnabled(false);
}

/**
 * @brief 设置添加状态
 *
 * 此函数将成员变量isAdd设置为true，表示当前处于添加状态。
 */
void FTPDataDialog::set_add_state()
{
    isAdd = true;
}

/**
 * @brief 设置FTPDataDialog的修改状态
 *
 * 将FTPDataDialog的修改状态设置为非添加状态。
 */
void FTPDataDialog::set_modify_state()
{
    isAdd = false;
}

/**
 * @brief 当点击“检查”按钮时触发的槽函数
 *
 * 当用户点击“检查”按钮时，该函数会被调用。该函数主要用于测试FTP连接是否成功。
 */
void FTPDataDialog::slot_pushButton_inspect_clicked()
{
    get_ui_data();
    FTPClientInterface tempFtpInterface;
    tempFtpInterface.initConfig(ftpConfig);
    ConnectStatus = tempFtpInterface.testConnection();
    if (ConnectStatus)
    {
        QMessageBox::information(nullptr, "FTP连接状态", "连接成功");
    }
    else
    {
        QMessageBox::information(nullptr, "FTP连接状态", "连接失败");
    }
}

/**
 * @brief 获取UI界面的数据，并验证数据的有效性
 *
 * 此函数从UI界面获取FTP服务器的配置信息，并验证输入数据的有效性。
 * 如果所有输入都有效，则将配置信息保存到ftpConfig对象中，并返回true；
 * 否则，显示相应的错误消息，并返回false。
 *
 * @return 如果所有输入数据都有效，则返回true；否则返回false。
 */
bool FTPDataDialog::get_ui_data()
{
    // 验证主机地址格式(支持IP和域名)
    QRegularExpression hostRegex("^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|"  // IPv4
                                "^([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)+[a-zA-Z]{2,}$");  // 域名
    QString host = ui->lineEdit_ftp_ip->text();
    if (!hostRegex.match(host).hasMatch())
    {
        QMessageBox::warning(this, tr("错误"), tr("请输入有效的IP地址或域名"));
        return false;
    }

    // 验证端口号(1-65535)
    int port = ui->lineEdit_ftp_port->text().toInt();
    if (port < 1 || port > 65535)
    {
        QMessageBox::warning(this, tr("错误"), tr("端口号必须在1-65535范围内"));
        return false;
    }

    // 验证用户名非空
    if (ui->lineEdit_ftp_user->text().isEmpty())
    {
        QMessageBox::warning(this, tr("错误"), tr("用户名不能为空"));
        return false;
    }

    // 验证密码非空
    if (ui->lineEdit_ftp_password->text().isEmpty())
    {
        QMessageBox::warning(this, tr("错误"), tr("密码不能为空"));
        return false;
    }

    // 验证远程目录非空
    if (ui->lineEdit_ftp_long_download_catalogue->text().isEmpty())
    {
        QMessageBox::warning(this, tr("错误"), tr("远程目录不能为空"));
        return false;
    }

    // 验证检查超时时间必须大于等于3秒
    if (ui->spinBox_check_out->value() < 3)
    {
        QMessageBox::warning(this, tr("错误"), tr("检查超时时间必须大于等于3秒"));
        return false;
    }

    // 验证上传超时时间必须大于等于3秒
    if (ui->spinBox_upload_out->value() < 3)
    {
        QMessageBox::warning(this, tr("错误"), tr("上传超时时间必须大于等于3秒"));
        return false;
    }

    ftpConfig.siteName = ui->lineEdit_ftp_name->text();
    ftpConfig.host = ui->lineEdit_ftp_ip->text();
    ftpConfig.port = port;
    ftpConfig.username = ui->lineEdit_ftp_user->text();
    ftpConfig.password = ui->lineEdit_ftp_password->text();
    ftpConfig.remoteDir = ui->lineEdit_ftp_long_download_catalogue->text();
    ftpConfig.timeout = ui->spinBox_check_out->value();
    ftpConfig.updateTimeOut = ui->spinBox_upload_out->value();
    ftpConfig.repCnt = ui->spinBox_buchuan_num->value();
    ftpConfig.repDay = ui->spinBox_buchuan_day->value();
    ftpConfig.seqUpload = ui->checkBox_upload->isChecked();
    ftpConfig.repSwitchData = ui->checkBox_buchuan->isChecked();
    ftpConfig.lv1Data = ui->checkBox_lv1->isChecked();
    ftpConfig.lv2Data = ui->checkBox_lv2->isChecked();
    ftpConfig.sensorData = ui->checkBox_sensor->isChecked();
    ftpConfig.calibrationData = ui->checkBox_clib->isChecked();
    return true;
}

/**
 * @brief 设置FTP数据对话框的默认UI界面
 *
 * 此函数用于设置FTP数据对话框的默认UI界面，包括文本框、下拉框和复选框等。
 */
void FTPDataDialog::set_ui_default()
{
    ui->lineEdit_ftp_name->setText("");
    ui->lineEdit_ftp_ip->setText("");
    ui->lineEdit_ftp_port->setText("");
    ui->lineEdit_ftp_user->setText("");
    ui->lineEdit_ftp_password->setText("");
    ui->lineEdit_ftp_long_download_catalogue->setText("");
    ui->spinBox_check_out->setValue(3);
    ui->spinBox_upload_out->setValue(3);
    ui->spinBox_buchuan_num->setValue(0);
    ui->spinBox_buchuan_day->setValue(0);
    ui->checkBox_upload->setChecked(false);
    ui->checkBox_buchuan->setChecked(false);
    ui->checkBox_lv1->setChecked(false);
    ui->checkBox_lv2->setChecked(false);
    ui->checkBox_sensor->setChecked(false);
    ui->checkBox_clib->setChecked(false);
    ui->lineEdit_ftp_name->setEnabled(true);
}



/**
 * @brief slot_pushButton_ok_clicked 槽函数，处理“确定”按钮点击事件
 *
 * 当用户点击“确定”按钮时，此函数将被调用。
 *
 * 如果获取到界面数据成功，且FTP连接状态为已连接，则发送FTP配置信息并关闭对话框。
 * 如果连接状态不为已连接，则弹出确认对话框，询问用户是否要添加测试失败或未测试的FTP连接。
 * 如果用户选择“是”，则发送FTP配置信息并关闭对话框，并输出调试信息“用户确认执行操作”。
 * 如果用户选择“否”，则仅输出调试信息“用户取消操作”。
 *
 * 如果获取界面数据失败，则弹出警告对话框提示“参数输入有误”。
 */
void FTPDataDialog::slot_pushButton_ok_clicked()
{
    if (get_ui_data())
    {
        if (ConnectStatus)
        {
            emit signal_data_send_show(ftpConfig);
            this->close();
        }
        else
        {
            QMessageBox msgBox;
            msgBox.setWindowTitle("确认操作");
            msgBox.setText("你确定要添加测试失败或者未测试的FTP连接？");
            msgBox.setStandardButtons(QMessageBox::Yes | QMessageBox::No);
            msgBox.setDefaultButton(QMessageBox::No);
            int ret = msgBox.exec();
            if (ret == QMessageBox::Yes)
            {
                emit signal_data_send_show(ftpConfig);
                this->close();

                // 用户点击了 Yes 按钮
                qDebug() << "用户确认执行操作";
            }
            else
            {
                // 用户点击了 No 按钮
                qDebug() << "用户取消操作";
            }
        }
    }
    else
    {
        QMessageBox::warning(this, tr("错误"), tr("参数输入有误"));
    }
}


/**
 * @brief 处理关闭事件
 *
 * 当FTPDataDialog窗口关闭时，调用此函数来处理关闭事件。
 *
 * @param event 关闭事件对象指针
 */
void FTPDataDialog::closeEvent(QCloseEvent* event)
{
    QDialog::closeEvent(event);
}

/**
 * @brief 显示事件处理函数
 *
 * 当FTP数据对话框显示时，会调用此函数。
 *
 * @param event 显示事件指针
 */
void FTPDataDialog::showEvent(QShowEvent* event)
{
    if (isAdd)
    {
        set_ui_default();
    }
    QDialog::showEvent(event);
}