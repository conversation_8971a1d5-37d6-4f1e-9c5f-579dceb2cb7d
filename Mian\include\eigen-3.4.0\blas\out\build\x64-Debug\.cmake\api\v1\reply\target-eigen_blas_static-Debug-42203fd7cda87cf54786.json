{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "eigen_blas_static.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 31, "parent": 0}, {"command": 1, "file": 0, "line": 45, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4]}], "id": "eigen_blas_static::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/blas/out/install/x64-Debug"}}, "name": "eigen_blas_static", "nameOnDisk": "eigen_blas_static.lib", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "single.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "double.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "complex_single.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "complex_double.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "xerbla.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/srotm.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/srotmg.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/drotm.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/drotmg.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/lsame.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/dspmv.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/ssbmv.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/chbmv.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/sspmv.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/zhbmv.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/chpmv.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/dsbmv.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/zhpmv.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/dtbmv.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/stbmv.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/ctbmv.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/ztbmv.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/d_cnjg.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/r_cnjg.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "f2c/complexdots.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}