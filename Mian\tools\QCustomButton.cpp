﻿#include "QCustomButton.h"

QCustomButton::QCustomButton(QWidget* parent, int width, int height) : QWidget(parent), m_width(width), m_height(height), position(0)
{

    max = qMax(m_width, m_height);
    min = m_width > m_height ? m_height : m_width;
    length = max - min;
    dir = m_width >= m_height ? Qt::AlignLeft : Qt::AlignTop;

    connect(&timer, SIGNAL(timeout()), this, SLOT(move_slot()));
    init();
}

QCustomButton::~QCustomButton()
{
    delete myLable;
}


void QCustomButton::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event);
    QStyleOption opt;
    opt.init(this);
    QPainter painter(this);

    style()->drawPrimitive(QStyle::PE_Widget, &opt, &painter, this);

}

void QCustomButton::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton)
    {
        switch (dir)
        {
        case Qt::AlignLeft: case Qt::AlignRight:
            position = myLable->pos().x();
            timer.start(1);
            break;

        case Qt::AlignTop: case Qt::AlignBottom:
            position = myLable->pos().y();
            timer.start(1);
            break;

        }
    }
    QWidget::mousePressEvent(event);
}
void QCustomButton::init()
{
    this->resize(m_width, m_height);
    this->setStyleSheet(QString("QCustomButton{background-color:rgb(144,236,144);border-radius:%1px;color:white;}").arg(min / 2));
    myLable = new QLabel(this);
    myLable->resize(min, min);
    myLable->setText("关");
    myLable->setAlignment(Qt::AlignCenter);
    myLable->setStyleSheet(QString("#myLable{background-color:red;border-radius:%1px;color:white;}").arg(min / 2));
    this->setFixedSize(m_width, m_height);

}

void QCustomButton::move_slot()
{
    switch (dir)
    {
    case Qt::AlignLeft:
        if (position > length)
        {
            timer.stop();
            this->setStyleSheet(QString("QCustomButton{background-color:red;border-radius:%1px;color:white;}").arg(min / 2));
            myLable->setText("开");
            myLable->setStyleSheet(QString("#myLable{background-color:rgb(144,236,144);border-radius:%1px;color:white;}").arg(min / 2));
            dir = Qt::AlignRight;
            emit stateChange(true);
            return;
        }
        ++position;
        myLable->move(position, 0);
        break;
    case Qt::AlignRight:
        if (position <= 0)
        {
            timer.stop();
            this->setStyleSheet(QString("QCustomButton{background-color:rgb(144,236,144);border-radius:%1px;color:white;}").arg(min / 2));
            myLable->setText("关");
            myLable->setStyleSheet(QString("#myLable{background-color:red;border-radius:%1px;color:white;}").arg(min / 2));
            dir = Qt::AlignLeft;
            emit stateChange(false);
            return;
        }
        --position;
        myLable->move(position, 0);
        break;
    case Qt::AlignTop:
        if (position > length)
        {
            timer.stop();
            this->setStyleSheet(QString("QCustomButton{background-color:red;border-radius:%1px;color:white;}").arg(min / 2));
            myLable->setText("开");
            myLable->setStyleSheet(QString("#myLable{background-color:rgb(144,236,144);border-radius:%1px;color:white;}").arg(min / 2));
            dir = Qt::AlignBottom;
            emit stateChange(true);
            return;
        }
        ++position;
        myLable->move(0, position);
        break;
    case Qt::AlignBottom:
        if (position <= 0)
        {
            timer.stop();
            this->setStyleSheet(QString("QCustomButton{background-color:rgb(144,236,144);border-radius:%1px;color:white;}").arg(min / 2));
            myLable->setText("关");
            myLable->setStyleSheet(QString("#myLable{background-color:red;border-radius:%1px;color:white;}").arg(min / 2));
            dir = Qt::AlignTop;
            emit stateChange(false);
            return;
        }
        --position;
        myLable->move(0, position);
        break;

    }
}
