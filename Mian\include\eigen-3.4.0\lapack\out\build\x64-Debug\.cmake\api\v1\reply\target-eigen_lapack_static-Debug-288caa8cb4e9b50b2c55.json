{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "eigen_lapack_static.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 93, "parent": 0}, {"command": 1, "file": 0, "line": 107, "parent": 0}, {"command": 2, "file": 0, "line": 14, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1"}], "includes": [{"backtrace": 3, "path": "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/lapack/../blas"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4]}], "id": "eigen_lapack_static::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/lapack/out/install/x64-Debug"}}, "name": "eigen_lapack_static", "nameOnDisk": "eigen_lapack_static.lib", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "single.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "double.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "complex_single.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "complex_double.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/blas/xerbla.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}