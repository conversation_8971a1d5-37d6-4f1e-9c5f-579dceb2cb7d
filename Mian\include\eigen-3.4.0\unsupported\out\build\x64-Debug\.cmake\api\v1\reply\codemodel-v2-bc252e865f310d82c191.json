{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "projectIndex": 0, "source": "."}, {"build": "Eigen", "childIndexes": [2], "hasInstallRule": true, "parentIndex": 0, "projectIndex": 0, "source": "Eigen"}, {"build": "Eigen/CXX11", "hasInstallRule": true, "parentIndex": 1, "projectIndex": 0, "source": "Eigen/CXX11"}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported/out/build/x64-Debug", "source": "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/unsupported"}, "version": {"major": 2, "minor": 2}}