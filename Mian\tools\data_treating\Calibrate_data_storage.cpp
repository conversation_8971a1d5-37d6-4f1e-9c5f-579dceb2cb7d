﻿#include "Calibrate_data_storage.h"

Calibrate_data_storage::Calibrate_data_storage(QString file_path, FILENAME file_name, QObject *parent)
{
    Q_UNUSED(parent);
    filepath = file_path;
    filename = file_name;

    file = nullptr;
}

/*
函数名：Generate_new_Calibrate_data
作用：定标数据存入日文件。
注意：必须将该类型的定标数据一次存入calibrate_data，不能多次调用该函数
参数要求：
CALTime:定标时间，不用使用，该函数根据本地时间自动生成
CALType：必须为同一种参数类型
Record：记录序号，不用输入，该函数根据自动从1开始到calibrate_data.count()
DataType:参数类型，根据实际输入
ch_freq：根据DataType输入各个频率的DataType类型参数

返回值：true :保存成功
       false :保存失败
*/

bool Calibrate_data_storage::Generate_new_Calibrate_data(QVector<struct Calibrate_data> calibrate_data)
{
    // 创建目录结构

    // 获取文件名
    struct FILENAME temp = filename;
    temp.pf1ag = "Z";
    temp.productidentifier = "UPAR";
    if (use_beijing_utc)
        temp.yyyyMMddhhmmss = QDateTime::currentDateTime().toString("yyyyMMddHHmmss");
    else
        temp.yyyyMMddhhmmss = QDateTime::currentDateTimeUtc().toString("yyyyMMddHHmmss");
    temp.deviceidentification = "YMWR";
    temp.frequency = "D";
    if (txt_or_xml)
        temp.type = "txt";
    else
        temp.type = "xml";
    temp.datatype = "CAL";
    temp.ftype = "R";
    QString filenamestr = Generate_file_name(temp);
    // 检查文件路径是否存在，如果不存在则创建
    QString filepath_dir;
    if (use_beijing_utc)
        filepath_dir = filepath + QDateTime::currentDateTime().toString("yyyy-MM-dd") + "/";
    else
        filepath_dir = filepath + QDateTime::currentDateTimeUtc().toString("yyyy-MM-dd") + "/";
    QDir dir(filepath_dir);
    if (!dir.exists())
        dir.mkpath(filepath_dir);

    bool isexitsfile = false; // 是否已存在日文件标志
    // 遍历指定文件夹下的所有文件
    foreach (const QFileInfo &fileinfo, dir.entryInfoList(QDir::Files))
    {
        if (comparefilenames(fileinfo.fileName(), filenamestr)) // comparefilenames为真则代表存在当天日文件
        {

            // 判断上次写入的日文件是否与找到的日文件相同
            if (cur_day_filename != fileinfo.fileName())
            {
                // 如果不相同则重新打开该日文件
                if (file != nullptr)
                    delete file;
                file = new QFile(fileinfo.path() + "/" + fileinfo.fileName());
                if (!file->open(QIODevice::ReadWrite | QIODevice::Text))
                {
                    return false;
                }

                cur_day_filename = fileinfo.fileName(); // 更新当前日文件

                if (txt_or_xml)
                {
                    QTextStream in(file);
                    int linecount = 0;

                    while (!in.atEnd())
                    {
                        in.readLine();
                        linecount++;
                    }
                    Record_number = linecount - 1;

                    file->close();
                }
                else
                {
                    // 通过有多少行判断
                    QDomDocument doc;
                    // file->seek(0);

                    int errorline, errorcolumn;
                    QString errormes;
                    if (!doc.setContent(file, &errormes, &errorline, &errorcolumn))
                    {
                        return false;
                    }

                    file->close();

                    QDomElement root = doc.documentElement();

                    if (root.isNull() || root.tagName() != "CalibrationInformation")
                    {
                        return false;
                    }
                    int count = 0;
                    QDomNodeList bnodes = root.elementsByTagName("CalibrationData");
                    for (int i = 0; i < bnodes.count(); i++)
                    {
                        QDomElement belement = bnodes.at(i).toElement();
                        if (belement.isNull())
                            continue;
                        count += root.elementsByTagName("CalibrationGroup").size();
                    }
                    // Record_number = root.elementsByTagName("CalibrationData").size()+1;
                    Record_number = count + 1;
                }
            }
            // 打开该日文件
            if (file != nullptr)
                delete file;
            file = new QFile(fileinfo.path() + "/" + fileinfo.fileName());
            if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
            {
                return false;
            }
            isexitsfile = true;
        }
    }
    if (!isexitsfile) // 如果没有当前的日文件则重新创建
    {
        // 创建文件
        if (file != nullptr)
            delete file;
        file = new QFile(filepath_dir + filenamestr);
        if (txt_or_xml)
        {
            if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
            {
                return false;
            }
            cur_day_filename = filenamestr;
            Record_number = 1; // 行号置1
        }
        else
        {
            if (!file->open(QIODevice::WriteOnly | QIODevice::Truncate))
            {
                return false;
            }
            cur_day_filename = filenamestr;
            Record_number = 1;                       // 行号置1
            Insert_new_Calibrate_Basic_parameters(); // 写入xml声明及根节点
        }
    }

    Insert_new_Calibrate_data(calibrate_data);
    return true;
}
/*
函数名：Generate_new_Calibrate_Minutes_file
作用：定标数据存入分钟文件。
注意：必须将该类型的定标数据一次存入calibrate_data，不能多次调用该函数
参数要求：
CALTime:定标时间，不用使用，该函数根据本地时间自动生成
CALType：必须为同一种参数类型
Record：记录序号，不用输入，该函数根据自动从1开始到calibrate_data.count()
DataType:参数类型，根据实际输入
ch_freq：根据DataType输入各个频率的DataType类型参数

返回值：true :保存成功
       false :保存失败
*/
bool Calibrate_data_storage::Generate_new_Calibrate_Minutes_file(QVector<struct Calibrate_data> calibrate_data)
{
    if (calibrate_data.isEmpty())
        return false;
    // 创建目录结构
    struct FILENAME temp = filename;
    temp.pf1ag = "Z";
    temp.productidentifier = "UPAR";
    if (use_beijing_utc)
        temp.yyyyMMddhhmmss = QDateTime::currentDateTime().toString("yyyyMMddHHmmss");
    else
        temp.yyyyMMddhhmmss = QDateTime::currentDateTimeUtc().toString("yyyyMMddHHmmss");
    temp.deviceidentification = "YMWR";
    temp.frequency = "M";
    if (txt_or_xml)
        temp.type = "txt";
    else
        temp.type = "xml";
    temp.datatype = "CAL";
    temp.ftype = "R";

    QString filenamestr = Generate_file_name(temp);
    QString filepath_dir;
    if (use_beijing_utc)
        filepath_dir = filepath + QDateTime::currentDateTime().toString("yyyy-MM-dd") + "/";
    else
        filepath_dir = filepath + QDateTime::currentDateTimeUtc().toString("yyyy-MM-dd") + "/";
    QDir dir(filepath_dir);
    if (!dir.exists())
        dir.mkpath(filepath_dir);

    if (txt_or_xml)
    {
        QFile *file = new QFile(filepath_dir + Generate_file_name(temp));
        if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
        {
            return false;
        }
        Record_number = 1;

        if (calibrate_data.isEmpty())
            return false;
        QByteArray linedata;
        QString mes = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss") + ',' + calibrate_data[0].CALType;
        if (!isPureAscii(mes))
        {
            file->close();
            return false;
        }
        linedata = mes.toLatin1();
        linedata.append("\r\n");
        if (file->write(linedata) == -1)
        {
            file->close();
            return false;
        }
        linedata.clear();
        QString table_header = "Record,DateType,Ch Freq1,Ch Freq2,Ch Freq3,Ch Freq4,Ch Freq5,Ch Freq6,Ch Freq7,Ch Freq8,Ch Freq9,Ch Freq10,Ch Freq11,Ch Freq12,Ch Freq13,Ch Freq14";
        if (!isPureAscii(table_header))
        {
            file->close();
            return false;
        }
        linedata = table_header.toLatin1();
        linedata.append("\r\n");

        if (file->write(linedata) == -1)
        {
            file->close();
            return false;
        }

        for (int i = 0; i < calibrate_data.count(); i++)
        {
            linedata.clear();
            QString line = QString::number(Record_number++) + ','
                           //+bright_temperature.DateTime +','
                           + deal_qstring_data(calibrate_data[i].DataType) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[0], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[1], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[2], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[3], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[4], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[5], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[6], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[7], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[8], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[9], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[10], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[11], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[12], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[13], 3, true);
            if (!isPureAscii(line))
            {
                file->close();
                return false;
            }
            linedata = line.toLatin1();
            linedata.append("\r\n");

            if (file->write(linedata) == -1)
            {
                file->close();
                return false;
            }
        }
        file->close();
        return true;
    }
    else
    {
        // 创建文件
        QFile *file = new QFile(filepath_dir + filenamestr);

        QDomDocument doc;
        // 添加XML声明
        QDomProcessingInstruction header = doc.createProcessingInstruction("xml", "version=\"1.0\" encoding=\"UTF-8\"");
        doc.appendChild(header);
        // 创建根节点
        QDomElement root = doc.createElement("CalibrationInformation");
        root.setAttribute("device", device);
        root.setAttribute("type", type);
        doc.appendChild(root);

        QDomElement CalibrationDataelement = doc.createElement("CalibrationData");

        QDomElement CALTimeelement = doc.createElement("CALTime");
        CALTimeelement.appendChild(doc.createTextNode(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss")));
        CalibrationDataelement.appendChild(CALTimeelement);
        QDomElement CALTypeelement = doc.createElement("CALType");
        CALTypeelement.appendChild(doc.createTextNode(calibrate_data[0].CALType));
        CalibrationDataelement.appendChild(CALTypeelement);

        for (int i = 0; i < calibrate_data.count(); i++)
        {
            QDomElement CalibrationGroupelement = doc.createElement("CalibrationGroup");

            QDomElement Recordelement = doc.createElement("Record");
            Recordelement.appendChild(doc.createTextNode(QString::number(i + 1)));
            CalibrationGroupelement.appendChild(Recordelement);
            QDomElement DataTypeelement = doc.createElement("DataType");
            DataTypeelement.appendChild(doc.createTextNode(calibrate_data[i].DataType));
            CalibrationGroupelement.appendChild(DataTypeelement);
            for (int n = 0; n < 14; n++)
            {
                QDomElement chelement = doc.createElement("CH");
                chelement.setAttribute("freq", Control_decimal_bit(fre[n], 3, true));
                chelement.appendChild(doc.createTextNode(Control_decimal_bit(calibrate_data[i].ch_freq[n], 3, true)));
                CalibrationGroupelement.appendChild(chelement);
            }
            CalibrationDataelement.appendChild(CalibrationGroupelement);
        }

        root.appendChild(CalibrationDataelement);

        if (!file->open(QIODevice::ReadWrite | QIODevice::Truncate | QIODevice::Text))
        {
            return false; // 如果
        }

        QTextStream stream(file);
        stream.setCodec("UTF-8");
        doc.save(stream, 4);
        file->close();

        return true;
    }
}

/*
 * 函数名：set_device_and_type
 * 作用：更改xml文件中device及type
 */
void Calibrate_data_storage::set_device_and_type(QString device, QString type)
{
    this->device = device;
    this->type = type;
}

/*
 * set_file_path
 * 作用：更改文件路径
 */
void Calibrate_data_storage::set_file_path(QString path)
{
    filepath = path;
}
/*
 * set_file_name
 * 作用：更改文件名中的of1ag(按台站区站号进行编码)、originator(气象台站区站号)、equipmenttype(设备型号),其他参数无法修改。
 * 注意：name中参数必须严格按照格式要求、程序中不检查该格式
 */
void Calibrate_data_storage::set_file_name(FILENAME name)
{

    filename = name;
}
/*
 * set_Minutes_file_en
 * 作用：启动按分钟保存文件。
 */
void Calibrate_data_storage::set_Minutes_file_en(bool en)
{
    Minutes_file_en = en;
}
/*
 * set_Minutes_file_en
 * 作用：启动按天保存文件。
 */
void Calibrate_data_storage::set_Day_file_en(bool en)
{
    Day_file_en = en;
}
/*
 * set_beijing_or_utc
 * 作用：设置文件路径及文件名中的时间是使用北京时间还是UTC时间。
 * true beijing false utc
 */
void Calibrate_data_storage::set_beijing_or_utc(bool choose)
{
    use_beijing_utc = choose;
}

void Calibrate_data_storage::set_save_mode(bool mode)
{
    txt_or_xml = mode;
}

void Calibrate_data_storage::save_data(QVector<Calibrate_data> calibrate_data)
{
    if (calibrate_data.isEmpty())
        return;
    if (Day_file_en)
    {
        bool re = Generate_new_Calibrate_data(calibrate_data);
        if (re)
        {
            qDebug() << "Calibrate day succeed";
        }
        else
        {
            qDebug() << "Calibrate day failure";
        }
    }

    if (Minutes_file_en)
    {
        bool re = Generate_new_Calibrate_Minutes_file(calibrate_data);
        if (re)
        {
            qDebug() << "Calibrate Minutes succeed";
        }
        else
        {
            qDebug() << "Calibrate Minutes failure";
        }
    }
}
/*
 * Insert_new_Calibrate_Basic_parameters
 * 作用：创建XML声明及根节点
 */
bool Calibrate_data_storage::Insert_new_Calibrate_Basic_parameters()
{

    if (txt_or_xml)
    {
    }
    else
    {
        file->close();

        QDomDocument doc;
        // 添加XML声明
        QDomProcessingInstruction header = doc.createProcessingInstruction("xml", "version=\"1.0\" encoding=\"UTF-8\"");
        doc.appendChild(header);
        // 创建根节点
        QDomElement root = doc.createElement("CalibrationInformation");
        root.setAttribute("device", device);
        root.setAttribute("type", type);
        doc.appendChild(root);

        if (!file->isOpen())
        {
            if (!file->open(QIODevice::WriteOnly | QIODevice::Truncate))
            {
                return false; // 如果
            }
        }
        QTextStream stream(file);
        stream.setCodec("UTF-8");
        doc.save(stream, 4);
        file->close();
        return true;
    }
}
/*
 * Insert_new_Calibrate_data
 * 作用：在日文件中插入一组定标数据
 */
bool Calibrate_data_storage::Insert_new_Calibrate_data(QVector<struct Calibrate_data> calibrate_data)
{
    if (txt_or_xml)
    {
        if (!file->isOpen())
        {
            if (!file->open(QIODevice::WriteOnly | QIODevice::Append))
            {
                return false; // 如果
            }
        }

        if (calibrate_data.isEmpty())
            return false;
        QByteArray linedata;
        QString mes = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss") + ',' + calibrate_data[0].CALType;
        if (!isPureAscii(mes))
        {
            file->close();
            return false;
        }
        linedata = mes.toLatin1();
        linedata.append("\r\n");
        if (file->write(linedata) == -1)
        {
            file->close();
            return false;
        }
        linedata.clear();
        QString table_header = "Record,DateType,Ch Freq1,Ch Freq2,Ch Freq3,Ch Freq4,Ch Freq5,Ch Freq6,Ch Freq7,Ch Freq8,Ch Freq9,Ch Freq10,Ch Freq11,Ch Freq12,Ch Freq13,Ch Freq14";
        if (!isPureAscii(table_header))
        {
            file->close();
            return false;
        }
        linedata = table_header.toLatin1();
        linedata.append("\r\n");

        if (file->write(linedata) == -1)
        {
            file->close();
            return false;
        }

        for (int i = 0; i < calibrate_data.count(); i++)
        {
            linedata.clear();
            QString line = QString::number(Record_number++) + ','
                           //+bright_temperature.DateTime +','
                           + deal_qstring_data(calibrate_data[i].DataType) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[0], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[1], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[2], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[3], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[4], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[5], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[6], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[7], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[8], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[9], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[10], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[11], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[12], 3, true) + ',' + Control_decimal_bit(calibrate_data[i].ch_freq[13], 3, true);
            if (!isPureAscii(line))
            {
                file->close();
                return false;
            }
            linedata = line.toLatin1();
            linedata.append("\r\n");

            if (file->write(linedata) == -1)
            {
                file->close();
                return false;
            }
        }
        file->close();
        return true;
    }
    else
    {
        QDomDocument doc;
        if (file->isOpen())
        {
            file->close();
        }

        if (!file->open(QIODevice::ReadWrite | QIODevice::Text))
        {
            return false; // 如果
        }

        // file->seek(0);
        if (!doc.setContent(file))
        {
            file->close();
            return false;
        }

        file->close();

        /*可添加判断xml声明是否存在，如果不存在则添加声明*/
        // 获取或创建根节点
        QDomElement root = doc.documentElement();
        if (root.isNull())
        {
            root = doc.createElement("CalibrationInformation");
            root.setAttribute("device", device);
            root.setAttribute("type", type);
            doc.appendChild(root);
        }

        // 创建数据节点
        QDomElement CalibrationDataelement = doc.createElement("CalibrationData");

        QDomElement CALTimeelement = doc.createElement("CALTime");
        CALTimeelement.appendChild(doc.createTextNode(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss")));
        CalibrationDataelement.appendChild(CALTimeelement);
        QDomElement CALTypeelement = doc.createElement("CALType");
        CALTypeelement.appendChild(doc.createTextNode(calibrate_data[0].CALType));
        CalibrationDataelement.appendChild(CALTypeelement);

        for (int i = 0; i < calibrate_data.count(); i++)
        {
            QDomElement CalibrationGroupelement = doc.createElement("CalibrationGroup");

            QDomElement Recordelement = doc.createElement("Record");
            Recordelement.appendChild(doc.createTextNode(QString::number(Record_number++)));
            CalibrationGroupelement.appendChild(Recordelement);
            QDomElement DataTypeelement = doc.createElement("DataType");
            DataTypeelement.appendChild(doc.createTextNode(calibrate_data[i].DataType));
            CalibrationGroupelement.appendChild(DataTypeelement);
            for (int n = 0; n < 14; n++)
            {
                QDomElement chelement = doc.createElement("CH");
                chelement.setAttribute("freq", Control_decimal_bit(fre[n], 3, true));
                chelement.appendChild(doc.createTextNode(Control_decimal_bit(calibrate_data[i].ch_freq[n], 3, true)));
                CalibrationGroupelement.appendChild(chelement);
            }
            CalibrationDataelement.appendChild(CalibrationGroupelement);
        }

        root.appendChild(CalibrationDataelement);

        if (!file->open(QIODevice::ReadWrite | QIODevice::Truncate | QIODevice::Text))
        {
            return false; // 如果
        }

        QTextStream stream(file);
        stream.setCodec("UTF-8");
        doc.save(stream, 4);
        file->close();

        return true;
    }
}

/*
 * Generate_file_name
 * 作用：根据file_name中信息生成文件名
 */
QString Calibrate_data_storage::Generate_file_name(FILENAME file_name)
{
    return file_name.pf1ag + "_" + file_name.productidentifier + "_" + file_name.of1ag + "_" + file_name.originator + "_" + file_name.yyyyMMddhhmmss + "_" + file_name.ftype + "_" + file_name.deviceidentification + "_" + file_name.equipmenttype + "_" + file_name.datatype + "_" + file_name.frequency + "." + file_name.type;
}
/*
 * isPureAscii
 * 作用：判断字符串str是否是ASCII字符
 */
bool Calibrate_data_storage::isPureAscii(const QString &str)
{
    for (const QChar &ch : str)
    {
        if (ch.unicode() > 0x7f) // ASCII范围0x00~0x7f
        {
            return false;
        }
    }
    return true;
}

/*
 * Control_decimal_bit
 * 作用：将data数据保留bit个小数，并且转化成qstring返回
 * status：true 保留小数位时四舍五入
 *         false：保留小数位时不四舍五入
 */
QString Calibrate_data_storage::Control_decimal_bit(double data, int bit, bool status)
{
    if (data == std::numeric_limits<double>::lowest())
    {
        return "-";
    }
    if (status)
    {
        return QString::number(data, 'f', bit);
    }
    else
    {
        double temp = std::trunc(data * std::pow(10, bit)) / std::pow(10, bit);
        return QString::number(temp, 'f', bit);
    }
}

QString Calibrate_data_storage::deal_int_data(int data)
{
    if (data == std::numeric_limits<int>::lowest())
    {
        return "-";
    }
    return QString::number(data);
}

QString Calibrate_data_storage::deal_qstring_data(QString data)
{
    if (data.isEmpty())
        return "-";
    return data;
}
/*
 * comparefilenames
 * 作用：对比两个文件名除了时分秒外的其他数据是否一致，并且检查时间是否合法
 * status：true 保留小数位时四舍五入
 *         false：保留小数位时不四舍五入
 */
bool Calibrate_data_storage::comparefilenames(const QString &filename1, const QString &filename2)
{
    QString processedbasename1, extension1;
    QString processedbasename2, extension2;
    bool time1valid, time2valid;

    bool valid1 = processFileName(filename1, time1valid, processedbasename1, extension1);
    bool valid2 = processFileName(filename2, time2valid, processedbasename2, extension2);

    if (!valid1 || !valid2 || !time1valid || !time2valid)
    {
        return false;
    }
    if (extension1 != extension2)
    {
        return false;
    }

    return (processedbasename1 == processedbasename2);
}

bool Calibrate_data_storage::processFileName(const QString &filename, bool &timevalid, QString &processedbasename, QString &extension)
{
    QFileInfo fileinfo(filename);
    QString basename = fileinfo.baseName();
    extension = fileinfo.suffix();

    QStringList parts = basename.split('_');
    if (parts.size() < 5)
    {
        timevalid = false;
        return false;
    }

    // 获取时间字段
    QString timestr = parts.value(4);
    if (timestr.length() != 14)
    {
        timevalid = false;
        return false;
    }

    // 验证时间合法性
    QDateTime dt = QDateTime::fromString(timestr, "yyyyMMddHHmmss");
    if (!dt.isValid())
    {
        timevalid = false;
        return false;
    }
    timevalid = true;

    // 替换时分秒为000000
    QString newtime = timestr.left(8) + "000000";
    parts[4] = newtime;
    processedbasename = parts.join("_");
    return true;
}
