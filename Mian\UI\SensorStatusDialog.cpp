﻿#include "SensorStatusDialog.h"
#include "tools/Configdata/CSystemConfigData.h"
#include "tools/Global.h"
#include <cstring>
#include <iostream>

SensorStatusDialog::SensorStatusDialog(QWidget* parent) :
    QDialog(parent),
    ui(new Ui::SensorStatusDialog)
{
    ui->setupUi(this);
    this->setWindowTitle("传感器状态");
    this->setMinimumWidth(220);
    this->setStyleSheet("SensorStatusDialog::title{background-color:red;}");
    sensorComm = widgetComm;
    widget_1_flowLayout = new FlowLayout();
    widget_2_flowLayout = new FlowLayout();
    widget_3_flowLayout = new FlowLayout();
    widget_4_flowLayout = new FlowLayout();
    widget_5_flowLayout = new FlowLayout();
    widget_7_flowLayout = new FlowLayout();

    memset((void *)&six_temp,0,sizeof(six_temp));
    NoiseSrcSw = false;
    memset((void*)&gps,0,sizeof(gps));
    memset((void*)&antAng, 0, sizeof(antAng));
    memset((void*)&lowtemp, 0, sizeof(lowtemp));
    memset((void*)&weathersix, 0, sizeof(weathersix));
    memset((void*)&fan, 0, sizeof(fan));
    memset((void*)&work, 0, sizeof(work));
    memset((void*)&power, 0, sizeof(power));
    lowAveTemp=0;



    for (int i = 0; i < 4; i++)
    {
        // 传感器电源相关
        Power_layout[i] = new QFormLayout();
        Power_lable[i] = new QLabel();
        Power_Led_lable[i] = new QLabel();
        Power_lable[i]->setText(Power_Name[i]);
        Power_layout[i]->addRow(Power_Led_lable[i], Power_lable[i]);
        Power_layout[i]->setSpacing(1);
        Power_layout[i]->setContentsMargins(0, 0, 50, 0);
        widget_1_flowLayout->addItem(Power_layout[i]);
        setLED(Power_Led_lable[i], 0, 16);

        // 传感器状态相关
        Status_layout[i] = new QFormLayout();
        Status_lable[i] = new QLabel();
        Status_Led_lable[i] = new QLabel();
        Status_lable[i]->setText(Status_Name[i]);
        Status_layout[i]->addRow(Status_Led_lable[i], Status_lable[i]);
        Status_layout[i]->setSpacing(1);
        Status_layout[i]->setContentsMargins(0, 0, 50, 0);
        widget_2_flowLayout->addItem(Status_layout[i]);
        setLED(Status_Led_lable[i], 0, 16);
    }

    for (int i = 0; i < 6; i++)
    {
        // 气象六要素相关
        Six_layout[i] = new QFormLayout();
        Six_lable[i] = new QLabel();
        Six_Led_lable[i] = new QLabel();
        Six_lable[i]->setText(Six_Name[i]);
        Six_layout[i]->addRow(Six_lable[i], Six_Led_lable[i]);
        Six_layout[i]->setSpacing(1);
        Six_layout[i]->setContentsMargins(0, 0, 50, 0);
        widget_3_flowLayout->addItem(Six_layout[i]);

        // 接收机温度相关
        radioTemp_layout[i] = new QFormLayout();
        radioTemp_lable[i] = new QLabel();
        radioTemp_Led_lable[i] = new QLabel();
        radioTemp_lable[i]->setText(radioTemp_Name[i]);
        radioTemp_layout[i]->addRow(radioTemp_lable[i], radioTemp_Led_lable[i]);
        radioTemp_layout[i]->setSpacing(1);
        radioTemp_layout[i]->setContentsMargins(0, 0, 50, 0);
        widget_5_flowLayout->addItem(radioTemp_layout[i]);
    }

    for (int i = 0; i < 8; i++)
    {
        // GPS数据相关
        GPS_layout[i] = new QFormLayout();
        GPS_lable[i] = new QLabel();
        GPS_Led_lable[i] = new QLabel();
        GPS_lable[i]->setText(GPS_Name[i]);
        GPS_layout[i]->addRow(GPS_lable[i], GPS_Led_lable[i]);
        GPS_layout[i]->setSpacing(1);
        GPS_layout[i]->setContentsMargins(0, 0, 50, 0);
        widget_4_flowLayout->addItem(GPS_layout[i]);
    }

    // 四个黑体温度
    for (int i = 0; i < 4; i++)
    {
        boldface_layout[i] = new QFormLayout();
        boldface_lable[i] = new QLabel();
        boldface_Led_lable[i] = new QLabel();
        boldface_lable[i]->setText(boldface_Name[i]);
        boldface_layout[i]->addRow(boldface_lable[i], boldface_Led_lable[i]);
        boldface_layout[i]->setSpacing(1);
        boldface_layout[i]->setContentsMargins(0, 0, 50, 0);
        widget_5_flowLayout->addItem(boldface_layout[i]);
    }

    // 常温源平均温度
    normalTemp_layout = new QFormLayout();
    normalTemp_lable = new QLabel();
    normalTemp_Led_lable = new QLabel();
    normalTemp_lable->setText(normalTemp_Name);
    normalTemp_layout->addRow(normalTemp_lable, normalTemp_Led_lable);
    normalTemp_layout->setSpacing(1);
    normalTemp_layout->setContentsMargins(0, 0, 50, 0);

    widget_5_flowLayout->addItem(normalTemp_layout);


    // 风机转速
    FAN_layout = new QFormLayout();
    FAN_lable = new QLabel();
    FAN_Led_lable = new QLabel();
    FAN_lable->setText(FAN_Name);
    FAN_layout->addRow(FAN_lable, FAN_Led_lable);
    FAN_layout->setSpacing(1);
    FAN_layout->setContentsMargins(0, 0, 50, 0);

    widget_7_flowLayout->addItem(FAN_layout);


    // 噪声源开关
    NoiseSrcSw_layout = new QFormLayout();
    NoiseSrcSw_lable = new QLabel();
    NoiseSrcSw_Led_lable = new QLabel();
    NoiseSrcSw_lable->setText(NoiseSrcSw_Name);
    NoiseSrcSw_layout->addRow(NoiseSrcSw_lable, NoiseSrcSw_Led_lable);
    NoiseSrcSw_layout->setSpacing(1);
    NoiseSrcSw_layout->setContentsMargins(0, 0, 50, 0);

    widget_7_flowLayout->addItem(NoiseSrcSw_layout);

    // 当前天线角度
    antAng_layout = new QFormLayout();
    antAng_lable = new QLabel();
    antAng_Led_lable = new QLabel();
    antAng_lable->setText(NoiseSrcSw_Name);
    antAng_layout->addRow(antAng_lable, antAng_Led_lable);
    antAng_layout->setSpacing(1);
    antAng_layout->setContentsMargins(0, 0, 50, 0);

    widget_7_flowLayout->addItem(antAng_layout);

    Sensor_Status_update_data();

    ui->widget_2->setLayout(widget_1_flowLayout);
    ui->widget_4->setLayout(widget_2_flowLayout);
    ui->widget_6->setLayout(widget_3_flowLayout);
    ui->widget_8->setLayout(widget_4_flowLayout);
    ui->widget_10->setLayout(widget_5_flowLayout);
    ui->widget_14->setLayout(widget_7_flowLayout);



    set_SensorStatusDialog_stylesheet(systemconfigfdata.systemdata.set_skin);

    qTimer_t = new QTimer();
    connect(qTimer_t, &QTimer::timeout, this, &SensorStatusDialog::slot_Sensor_Status_Timer_outtime);
    qTimer_t->start(1000);
}

SensorStatusDialog::~SensorStatusDialog()
{
    delete ui;
}


void SensorStatusDialog::slot_Sensor_Status_Timer_outtime()
{
    if (sensorComm != NULL && sensorComm->Connection_status == true && sensorComm->facility_connect_status == true)
    {
        if (!isConnected)
        {
            connect(sensorComm, &classWidgetComm::signal_PowerStatus_data, this, &SensorStatusDialog::slot_Power_update_receive);
            connect(sensorComm, &classWidgetComm::signal_WorkStatus_data, this, &SensorStatusDialog::slot_Status_update_receive);
            connect(sensorComm, &classWidgetComm::siganl_sixWeather_data, this, &SensorStatusDialog::slot_Six_update_receive);
            connect(sensorComm, &classWidgetComm::signal_GPS_buff_data, this, &SensorStatusDialog::slot_GPS_update_receive);
            connect(sensorComm, &classWidgetComm::signal_SixTemp_buff_data, this, &SensorStatusDialog::slot_radioTemp_update_receive);
            connect(sensorComm, &classWidgetComm::signal_fTemp_Low_data, this, &SensorStatusDialog::slot_boldface_update_receive);
            connect(sensorComm, &classWidgetComm::signal_fTemp_LowAverage_data, this, &SensorStatusDialog::slot_normalTemp_update_receive);
            connect(sensorComm, &classWidgetComm::signal_windTurbParam_data, this, &SensorStatusDialog::slot_FAN_update_receive);
            connect(sensorComm, &classWidgetComm::signal_NoiseSrcSw_data, this, &SensorStatusDialog::slot_NoiseSrcSw_update_receive);
            connect(sensorComm, &classWidgetComm::signal_AntAng_data, this, &SensorStatusDialog::slot_antAng_update_receive);
            isConnected = true;
        }

        Sensor_Status_update_send();
    }
    else
    {
        sensorComm = widgetComm;
    }
    Sensor_Status_update_data();
}

void SensorStatusDialog::slot_Power_update_receive()
{
    power = sensorComm->get_PowerStatus_fun();
}

void SensorStatusDialog::slot_Status_update_receive()
{
    work = sensorComm->get_workStatus_fun();
}

void SensorStatusDialog::slot_Six_update_receive()
{
    weathersix= sensorComm->get_WeathersixEles_fun();
}

void SensorStatusDialog::slot_GPS_update_receive()
{
    gps= sensorComm->get_GPSParam_fun();
}

void SensorStatusDialog::slot_radioTemp_update_receive()
{
    six_temp= sensorComm->get_SixTemp_fun();
}

void SensorStatusDialog::slot_boldface_update_receive()
{
    lowtemp= sensorComm->get_fTempLow_fun();
}

void SensorStatusDialog::slot_normalTemp_update_receive()
{
    lowAveTemp= sensorComm->get_fTempLowAverage_fun();
}

void SensorStatusDialog::slot_FAN_update_receive()
{
    fan= sensorComm->get_windTurbParam_fun();
}

void SensorStatusDialog::slot_NoiseSrcSw_update_receive()
{
    NoiseSrcSw= sensorComm->get_NoiseSrcSw_fun();
}

void SensorStatusDialog::slot_antAng_update_receive()
{
    antAng= sensorComm->get_AntAngParam_fun();
}



// 该函数将label控件变成一个圆形指示灯，需要指定颜色color以及直径size
// color 0:grey 1:red 2:green 3:yellow
// size  单位是像素
void SensorStatusDialog::setLED(QLabel* label, int color, int size)
{
    // 将label中的文字清空
    label->setText("");
    // 先设置矩形大小
    // 如果ui界面设置的label大小比最小宽度和高度小，矩形将被设置为最小宽度和最小高度；
    // 如果ui界面设置的label大小比最小宽度和高度大，矩形将被设置为最大宽度和最大高度；
    QString min_width = QString("min-width: %1px;").arg(size);              // 最小宽度：size
    QString min_height = QString("min-height: %1px;").arg(size);            // 最小高度：size
    QString max_width = QString("max-width: %1px;").arg(size);              // 最小宽度：size
    QString max_height = QString("max-height: %1px;").arg(size);            // 最小高度：size
    // 再设置边界形状及边框
    QString border_radius = QString("border-radius: %1px;").arg(size / 2);    // 边框是圆角，半径为size/2
    QString border = QString("border:1px solid black;");                    // 边框为1px黑色
    // 最后设置背景颜色
    QString background = "background-color:";
    switch (color) {
    case 0:
        // 灰色
        background += "rgb(190,190,190)";
        break;
    case 1:
        // 红色
        background += "rgb(255,0,0)";
        break;
    case 2:
        // 绿色
        background += "rgb(0,255,0)";
        break;
    case 3:
        // 黄色
        background += "rgb(255,255,0)";
        break;
    default:
        break;
    }

    const QString SheetStyle = min_width + min_height + max_width + max_height + border_radius + border + background;
    label->setStyleSheet(SheetStyle);
}


void SensorStatusDialog::set_SensorStatusDialog_stylesheet(int index)
{

}


void SensorStatusDialog::Sensor_Status_update_send()
{
    // 六路接收机温度
    sensorComm->set_SixTemp_read_fun(true);
    // 噪声源开关
    sensorComm->set_NoiseSrcSw_read_fun(true);

    // GPS
    sensorComm->set_GPSParam_read_fun(true);

    // 电线角度电机
    sensorComm->set_AntAngParam_read_fun(true);

    // 4个常温源温度
    sensorComm->set_AntAngParam_read_fun(true);

    // 常温源平均温度
    sensorComm->set_fTempLowAverage_read_fun(true);

    // 天线角度电机
    sensorComm->set_AntAngParam_read_fun(true);

    // 气象6要素
    sensorComm->set_sixWeather_read_fun(true);

    // 风机开关
    sensorComm->set_windTurbParam_read_fun(true);

    // 工作状态
    sensorComm->set_workStatus_read_fun(true);
    
    // 电源状态
    sensorComm->set_PowerStatus_read_fun(true);
}

void SensorStatusDialog::Sensor_Status_update_data()
{
    // 通讯以建立连接
    if (sensorComm != NULL && sensorComm->Connection_status == true && sensorComm->facility_connect_status ==true)
    {
        // 电源
        Power_update_data(Power_Led_lable[0], power.colCardStat     );
        Power_update_data(Power_Led_lable[1], power.antMotStat      );
        Power_update_data(Power_Led_lable[2], power.metSixElemsStat );
        Power_update_data(Power_Led_lable[3], power.WindTurbStat    );

        // 状态
        Status_update_data(Status_Led_lable[0], work.colCardStat);
        Status_update_data(Status_Led_lable[1], work.antMotStat);
        Status_update_data(Status_Led_lable[2], work.metSixElemsStat);
        Status_update_data(Status_Led_lable[3], work.WindTurbStat);

        // 气象六要素
        Six_update_data();

        // GPS
        GPS_update_data();

        // 接收机温度
        RadioTemp_update_data();

        // 黑体温度
        boldface_Led_lable[0]->setText(QString::number(lowtemp.LowTemp1) + " ℃");
        boldface_Led_lable[1]->setText(QString::number(lowtemp.LowTemp2) + " ℃");
        boldface_Led_lable[2]->setText(QString::number(lowtemp.LowTemp3) + " ℃");
        boldface_Led_lable[3]->setText(QString::number(lowtemp.LowTemp4) + " ℃");

        NoiseSrcSw_Led_lable->setText(QString::number(lowtemp.LowTemp4) + " ℃");

        antAng_Led_lable->setText(QString(NoiseSrcSw?"启用":"停用") + " °");

        // 常温源平均温度
        normalTemp_Led_lable->setText(QString::number(lowAveTemp) + " ℃");

        // 风机转速
        FAN_Led_lable->setText(QString::number(fan.Speed) + " Rpm");
    }
    else
    {
        // 通讯未连接
        for (int i = 0; i < 4; i++)
        {
            setLED(Power_Led_lable[i], 0, 16);
            setLED(Status_Led_lable[i], 0, 16);
        }
        Six_Led_lable[0]->setText("NULL");
        Six_Led_lable[1]->setText("NULL");
        Six_Led_lable[2]->setText("NULL");
        Six_Led_lable[3]->setText("NULL");
        Six_Led_lable[4]->setText("NULL");
        Six_Led_lable[5]->setText("NULL");


        GPS_Led_lable[0]->setText("NULL");
        GPS_Led_lable[1]->setText("NULL");
        GPS_Led_lable[2]->setText("NULL");
        GPS_Led_lable[3]->setText("NULL");
        GPS_Led_lable[4]->setText("NULL");
        GPS_Led_lable[5]->setText("NULL");
        GPS_Led_lable[6]->setText("NULL");
        GPS_Led_lable[7]->setText("NULL");

        for (int i = 0; i < 6; i++)
        {
            radioTemp_Led_lable[i]->setText("NULL");
        }


        // 黑体温度
        boldface_Led_lable[0]->setText("NULL");
        boldface_Led_lable[1]->setText("NULL");
        boldface_Led_lable[2]->setText("NULL");
        boldface_Led_lable[3]->setText("NULL");

        NoiseSrcSw_Led_lable->setText("NULL");

        antAng_Led_lable->setText("NULL");

        normalTemp_Led_lable->setText("NULL");

        FAN_Led_lable->setText("NULL");
    }
}

void SensorStatusDialog::Power_update_data(QLabel* label, uchar data)
{
    if (data == 0)
    {
        setLED(label, 1, 16);
    }
    else if (data == 1)
    {
        setLED(label, 2, 16);
    }
    else
    {
        setLED(label, 0, 16);
    }
}

void SensorStatusDialog::Status_update_data(QLabel* label, uchar data)
{
    if (data == 0)
    {
        setLED(label, 2, 16);
    }
    else if (data == 1)
    {
        setLED(label, 1, 16);
    }
    else
    {
        setLED(label, 0, 16);
    }
}

void SensorStatusDialog::Six_update_data()
{
    Six_Led_lable[0]->setText(QString::number(weathersix.Humidity) + " %RH");
    Six_Led_lable[1]->setText(QString::number(weathersix.Temp) + " ℃");
    Six_Led_lable[2]->setText(QString::number(weathersix.AirPress) + " Pa");
    Six_Led_lable[3]->setText(QString::number(weathersix.WindSpeed) + " M/s");
    Six_Led_lable[4]->setText(QString::number(weathersix.WindDir) + " °");
    Six_Led_lable[5]->setText(QString::number(weathersix.Rain10Minute) + " mm");

}

void SensorStatusDialog::GPS_update_data()
{
    GPS_Led_lable[0]->setText(QString::number(gps.longitude) + " °");
    GPS_Led_lable[1]->setText(QString::number(gps.latitude) + " °");
    GPS_Led_lable[2]->setText(QString::number(gps.UTC_Time) + "  ");
    GPS_Led_lable[3]->setText(QString::number(gps.UTC_Date) + "  ");
    GPS_Led_lable[4]->setText(QString::number(gps.CenturySecond) + " 秒");
    GPS_Led_lable[5]->setText(QString::number(gps.HDOP) + "  ");
    GPS_Led_lable[6]->setText(QString::number(gps.HeightSeaLevel) + " 米");
    GPS_Led_lable[7]->setText(QString::number(gps.HeightEarthLevel) + " 米");
}

void SensorStatusDialog::RadioTemp_update_data()
{
    for (int i = 0; i < 6; i++)
    {
        radioTemp_Led_lable[i]->setText(QString::number(six_temp.Temp[i]) + " ℃");
    }
}







