﻿#ifndef PRINTSCREEN_H
#define PRINTSCREEN_H

#include <QWidget>
#include <QCloseEvent>
#include <QPixmap>
#include <QDesktopServices>
#include <QClipboard>
#include <QMenu>    //右键菜单
#include <QPainter> //画笔
#include <QShowEvent>

extern QList<QPixmap> pixmaps;

/**
 * @brief The PrintScreen class
 * @param 区域截屏功能
 */
class PrintScreen : public QWidget
{
    Q_OBJECT
public:
    PrintScreen(QWidget *parent = nullptr);
    ~PrintScreen();

private:
    /**
     * @brief 初始化截图窗口的背景和尺寸
     */
    void InitWindow(void);
    void CreateMenu(void);
    /**
     * @brief 根据起始点和终止点计算矩形区域
     * @param beginPoint 矩形区域的起始点
     * @param endPoint 矩形区域的终止点
     * @return 返回根据两点计算出的 QRect 对象
     */
    QRect GetRect(const QPoint &beginPoint, const QPoint &endPoint);

protected:
    // 事件处理方法
    void mousePressEvent(QMouseEvent *event);
    void mouseMoveEvent(QMouseEvent *event);
    void mouseReleaseEvent(QMouseEvent *event);
    void keyPressEvent(QKeyEvent *event);
    void paintEvent(QPaintEvent *event);
    QPixmap copytoPasteboard(bool hideflag); // 将截图复制到粘贴板
    void savetoImage(void);                  // 截图保存到图片
    // void copytoPDF(void);                    //截图复制到文档
    // void printtoPDF(void);                   //将制到文档的截图打印出来
    void saveImage(void);

private:
    // 成员变量
    bool m_isMousePress = false;    // 是否按下鼠标
    bool m_captureComplete = false; // 截图是否完成
    bool m_isDragging = false;      // 是否正在拖动截图区域

    QPixmap m_loadPixmap;    // 加载的屏幕截图
    QPixmap m_capturePixmap; // 截取的屏幕区域

    int m_screenWidth;  // 屏幕宽度
    int m_screenHeight; // 屏幕高度

    QPoint m_beginPoint;   // 截图开始点
    QPoint m_endPoint;     // 截图结束点
    QPoint m_dragPosition; // 拖动时的鼠标位置

    QPainter m_painter; // 绘图器对象

    QMenu *menu;
};
#endif // PRINTSCREEN_H
