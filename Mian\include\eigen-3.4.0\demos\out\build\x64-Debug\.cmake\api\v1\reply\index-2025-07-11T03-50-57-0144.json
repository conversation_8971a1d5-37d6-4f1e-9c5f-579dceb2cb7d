{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe", "cpack": "D:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cpack.exe", "ctest": "D:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/ctest.exe", "root": "D:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20"}, "version": {"isDirty": false, "major": 3, "minor": 20, "patch": 21032501, "string": "3.20.21032501-MSVC_2", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-4c321dcbebd4c9110ce1.json", "kind": "codemodel", "version": {"major": 2, "minor": 2}}, {"jsonFile": "cache-v2-969461175e3ff102955c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-5a0805746c962ba00476.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-1416e559c7cbb5beeab8.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-MicrosoftVS": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "cmakeFiles", "version": 1}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}], "responses": [{"jsonFile": "cache-v2-969461175e3ff102955c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-5a0805746c962ba00476.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "codemodel-v2-4c321dcbebd4c9110ce1.json", "kind": "codemodel", "version": {"major": 2, "minor": 2}}, {"jsonFile": "toolchains-v1-1416e559c7cbb5beeab8.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}]}}}}