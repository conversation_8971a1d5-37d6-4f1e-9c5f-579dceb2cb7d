﻿#ifndef CONTROLSETPARAMETERDIALOG_H
#define CONTROLSETPARAMETERDIALOG_H

#include "ui_ControlSetParameterDialog.h"
#include <QDialog>
#include <QTimer>
#include <QElapsedTimer>
#include <QObject>
#include <atomic>
#include "UI/comm/classwidgetcomm.h"

namespace Ui
{
    class ControlSetParameterDialog;
}

class ControlSetParameterDialog : public QDialog
{
    Q_OBJECT
    // public:

public:
    explicit ControlSetParameterDialog(QWidget *parent = nullptr);
    ~ControlSetParameterDialog();

signals:

private slots:
    // 槽函数，用于处理天线和噪声源动作是否完成
    void solt_setAntennaAngleAndNoiseSrc(QVector<ushort> var);

    // 槽函数, 定时器超时
    void slot_timerTimeout();

    // 槽函数, 更新电压数据
    void slot_updateVoltageData(QVector<ushort> var);

    // 槽函数, 常温源平均温度和常温源亮温数据更新
    void slot_updateBrightTempAndAvgTemp(QVector<ushort> var);

    // 槽函数, 定标方式切换
    void slot_changeCalibrationMode();

    // 槽函数, 切换自动模式
    void slot_changeAutoMode();

    // 槽函数, 天空倾斜角度个数据更新
    void slot_updateSkyTiltAngle();

    // 槽函数, 参数传递更新
    void slot_updateParameter();

    // 槽函数，读取按钮触发
    void slot_readButtonTriggered();

    // 槽函数，参数计算按钮触发
    void slot_paramCalcButtonTriggered();

    // 槽函数，自动定标按钮触发
    void slot_changeAutoCalibrationMode();

    // 槽函数，参数保存按钮触发
    void slot_saveButtonTriggered();

    // 槽函数，数据清除按钮触发
    void slot_clearButtonTriggered();

    // 槽函数，定标参数读取按钮触发
    void slot_readCalibParamButtonTriggered();

private:
    // 定标模式枚举
    enum CalibrationType
    {
        MANUAL,          // 手动黑体定标
        MANUAL_SKY_TILT, // 手动天空倾斜定标
        AUTO_BLACKBODY,  // 自动黑体定标
        SKY_TILT         // 天空倾斜定标
    };

    enum CalibListRefreshIndex
    {
        Value_Tn = 0,
        Value_C,
        Value_Tsys,
        Value_LN2_Voltage,
        Value_AT_Voltage,
        Value_AT_Temp,
        Value_Noise_Voltage,
        Value_Angle1,
        Value_Angle2,
        Value_Angle3,
        Value_Angle4,
        Value_Angle5,
        Value_Clear
    };

    /**
     * @brief 黑体定标状态机步骤
     */
    enum BbCalibrationStep
    {
        NoCail = 0,            ///< 定标未开始或已完成
        SET_LN2,               ///< 转向液氮源(77K)，准备读取冷端电压
        ReadBbVoltageLN2,      ///< 读取液氮源电压值(冷端)
        SET_HOT,               ///< 转向常温源(300K)，准备读取热端电压
        ReadBbVoltageNT,       ///< 读取常温源电压值(热端)
        SET_NOISE,             ///< 转向噪声源，准备读取噪声电压
        ReadBbVoltageNoiseSrc, ///< 读取噪声源电压值
        BbCalibrationCalculate ///< 计算系统参数(C, Tsys, Tn)
    };

    //倾斜天空定标状态机步骤
    enum SkyTiltCalibrationStep
    {
        NoSkyTiltCail = 0,            ///< 定标未开始或已完成
        SET_SKYTILT1,               ///< 转向天空倾斜角度1，准备读取冷端电压
        ReadBbVoltageSkyTilt1,       ///< 读取角度1电压值
        SET_SKYTILT2,               ///< 转向天空倾斜角度2，准备读取热端电压
        ReadBbVoltageSkyTilt2,       ///< 读取角度2电压值
        SET_SKYTILT3,               ///< 转向天空倾斜角度3，准备读取噪声源电压
        ReadBbVoltageSkyTilt3,       ///< 读取角度3电压值
        SET_SKYTILT4,               ///< 转向天空倾斜角度4，准备读取噪声源电压
        ReadBbVoltageSkyTilt4,       ///< 读取角度4电压值
        SET_SKYTILT5,               ///< 转向天空倾斜角度5，准备读取噪声源电压
        ReadBbVoltageSkyTilt5,       ///< 读取角度5电压值
        SkyTiltCalibrationCalculate ///< 计算系统参数(Tn)
    };
    /**
     * @brief 定标参数结构体
     */
    typedef struct
    {
        float lnSrcVolt[16];       // 液氮源电压
        float normTempSrcVolt[16]; // 常温源电压
        float normTempSrcTemp[16]; // 常温源亮温
        float noiseSrcVolt[16];    // 噪声源电压
        float SkyTiltAng1[16];     // 天线倾斜角度1
        float SkyTiltAng2[16];     // 天线倾斜角度2
        float SkyTiltAng3[16];     // 天线倾斜角度3
        float SkyTiltAng4[16];     // 天线倾斜角度4
        float SkyTiltAng5[16];     // 天线倾斜角度5
    } CALIB_PARAM_STRUCT;

    typedef struct
    {
        float Tn[16];   // 定标参数Tn
        float C[16];    // 定标参数C
        float Tsys[16]; // 定标参数Tsys
    } CALIB_CORRECT_STRUCT;

    Ui::ControlSetParameterDialog *ui;

    // 定标自动定标标志
    bool m_AutoCalibFlag = false;

    // 自动定标按钮标志位
    bool m_AutoCalibPushButtonFlag = false;

    // 液氮源已读取标志位
    bool m_LiqN2SrcReadFlag = false;
    // 噪声源已读取标志位
    bool m_NoiseSrcReadFlag = false;
    // 常温源已读取标志位
    bool m_SrcTempReadFlag = false;

    // 定时器
    QTimer *m_Timer;

    // 当前目标角度值
    float m_AntTgtAng;
    // 当前噪声源状态
    bool m_NoiseSrcSw;

    // 液氮源角度
    float m_LN2_Angle;
    // 常温源角度
    float m_NT_Angle;
    // 噪声源角度
    float m_Noise_Angle;
    // 液氮亮温
    float m_ln2Temp;
    // 天空倾斜角度个数
    uint32_t m_SkyTiltAngNum;
    // 天空倾斜角度1
    float m_SkyTiltAng1;
    // 天空倾斜角度2
    float m_SkyTiltAng2;
    // 天空倾斜角度3
    float m_SkyTiltAng3;
    // 天空倾斜角度4
    float m_SkyTiltAng4;
    // 天空倾斜角度5
    float m_SkyTiltAng5;

    // 观测时长
    uint32_t m_ObsTime;
    // 当前时长
    uint32_t m_CurTime;

    // 黑体定标状态机步骤
    BbCalibrationStep m_BbCalibrationStep = NoCail;

    // 天空倾斜定标状态机步骤
    SkyTiltCalibrationStep  m_SkyTiltCalibrationStep = NoSkyTiltCail;

    CALIB_PARAM_STRUCT m_calibData;      // 定标数据
    CALIB_CORRECT_STRUCT m_calibCorrect; // 定标结果

    // 设置天线角度，和 噪声源开关
    void setAntennaAngleAndNoiseSrc(float antAng, bool isOpen);

    // 设置读取液氮源电压值
    void setReadBbVoltageLN2();

    // 设置读取常温源电压值和亮温
    void setReadBbVoltageNT();

    // 设置读取噪声源电压值
    void setReadBbVoltageNoiseSrc();

    // 设置读取角度电压值
    void setReadVoltageAngle();

    // 界面表格刷新
    void table_update_fun(uchar columnIndex, float value[]);

    // 计算参数并显示界面
    void Settlement_fun();
    // 计算参数C
    void Settlement_C_fun();
    // 计算参数Tsys
    void Settlement_Tsys_fun();
    // 计算参数Tn
    void Settlement_Alpha_fun();

    // 定标参数C保存
    void Calibration_param_C_fun();
    // 定标参数Tsys保存
    void Calibration_param_Tsys_fun();
    // 定标参数Tn保存
    void Calibration_param_Alpha_fun();

    // 读取定标参数
    void Calibration_param_Read_fun();

    // 日志记录
    void log_record(QString log);

protected:
    // virtual void closeEvent(QCloseEvent *event) override;
    // virtual void showEvent(QShowEvent *event);
};

#endif // CONTROLSETPARAMETERDIALOG_H
