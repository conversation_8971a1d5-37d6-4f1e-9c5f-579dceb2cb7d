﻿#include "tools/Data_storage/sqlite_faultrecord_data.h"
#include <QHeaderView>
sqlite_FaultRecord_data::sqlite_FaultRecord_data(QString path)
{
    createDb(path);
    if (!tableExists("FaultRecord_data"))
    {
        execute(
            "CREATE TABLE FaultRecord_data ("
            "id INTEGER PRIMARY KEY AUTOINCREMENT,"
            "number TEXT NOT NULL,"
            "type TEXT NOT NULL,"
            "content TEXT NOT NULL,"
            "grade TEXT NOT NULL,"
            "u_time TEXT NOT NULL,"
            "time TEXT NOT NULL);");
    }

    myCustomSqlTableModel = new CustomSqlTableModel("FaultRecord_data");
    myCustomSqlTableModel->setTable("FaultRecord_data");
    myCustomSqlTableModel->setEditStrategy(QSqlTableModel::OnFieldChange); // OnFieldChange数据改变就提交, OnRowChange行改变时提交, OnManualSubmit不主动提交只有使用submit才提交
    myCustomSqlTableModel->select();
    myCustomSqlTableModel->setHeaderData(1, Qt::Horizontal, tr("告警编号"));
    myCustomSqlTableModel->setHeaderData(2, Qt::Horizontal, tr("告警类型"));
    myCustomSqlTableModel->setHeaderData(3, Qt::Horizontal, tr("告警内容"));
    myCustomSqlTableModel->setHeaderData(4, Qt::Horizontal, tr("告警等级"));
    myCustomSqlTableModel->setHeaderData(5, Qt::Horizontal, tr("告警时间"));
    myCustomSqlTableModel->setHeaderData(6, Qt::Horizontal, tr("创建时间"));
}

sqlite_FaultRecord_data::~sqlite_FaultRecord_data()
{
    QObject::disconnect(coon);
    m_dbCount--;
}

bool sqlite_FaultRecord_data::select_sqlitedata(QString insertSQL)
{
    QSqlQuery sql_query(m_db);
    sql_query.prepare(insertSQL);
    if (!sql_query.exec())
    {
        QSqlError error = sql_query.lastError();
        qDebug() << error.text();
        return false;
    }
    else
    {
        data.clear();
        while (sql_query.next())
        {
            struct FaultRecord_SQ_GROUP temp;
            temp.number = (sql_query.value("number").toString());
            temp.type = (sql_query.value("type").toString());
            temp.content = (sql_query.value("content").toString());
            temp.grade = (sql_query.value("grade").toString());
            temp.u_time = (sql_query.value("u_time").toString());
            temp.time = (sql_query.value("time").toString());
            data.append(temp);
        }
        return true;
    }
}

void sqlite_FaultRecord_data::use_CustomSqlTableModel(QTableView *view)
{
    if (myCustomSqlTableModel_status)
        return;
    view->setModel(myCustomSqlTableModel);
    view->setColumnHidden(0, true);
    tableview = view;

    set_column_edit(6, false);
    // view->setEditTriggers(QAbstractItemView::NoEditTriggers);
    view->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
    coon = connect(tableview->horizontalHeader(), &QHeaderView::geometriesChanged, [&]()
                   {
                       if (tableview == nullptr)
                           return;
                       int totalwidth = tableview->viewport()->width();
                       int ratiosum = std::accumulate(ratios.begin(), ratios.end(), 0);

                       tableview->setColumnWidth(1, (totalwidth * ratios[0]) / ratiosum);
                       tableview->setColumnWidth(2, (totalwidth * ratios[1]) / ratiosum);
                       tableview->setColumnWidth(3, (totalwidth * ratios[2]) / ratiosum);
                       tableview->setColumnWidth(4, (totalwidth * ratios[3]) / ratiosum);
                       tableview->setColumnWidth(5, (totalwidth * ratios[4]) / ratiosum);
                       tableview->setColumnWidth(6, (totalwidth * ratios[5]) / ratiosum);
                   });
    myCustomSqlTableModel_status = true;
}
/*
 *插入故障记录数据
 * 参数:
 *    data 故障数据
 * 返回值: 0 插入成功
 *        1 参数输入存在空值
 *        2 时间数据异常
 *        3 同步数据失败
 *        4 旧数据删除失败
 */
int sqlite_FaultRecord_data::insertCountLimit(struct FaultRecord_SQ_GROUP data)
{
    if (data.content.isEmpty() || data.grade.isEmpty() || data.number.isEmpty() || data.time.isEmpty() || data.u_time.isEmpty() || data.type.isEmpty())
    {
        return 1;
    }

    if (myCustomSqlTableModel_status)
    {
        const int newrow = myCustomSqlTableModel->rowCount();
        myCustomSqlTableModel->insertRow(newrow);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 1), data.number);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 2), data.type);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 3), data.content);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 4), data.grade);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 5), data.u_time);

        if (data.time == "beijing")
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 6), QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss"));
        }
        else if (data.time == "UTC")
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 6), QDateTime::currentDateTimeUtc().toString("yyyy-MM-dd HH:mm:ss"));
        }
        else
        {
            if (!QDateTime::fromString(data.time, "yyyy-MM-dd HH:mm:ss").isValid())
            {
                myCustomSqlTableModel->removeRow(newrow);

                return 2;
            }

            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 5), data.time);
        }
        tableview->scrollToBottom();
    }
    current_data.append(data);
    if (current_data.count() >= CURRENT_DATA_COUNT)
    {
        // 将数据加载到数据库
        int re = syncToDatabase();
        if (re == 1)
            return 3;
        else if (re == 2)
            return 4;
        else
            return 0;
    }
    return 0;
}
/*
 *通过时间范围搜索故障几率
 * 参数:
 *    无
 * 返回值: 0 同步成功
 *        1 同步失败
 *        2 旧数据删除失败
 *        3 数据同步成功，搜索成功
 *        4 搜索失败
 */
int sqlite_FaultRecord_data::select_usetime_SqlTableModel(QString start_time, QString stop_time)
{
    // 在插入数据时，如果设置CURRENT_DATA_COUNT>1,则可能存在数据只加载到了模型，未加载到数据库，在查询前为防止数据丢失，先进行数据同步保存
    int re = syncToDatabase();
    if (re != 0)
        return re;
    QString sql = "";
    if (start_time == "begin" && stop_time == "end")
    {
        sql = "";
    }
    else
    {
        sql = QString("time BETWEEN '%1' AND '%2'").arg(start_time, stop_time);
    }

    myCustomSqlTableModel->setFilter(sql);
    if (myCustomSqlTableModel->select())
    {

        return 3;
    }
    else
        return 4;
}
/*
 *获取指定时间段的数据类型及各类型的个数
 * 参数:
 *    start_time：开始时间
 *    stop_time：结束时间
 * 返回值: 查询到的数据
 */
QMap<QString, int> sqlite_FaultRecord_data::get_startTostop_type(QString start_time, QString stop_time)
{
    QMap<QString, int> data;
    QString querystr = "";
    if (start_time == "begin" && stop_time == "end")
    {
        querystr = QString("SELECT type,COUNT(*) FROM FaultRecord_data GROUP BY type ");
    }
    else
    {
        querystr = QString("SELECT type,COUNT(*) FROM FaultRecord_data WHERE time BETWEEN '%1' AND '%2' GROUP BY type ").arg(start_time, stop_time);
    }
    QSqlQuery sql_query(m_db);

    if (!sql_query.exec(querystr))
    {
        return data;
    }
    while (sql_query.next())
    {
        QString type = sql_query.value(0).toString();
        int count = sql_query.value(1).toInt();
        data[type] = count;
    }

    return data;
}
/*
 *删除指定时间节点前的数据
 * 参数:
 *    无
 * 返回值: 0 删除成功，刷新模型成功
 *        1 删除失败
 *        2 删除成功，刷新模型失败
 */
int sqlite_FaultRecord_data::deleteOldsql(QString time)
{
    QSqlQuery sql_query(m_db);
    sql_query.prepare("DELETE FROM FaultRecord_data WHERE time < ?");
    sql_query.addBindValue(time);
    if (!sql_query.exec())
    {
        return 1;
    }

    if (myCustomSqlTableModel->select())
        return 0;
    else
        return 2;
}
/*
 *通过类型搜索故障数据
 * 参数:
 *    无
 * 返回值:
 *      true 查询成功
 *      false 查询失败
 *
 *
 *
 */
bool sqlite_FaultRecord_data::select_usetype_SqlTableModel(QString type)
{
    if (select_type_SqlTableModel("type", type) != 3)
        return false;
    return true;
}
/*
 *搜索制定类型的数据
 * 参数:
 *    无
 * 返回值: 0 同步成功
 *        1 同步失败
 *        2 旧数据删除失败
 *        3 数据同步成功，搜索成功
 *        4 搜索失败
 */
int sqlite_FaultRecord_data::select_type_SqlTableModel(QString key, QString type)
{

    // 在插入数据时，如果设置CURRENT_DATA_COUNT>1,则可能存在数据只加载到了模型，未加载到数据库，在查询前为防止数据丢失，先进行数据同步保存
    int re = syncToDatabase();
    if (re != 0)
        return re;

    QString sql = "";
    if (!type.isEmpty())
    {
        sql = QString("%1 = '%2'").arg(key, type);
    }

    myCustomSqlTableModel->setFilter(sql);
    if (myCustomSqlTableModel->select())
        return 3;
    else
        return 4;
}
/*
 *通过等级搜索故障数据
 * 参数:
 *    无
 * 返回值:
 *      true 查询成功
 *      false 查询失败
 */
bool sqlite_FaultRecord_data::select_usegrade_SqlTableModel(QString type)
{

    if (select_type_SqlTableModel("grade", type) != 3)
        return false;
    return true;
}

void sqlite_FaultRecord_data::set_auto_delect_en(bool en)
{
    auto_delect_en = en;
}

bool sqlite_FaultRecord_data::set_column_color(QVector<QColor> c)
{

    int count = 0;
    QSqlQuery sql_query(m_db);
    sql_query.prepare("SELECT COUNT(*) FROM FaultRecord_data");
    if (!sql_query.exec() || !sql_query.next())
    {
        QSqlError error = sql_query.lastError();
        qDebug() << error.text();
        return false;
    }
    else
    {
        count = sql_query.value(0).toInt();
    }
    qDebug() << count;
    if (myCustomSqlTableModel_status)
    {
        if (c.count() != myCustomSqlTableModel->columnCount())
            return false;
        for (int i = 0; i < myCustomSqlTableModel->columnCount(); i++)
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(count - 1, i), c[i], Qt::ForegroundRole);
        }
        return true;
    }
    return false;
}

bool sqlite_FaultRecord_data::set_column_color(int row, int column, const QColor &c)
{
    int count = 0;
    QSqlQuery sql_query(m_db);
    sql_query.prepare("SELECT COUNT(*) FROM FaultRecord_data");
    if (!sql_query.exec() || !sql_query.next())
    {
        QSqlError error = sql_query.lastError();
        qDebug() << error.text();
        return false;
    }
    else
    {
        count = sql_query.value(0).toInt();
    }
    qDebug() << count;
    if (myCustomSqlTableModel_status)
    {
        if (count < row && myCustomSqlTableModel->columnCount() < column)
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(row, column), c, Qt::ForegroundRole);
            return true;
        }
    }
    return false;
}

void sqlite_FaultRecord_data::set_column_edit(int column, bool en)
{
    myCustomSqlTableModel->set_edit(column, en);
}

/*
 *同步模型数据到数据库，并检查数据库数据是否超过阈值
 * 参数:
 *    无
 * 返回值: 0 同步成功
 *        1 同步失败
 *        2 旧数据删除失败
 */
int sqlite_FaultRecord_data::syncToDatabase()
{
    if (current_data.isEmpty())
        return 0;
    //    m_db.transaction();

    // 提交到数据库
    if (!myCustomSqlTableModel->submitAll())
    {

        return 1;
    }

    current_data.clear();

    if (auto_delect_en)
    {
        // 当数据大于DATEBASE_DATA_COUNT时删除旧数据
        QSqlQuery cutoffquery(m_db);
        cutoffquery.prepare("SELECT id FROM FaultRecord_data ORDER BY id DESC LIMIT 1 OFFSET ?");
        cutoffquery.addBindValue(DATEBASE_DATA_COUNT - 1);

        if (cutoffquery.exec() && cutoffquery.next())
        {
            quint64 cutoffid = cutoffquery.value(0).toULongLong();
            QSqlQuery delquery(m_db);
            delquery.prepare("DELETE FROM FaultRecord_data WHERE id <= ?");
            delquery.addBindValue(cutoffid);
            if (!delquery.exec())
            {
                return 2;
            }
        }
    }
    //    m_db.commit();
    myCustomSqlTableModel->setFilter(""); // 清除过滤条件
    myCustomSqlTableModel->select();
    return 0;
}
