{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "EigenBlas", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "blas::@6890427a1f51a3e7e1df", "jsonFile": "target-blas-Debug-6d28d2c761fa164f3652.json", "name": "blas", "projectIndex": 0}, {"directoryIndex": 0, "id": "eigen_blas_static::@6890427a1f51a3e7e1df", "jsonFile": "target-eigen_blas_static-Debug-42203fd7cda87cf54786.json", "name": "eigen_blas_static", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/blas/out/build/x64-Debug", "source": "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/blas"}, "version": {"major": 2, "minor": 2}}