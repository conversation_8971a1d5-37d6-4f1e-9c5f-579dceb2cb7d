﻿#include "CControlSetParameterData.h"
#include <QDir>
#include <QDebug>
#include <QDate>
CControlSetParameterData::CControlSetParameterData(QObject *parent) : QObject(parent)
{
    loadFromFile();
    creat_configfile();
}
CControlSetParameterData::~CControlSetParameterData()
{
}
void CControlSetParameterData::loadFromFile()
{
    QString filePath = "F:/QtMicrowaveRadiation/ControlSetParameData.ini";
    QSettings settings("Dataconfig/CControlSetParameterData.ini", QSettings::IniFormat);
    // 开关机参数
    parameterdata.open_close_down = settings.value("ControlSetParameData/open_close_down").toInt();
    parameterdata.close_down_process = settings.value("ControlSetParameData/close_down_process").toInt();
    // 工作模式参数
    parameterdata.currence_woke_mode = settings.value("ControlSetParameData/currence_woke_mode").toInt();
    parameterdata.return_woke_mode = settings.value("ControlSetParameData/return_woke_mode").toInt();
    // 系统参数设置
    parameterdata.sampling_time = settings.value("ControlSetParameData/sampling_time").toDouble();
    parameterdata.sampling_rate = settings.value("ControlSetParameData/sampling_rate").toDouble();
    parameterdata.black_num = settings.value("ControlSetParameData/black_num").toDouble();
    parameterdata.maintain_time = settings.value("ControlSetParameData/maintain_time").toDouble();
    parameterdata.return_mark = settings.value("ControlSetParameData/return_mark").toDouble();
    parameterdata.return_sampling_time = settings.value("ControlSetParameData/return_sampling_time").toDouble();
    parameterdata.return_sampling_rate = settings.value("ControlSetParameData/return_sampling_rate").toDouble();
    parameterdata.return_black_num = settings.value("ControlSetParameData/return_black_num").toDouble();
    parameterdata.return_maintain_time = settings.value("ControlSetParameData/return_maintain_time").toDouble();
    // 设备运行时间
    parameterdata.sum_time = settings.value("ControlSetParameData/sum_time").toString();
    parameterdata.open_time = settings.value("ControlSetParameData/open_time").toString();
    // 设备定标
    parameterdata.dingbiao_type = settings.value("ControlSetParameData/dingbiao_type").toInt();
    parameterdata.dingbiao_order = settings.value("ControlSetParameData/dingbiao_order").toDouble();
    parameterdata.jifen_time = settings.value("ControlSetParameData/jifen_time").toInt();
    parameterdata.period = settings.value("ControlSetParameData/period").toInt();
    parameterdata.return_dingbiao_type = settings.value("ControlSetParameData/return_dingbiao_type").toInt();
    parameterdata.dingbiao_status = settings.value("ControlSetParameData/dingbiao_status").toInt();
    parameterdata.dingbiao_process = settings.value("ControlSetParameData/dingbiao_process").toString();
    parameterdata.currence_jifen_time = settings.value("ControlSetParameData/currence_jifen_time").toString();
    parameterdata.currence_period = settings.value("ControlSetParameData/currence_period").toString();
    // 设置定标参数
    parameterdata.set_accept_gain1 = settings.value("ControlSetParameData/set_accept_gain1").toDouble();
    parameterdata.set_accept_gain2 = settings.value("ControlSetParameData/set_accept_gain2").toDouble();
    parameterdata.set_accept_gain3 = settings.value("ControlSetParameData/set_accept_gain3").toDouble();
    parameterdata.set_accept_gain4 = settings.value("ControlSetParameData/set_accept_gain4").toDouble();
    parameterdata.set_accept_gain5 = settings.value("ControlSetParameData/set_accept_gain5").toDouble();
    parameterdata.set_accept_gain6 = settings.value("ControlSetParameData/set_accept_gain6").toDouble();
    parameterdata.set_accept_gain7 = settings.value("ControlSetParameData/set_accept_gain7").toDouble();
    parameterdata.set_accept_gain8 = settings.value("ControlSetParameData/set_accept_gain8").toDouble();
    parameterdata.set_accept_gain9 = settings.value("ControlSetParameData/set_accept_gain9").toDouble();
    parameterdata.set_accept_gain10 = settings.value("ControlSetParameData/set_accept_gain10").toDouble();
    parameterdata.set_accept_gain11 = settings.value("ControlSetParameData/set_accept_gain11").toDouble();
    parameterdata.set_accept_gain12 = settings.value("ControlSetParameData/set_accept_gain12").toDouble();
    parameterdata.set_accept_gain13 = settings.value("ControlSetParameData/set_accept_gain13").toDouble();
    parameterdata.set_accept_gain14 = settings.value("ControlSetParameData/set_accept_gain14").toDouble();

    parameterdata.set_diode1 = settings.value("ControlSetParameData/set_diode1").toDouble();
    parameterdata.set_diode2 = settings.value("ControlSetParameData/set_diode2").toDouble();
    parameterdata.set_diode3 = settings.value("ControlSetParameData/set_diode3").toDouble();
    parameterdata.set_diode4 = settings.value("ControlSetParameData/set_diode4").toDouble();
    parameterdata.set_diode5 = settings.value("ControlSetParameData/set_diode5").toDouble();
    parameterdata.set_diode6 = settings.value("ControlSetParameData/set_diode6").toDouble();
    parameterdata.set_diode7 = settings.value("ControlSetParameData/set_diode7").toDouble();
    parameterdata.set_diode8 = settings.value("ControlSetParameData/set_diode8").toDouble();
    parameterdata.set_diode9 = settings.value("ControlSetParameData/set_diode9").toDouble();
    parameterdata.set_diode10 = settings.value("ControlSetParameData/set_diode10").toDouble();
    parameterdata.set_diode11 = settings.value("ControlSetParameData/set_diode11").toDouble();
    parameterdata.set_diode12 = settings.value("ControlSetParameData/set_diode12").toDouble();
    parameterdata.set_diode13 = settings.value("ControlSetParameData/set_diode13").toDouble();
    parameterdata.set_diode14 = settings.value("ControlSetParameData/set_diode14").toDouble();

    parameterdata.set_noise_temp1 = settings.value("ControlSetParameData/set_noise_temp1").toDouble();
    parameterdata.set_noise_temp2 = settings.value("ControlSetParameData/set_noise_temp2").toDouble();
    parameterdata.set_noise_temp3 = settings.value("ControlSetParameData/set_noise_temp3").toDouble();
    parameterdata.set_noise_temp4 = settings.value("ControlSetParameData/set_noise_temp4").toDouble();
    parameterdata.set_noise_temp5 = settings.value("ControlSetParameData/set_noise_temp5").toDouble();
    parameterdata.set_noise_temp6 = settings.value("ControlSetParameData/set_noise_temp6").toDouble();
    parameterdata.set_noise_temp7 = settings.value("ControlSetParameData/set_noise_temp7").toDouble();
    parameterdata.set_noise_temp8 = settings.value("ControlSetParameData/set_noise_temp8").toDouble();
    parameterdata.set_noise_temp9 = settings.value("ControlSetParameData/set_noise_temp9").toDouble();
    parameterdata.set_noise_temp10 = settings.value("ControlSetParameData/set_noise_temp10").toDouble();
    parameterdata.set_noise_temp11 = settings.value("ControlSetParameData/set_noise_temp11").toDouble();
    parameterdata.set_noise_temp12 = settings.value("ControlSetParameData/set_noise_temp12").toDouble();
    parameterdata.set_noise_temp13 = settings.value("ControlSetParameData/set_noise_temp13").toDouble();
    parameterdata.set_noise_temp14 = settings.value("ControlSetParameData/set_noise_temp14").toDouble();

    parameterdata.set_line_paraeter1 = settings.value("ControlSetParameData/set_line_paraeter1").toDouble();
    parameterdata.set_line_paraeter2 = settings.value("ControlSetParameData/set_line_paraeter2").toDouble();
    parameterdata.set_line_paraeter3 = settings.value("ControlSetParameData/set_line_paraeter3").toDouble();
    parameterdata.set_line_paraeter4 = settings.value("ControlSetParameData/set_line_paraeter4").toDouble();
    parameterdata.set_line_paraeter5 = settings.value("ControlSetParameData/set_line_paraeter5").toDouble();
    parameterdata.set_line_paraeter6 = settings.value("ControlSetParameData/set_line_paraeter6").toDouble();
    parameterdata.set_line_paraeter7 = settings.value("ControlSetParameData/set_line_paraeter7").toDouble();
    parameterdata.set_line_paraeter8 = settings.value("ControlSetParameData/set_line_paraeter8").toDouble();
    parameterdata.set_line_paraeter9 = settings.value("ControlSetParameData/set_line_paraeter9").toDouble();
    parameterdata.set_line_paraeter10 = settings.value("ControlSetParameData/set_line_paraeter10").toDouble();
    parameterdata.set_line_paraeter11 = settings.value("ControlSetParameData/set_line_paraeter11").toDouble();
    parameterdata.set_line_paraeter12 = settings.value("ControlSetParameData/set_line_paraeter12").toDouble();
    parameterdata.set_line_paraeter13 = settings.value("ControlSetParameData/set_line_paraeter13").toDouble();
    parameterdata.set_line_paraeter14 = settings.value("ControlSetParameData/set_line_paraeter14").toDouble();
    parameterdata.dingbiao_return = settings.value("ControlSetParameData/dingbiao_return").toInt();

    // 定标参数查询
    parameterdata.query_accept_gain1 = settings.value("ControlSetParameData/query_accept_gain1").toDouble();
    parameterdata.query_accept_gain2 = settings.value("ControlSetParameData/query_accept_gain2").toDouble();
    parameterdata.query_accept_gain3 = settings.value("ControlSetParameData/query_accept_gain3").toDouble();
    parameterdata.query_accept_gain4 = settings.value("ControlSetParameData/query_accept_gain4").toDouble();
    parameterdata.query_accept_gain5 = settings.value("ControlSetParameData/query_accept_gain5").toDouble();
    parameterdata.query_accept_gain6 = settings.value("ControlSetParameData/query_accept_gain6").toDouble();
    parameterdata.query_accept_gain7 = settings.value("ControlSetParameData/query_accept_gain7").toDouble();
    parameterdata.query_accept_gain8 = settings.value("ControlSetParameData/query_accept_gain8").toDouble();
    parameterdata.query_accept_gain9 = settings.value("ControlSetParameData/query_accept_gain9").toDouble();
    parameterdata.query_accept_gain10 = settings.value("ControlSetParameData/query_accept_gain10").toDouble();
    parameterdata.query_accept_gain11 = settings.value("ControlSetParameData/query_accept_gain11").toDouble();
    parameterdata.query_accept_gain12 = settings.value("ControlSetParameData/query_accept_gain12").toDouble();
    parameterdata.query_accept_gain13 = settings.value("ControlSetParameData/query_accept_gain13").toDouble();
    parameterdata.query_accept_gain14 = settings.value("ControlSetParameData/query_accept_gain14").toDouble();

    parameterdata.query_diode1 = settings.value("ControlSetParameData/query_diode1").toDouble();
    parameterdata.query_diode2 = settings.value("ControlSetParameData/query_diode2").toDouble();
    parameterdata.query_diode3 = settings.value("ControlSetParameData/query_diode3").toDouble();
    parameterdata.query_diode4 = settings.value("ControlSetParameData/query_diode4").toDouble();
    parameterdata.query_diode5 = settings.value("ControlSetParameData/query_diode5").toDouble();
    parameterdata.query_diode6 = settings.value("ControlSetParameData/query_diode6").toDouble();
    parameterdata.query_diode7 = settings.value("ControlSetParameData/query_diode7").toDouble();
    parameterdata.query_diode8 = settings.value("ControlSetParameData/query_diode8").toDouble();
    parameterdata.query_diode9 = settings.value("ControlSetParameData/query_diode9").toDouble();
    parameterdata.query_diode10 = settings.value("ControlSetParameData/query_diode10").toDouble();
    parameterdata.query_diode11 = settings.value("ControlSetParameData/query_diode11").toDouble();
    parameterdata.query_diode12 = settings.value("ControlSetParameData/query_diode12").toDouble();
    parameterdata.query_diode13 = settings.value("ControlSetParameData/query_diode13").toDouble();
    parameterdata.query_diode14 = settings.value("ControlSetParameData/query_diode14").toDouble();

    parameterdata.query_noise_temp1 = settings.value("ControlSetParameData/query_noise_temp1").toDouble();
    parameterdata.query_noise_temp2 = settings.value("ControlSetParameData/query_noise_temp2").toDouble();
    parameterdata.query_noise_temp3 = settings.value("ControlSetParameData/query_noise_temp3").toDouble();
    parameterdata.query_noise_temp4 = settings.value("ControlSetParameData/query_noise_temp4").toDouble();
    parameterdata.query_noise_temp5 = settings.value("ControlSetParameData/query_noise_temp5").toDouble();
    parameterdata.query_noise_temp6 = settings.value("ControlSetParameData/query_noise_temp6").toDouble();
    parameterdata.query_noise_temp7 = settings.value("ControlSetParameData/query_noise_temp7").toDouble();
    parameterdata.query_noise_temp8 = settings.value("ControlSetParameData/query_noise_temp8").toDouble();
    parameterdata.query_noise_temp9 = settings.value("ControlSetParameData/query_noise_temp9").toDouble();
    parameterdata.query_noise_temp10 = settings.value("ControlSetParameData/query_noise_temp10").toDouble();
    parameterdata.query_noise_temp11 = settings.value("ControlSetParameData/query_noise_temp11").toDouble();
    parameterdata.query_noise_temp12 = settings.value("ControlSetParameData/query_noise_temp12").toDouble();
    parameterdata.query_noise_temp13 = settings.value("ControlSetParameData/query_noise_temp13").toDouble();
    parameterdata.query_noise_temp14 = settings.value("ControlSetParameData/query_noise_temp14").toDouble();

    parameterdata.query_line_paraeter1 = settings.value("ControlSetParameData/query_line_paraeter1").toDouble();
    parameterdata.query_line_paraeter2 = settings.value("ControlSetParameData/query_line_paraeter2").toDouble();
    parameterdata.query_line_paraeter3 = settings.value("ControlSetParameData/query_line_paraeter3").toDouble();
    parameterdata.query_line_paraeter4 = settings.value("ControlSetParameData/query_line_paraeter4").toDouble();
    parameterdata.query_line_paraeter5 = settings.value("ControlSetParameData/query_line_paraeter5").toDouble();
    parameterdata.query_line_paraeter6 = settings.value("ControlSetParameData/query_line_paraeter6").toDouble();
    parameterdata.query_line_paraeter7 = settings.value("ControlSetParameData/query_line_paraeter7").toDouble();
    parameterdata.query_line_paraeter8 = settings.value("ControlSetParameData/query_line_paraeter8").toDouble();
    parameterdata.query_line_paraeter9 = settings.value("ControlSetParameData/query_line_paraeter9").toDouble();
    parameterdata.query_line_paraeter10 = settings.value("ControlSetParameData/query_line_paraeter10").toDouble();
    parameterdata.query_line_paraeter11 = settings.value("ControlSetParameData/query_line_paraeter11").toDouble();
    parameterdata.query_line_paraeter12 = settings.value("ControlSetParameData/query_line_paraeter12").toDouble();
    parameterdata.query_line_paraeter13 = settings.value("ControlSetParameData/query_line_paraeter13").toDouble();
    parameterdata.query_line_paraeter14 = settings.value("ControlSetParameData/query_line_paraeter14").toDouble();

    // 系统标定
    parameterdata.dingbiao_mode = settings.value("ControlSetParameData/dingbiao_mode").toInt();
    parameterdata.yedan_angle = settings.value("ControlSetParameData/yedan_angle").toDouble();
    parameterdata.normal_temp_angle = settings.value("ControlSetParameData/normal_temp_angle").toDouble();
    parameterdata.noise_angle = settings.value("ControlSetParameData/noise_angle").toDouble();
    parameterdata.yedan_temp = settings.value("ControlSetParameData/yedan_temp").toDouble();
    parameterdata.look_time = settings.value("ControlSetParameData/look_time").toDouble();
    parameterdata.currence_tianxian_angle = settings.value("ControlSetParameData/currence_tianxian_angle").toInt();
    parameterdata.system_dingbiao_angle = settings.value("ControlSetParameData/system_dingbiao_angle").toInt();
}

void CControlSetParameterData::creat_configfile()
{
    QDir currentDir = QDir::current();
    // 创建目录（相对路径）
    QString dirpath = "Dataconfig";
    if (!currentDir.exists(dirpath))
    {
        if (!currentDir.mkdir(dirpath))
        {
            qDebug() << "创建目录失败";
            return;
        }
        qDebug() << "创建目录成功";
    }
    // 构建文件路径（确保目录存在后在操作）
    QFileInfo fileInfo(currentDir, "Dataconfig/CControlSetParameterData.ini");
    // QString filePath = currentDir.filePath("Dataconfig/CControlSetParameterData.ini");
    QString filePath = fileInfo.absoluteFilePath();
    // 检查文件失败
    if (fileInfo.exists())
    {
        qDebug() << "文件已存在，无需再创建";
        return;
    }
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly))
    {
        file.close();
        qDebug() << "创建成功";
    }
    else
    {
        qDebug() << "创建失败";
    }
}

void CControlSetParameterData::saveToFile()
{
    QString filePath = "F:/QtMicrowaveRadiation/ControlSetParameData.ini";
    QSettings settings("Dataconfig/CControlSetParameterData.ini", QSettings::IniFormat);
    settings.setValue("ControlSetParameData/open_close_down", parameterdata.open_close_down);
    settings.setValue("ControlSetParameData/close_down_process", parameterdata.close_down_process);
    settings.setValue("ControlSetParameData/currence_woke_mode", parameterdata.currence_woke_mode);
    settings.setValue("ControlSetParameData/return_woke_mode", parameterdata.return_woke_mode);
    settings.setValue("ControlSetParameData/sampling_time", parameterdata.sampling_time);
    settings.setValue("ControlSetParameData/sampling_rate", parameterdata.sampling_rate);
    settings.setValue("ControlSetParameData/black_num", parameterdata.black_num);
    settings.setValue("ControlSetParameData/maintain_time", parameterdata.maintain_time);
    settings.setValue("ControlSetParameData/return_mark", parameterdata.return_mark);
    settings.setValue("ControlSetParameData/return_sampling_time", parameterdata.return_sampling_time);
    settings.setValue("ControlSetParameData/return_sampling_rate", parameterdata.return_sampling_rate);
    settings.setValue("ControlSetParameData/return_black_num", parameterdata.return_black_num);
    settings.setValue("ControlSetParameData/return_maintain_time", parameterdata.return_maintain_time);
    settings.setValue("ControlSetParameData/sum_time", parameterdata.sum_time);
    settings.setValue("ControlSetParameData/open_time", parameterdata.open_time);

    // 设置定标参数
    settings.setValue("ControlSetParameData/set_accept_gain1", parameterdata.set_accept_gain1);
    settings.setValue("ControlSetParameData/set_accept_gain2", parameterdata.set_accept_gain2);
    settings.setValue("ControlSetParameData/set_accept_gain3", parameterdata.set_accept_gain3);
    settings.setValue("ControlSetParameData/set_accept_gain4", parameterdata.set_accept_gain4);
    settings.setValue("ControlSetParameData/set_accept_gain5", parameterdata.set_accept_gain5);
    settings.setValue("ControlSetParameData/set_accept_gain6", parameterdata.set_accept_gain6);
    settings.setValue("ControlSetParameData/set_accept_gain7", parameterdata.set_accept_gain7);
    settings.setValue("ControlSetParameData/set_accept_gain8", parameterdata.set_accept_gain8);
    settings.setValue("ControlSetParameData/set_accept_gain9", parameterdata.set_accept_gain9);
    settings.setValue("ControlSetParameData/set_accept_gain10", parameterdata.set_accept_gain10);
    settings.setValue("ControlSetParameData/set_accept_gain11", parameterdata.set_accept_gain11);
    settings.setValue("ControlSetParameData/set_accept_gain12", parameterdata.set_accept_gain12);
    settings.setValue("ControlSetParameData/set_accept_gain13", parameterdata.set_accept_gain13);
    settings.setValue("ControlSetParameData/set_accept_gain14", parameterdata.set_accept_gain14);

    settings.setValue("ControlSetParameData/set_diode1", parameterdata.set_diode1);
    settings.setValue("ControlSetParameData/set_diode2", parameterdata.set_diode2);
    settings.setValue("ControlSetParameData/set_diode3", parameterdata.set_diode3);
    settings.setValue("ControlSetParameData/set_diode4", parameterdata.set_diode4);
    settings.setValue("ControlSetParameData/set_diode5", parameterdata.set_diode5);
    settings.setValue("ControlSetParameData/set_diode6", parameterdata.set_diode6);
    settings.setValue("ControlSetParameData/set_diode7", parameterdata.set_diode7);
    settings.setValue("ControlSetParameData/set_diode8", parameterdata.set_diode8);
    settings.setValue("ControlSetParameData/set_diode9", parameterdata.set_diode9);
    settings.setValue("ControlSetParameData/set_diode10", parameterdata.set_diode10);
    settings.setValue("ControlSetParameData/set_diode11", parameterdata.set_diode11);
    settings.setValue("ControlSetParameData/set_diode12", parameterdata.set_diode12);
    settings.setValue("ControlSetParameData/set_diode13", parameterdata.set_diode13);
    settings.setValue("ControlSetParameData/set_diode14", parameterdata.set_diode14);

    settings.setValue("ControlSetParameData/set_noise_temp1", parameterdata.set_noise_temp1);
    settings.setValue("ControlSetParameData/set_noise_temp2", parameterdata.set_noise_temp2);
    settings.setValue("ControlSetParameData/set_noise_temp3", parameterdata.set_noise_temp3);
    settings.setValue("ControlSetParameData/set_noise_temp4", parameterdata.set_noise_temp4);
    settings.setValue("ControlSetParameData/set_noise_temp5", parameterdata.set_noise_temp5);
    settings.setValue("ControlSetParameData/set_noise_temp6", parameterdata.set_noise_temp6);
    settings.setValue("ControlSetParameData/set_noise_temp7", parameterdata.set_noise_temp7);
    settings.setValue("ControlSetParameData/set_noise_temp8", parameterdata.set_noise_temp8);
    settings.setValue("ControlSetParameData/set_noise_temp9", parameterdata.set_noise_temp9);
    settings.setValue("ControlSetParameData/set_noise_temp10", parameterdata.set_noise_temp10);
    settings.setValue("ControlSetParameData/set_noise_temp11", parameterdata.set_noise_temp11);
    settings.setValue("ControlSetParameData/set_noise_temp12", parameterdata.set_noise_temp12);
    settings.setValue("ControlSetParameData/set_noise_temp13", parameterdata.set_noise_temp13);
    settings.setValue("ControlSetParameData/set_noise_temp14", parameterdata.set_noise_temp14);

    settings.setValue("ControlSetParameData/set_line_paraeter1", parameterdata.set_line_paraeter1);
    settings.setValue("ControlSetParameData/set_line_paraeter2", parameterdata.set_line_paraeter2);
    settings.setValue("ControlSetParameData/set_line_paraeter3", parameterdata.set_line_paraeter3);
    settings.setValue("ControlSetParameData/set_line_paraeter4", parameterdata.set_line_paraeter4);
    settings.setValue("ControlSetParameData/set_line_paraeter5", parameterdata.set_line_paraeter5);
    settings.setValue("ControlSetParameData/set_line_paraeter6", parameterdata.set_line_paraeter6);
    settings.setValue("ControlSetParameData/set_line_paraeter7", parameterdata.set_line_paraeter7);
    settings.setValue("ControlSetParameData/set_line_paraeter8", parameterdata.set_line_paraeter8);
    settings.setValue("ControlSetParameData/set_line_paraeter9", parameterdata.set_line_paraeter9);
    settings.setValue("ControlSetParameData/set_line_paraeter10", parameterdata.set_line_paraeter10);
    settings.setValue("ControlSetParameData/set_line_paraeter11", parameterdata.set_line_paraeter11);
    settings.setValue("ControlSetParameData/set_line_paraeter12", parameterdata.set_line_paraeter12);
    settings.setValue("ControlSetParameData/set_line_paraeter13", parameterdata.set_line_paraeter13);
    settings.setValue("ControlSetParameData/set_line_paraeter14", parameterdata.set_line_paraeter14);
    settings.setValue("ControlSetParameData/dingbiao_return", parameterdata.dingbiao_return);

    // 定标参数查询
    settings.setValue("ControlSetParameData/query_accept_gain1", parameterdata.query_accept_gain1);
    settings.setValue("ControlSetParameData/query_accept_gain2", parameterdata.query_accept_gain2);
    settings.setValue("ControlSetParameData/query_accept_gain3", parameterdata.query_accept_gain3);
    settings.setValue("ControlSetParameData/query_accept_gain4", parameterdata.query_accept_gain4);
    settings.setValue("ControlSetParameData/query_accept_gain5", parameterdata.query_accept_gain5);
    settings.setValue("ControlSetParameData/query_accept_gain6", parameterdata.query_accept_gain6);
    settings.setValue("ControlSetParameData/query_accept_gain7", parameterdata.query_accept_gain7);
    settings.setValue("ControlSetParameData/query_accept_gain8", parameterdata.query_accept_gain8);
    settings.setValue("ControlSetParameData/query_accept_gain9", parameterdata.query_accept_gain9);
    settings.setValue("ControlSetParameData/query_accept_gain10", parameterdata.query_accept_gain10);
    settings.setValue("ControlSetParameData/query_accept_gain11", parameterdata.query_accept_gain11);
    settings.setValue("ControlSetParameData/query_accept_gain12", parameterdata.query_accept_gain12);
    settings.setValue("ControlSetParameData/query_accept_gain13", parameterdata.query_accept_gain13);
    settings.setValue("ControlSetParameData/query_accept_gain14", parameterdata.query_accept_gain14);

    settings.setValue("ControlSetParameData/query_diode1", parameterdata.query_diode1);
    settings.setValue("ControlSetParameData/query_diode2", parameterdata.query_diode2);
    settings.setValue("ControlSetParameData/query_diode3", parameterdata.query_diode3);
    settings.setValue("ControlSetParameData/query_diode4", parameterdata.query_diode4);
    settings.setValue("ControlSetParameData/query_diode5", parameterdata.query_diode5);
    settings.setValue("ControlSetParameData/query_diode6", parameterdata.query_diode6);
    settings.setValue("ControlSetParameData/query_diode7", parameterdata.query_diode7);
    settings.setValue("ControlSetParameData/query_diode8", parameterdata.query_diode8);
    settings.setValue("ControlSetParameData/query_diode9", parameterdata.query_diode9);
    settings.setValue("ControlSetParameData/query_diode10", parameterdata.query_diode10);
    settings.setValue("ControlSetParameData/query_diode11", parameterdata.query_diode11);
    settings.setValue("ControlSetParameData/query_diode12", parameterdata.query_diode12);
    settings.setValue("ControlSetParameData/query_diode13", parameterdata.query_diode13);
    settings.setValue("ControlSetParameData/query_diode14", parameterdata.query_diode14);

    settings.setValue("ControlSetParameData/query_noise_temp1", parameterdata.query_noise_temp1);
    settings.setValue("ControlSetParameData/query_noise_temp2", parameterdata.query_noise_temp2);
    settings.setValue("ControlSetParameData/query_noise_temp3", parameterdata.query_noise_temp3);
    settings.setValue("ControlSetParameData/query_noise_temp4", parameterdata.query_noise_temp4);
    settings.setValue("ControlSetParameData/query_noise_temp5", parameterdata.query_noise_temp5);
    settings.setValue("ControlSetParameData/query_noise_temp6", parameterdata.query_noise_temp6);
    settings.setValue("ControlSetParameData/query_noise_temp7", parameterdata.query_noise_temp7);
    settings.setValue("ControlSetParameData/query_noise_temp8", parameterdata.query_noise_temp8);
    settings.setValue("ControlSetParameData/query_noise_temp9", parameterdata.query_noise_temp9);
    settings.setValue("ControlSetParameData/query_noise_temp10", parameterdata.query_noise_temp10);
    settings.setValue("ControlSetParameData/query_noise_temp11", parameterdata.query_noise_temp11);
    settings.setValue("ControlSetParameData/query_noise_temp12", parameterdata.query_noise_temp12);
    settings.setValue("ControlSetParameData/query_noise_temp13", parameterdata.query_noise_temp13);
    settings.setValue("ControlSetParameData/query_noise_temp14", parameterdata.query_noise_temp14);

    settings.setValue("ControlSetParameData/query_line_paraeter1", parameterdata.query_line_paraeter1);
    settings.setValue("ControlSetParameData/query_line_paraeter2", parameterdata.query_line_paraeter2);
    settings.setValue("ControlSetParameData/query_line_paraeter3", parameterdata.query_line_paraeter3);
    settings.setValue("ControlSetParameData/query_line_paraeter4", parameterdata.query_line_paraeter4);
    settings.setValue("ControlSetParameData/query_line_paraeter5", parameterdata.query_line_paraeter5);
    settings.setValue("ControlSetParameData/query_line_paraeter6", parameterdata.query_line_paraeter6);
    settings.setValue("ControlSetParameData/query_line_paraeter7", parameterdata.query_line_paraeter7);
    settings.setValue("ControlSetParameData/query_line_paraeter8", parameterdata.query_line_paraeter8);
    settings.setValue("ControlSetParameData/query_line_paraeter9", parameterdata.query_line_paraeter9);
    settings.setValue("ControlSetParameData/query_line_paraeter10", parameterdata.query_line_paraeter10);
    settings.setValue("ControlSetParameData/query_line_paraeter11", parameterdata.query_line_paraeter11);
    settings.setValue("ControlSetParameData/query_line_paraeter12", parameterdata.query_line_paraeter12);
    settings.setValue("ControlSetParameData/query_line_paraeter13", parameterdata.query_line_paraeter13);
    settings.setValue("ControlSetParameData/query_line_paraeter14", parameterdata.query_line_paraeter14);

    // 系统标定
    settings.setValue("ControlSetParameData/dingbiao_mode", parameterdata.dingbiao_mode);
    settings.setValue("ControlSetParameData/yedan_angle", parameterdata.yedan_angle);
    settings.setValue("ControlSetParameData/normal_temp_angle", parameterdata.normal_temp_angle);
    settings.setValue("ControlSetParameData/noise_angle", parameterdata.noise_angle);
    settings.setValue("ControlSetParameData/yedan_temp", parameterdata.yedan_temp);
    settings.setValue("ControlSetParameData/look_time", parameterdata.look_time);
    settings.setValue("ControlSetParameData/currence_tianxian_angle", parameterdata.currence_tianxian_angle);
    settings.setValue("ControlSetParameData/system_dingbiao_angle", parameterdata.system_dingbiao_angle);


}
