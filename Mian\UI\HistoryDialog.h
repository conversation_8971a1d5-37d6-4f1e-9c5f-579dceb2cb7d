﻿#ifndef HISTORYDIALOG_H
#define HISTORYDIALOG_H

#include <QDialog>
#include <QDir>
#include <QDesktopServices>
#include <QUrl>
#include <QMessageBox>
#include <QFileSystemModel>
#include <QIdentityProxyModel>
#include "tools/Global.h"
#include <QTreeWidget>
namespace Ui
{
    class HistoryDialog;
}

class HistoryDialog : public QDialog
{
    Q_OBJECT

public:
    explicit HistoryDialog(QWidget *parent = nullptr);
    ~HistoryDialog();

    QTreeWidget *get_QTreeWidget();
    QTreeWidget *get_QTreeWidget_lv1();
    void LoadStyleFile(QString strStyle);

private slots:

public slots:
    void set_HistoryDialog_styleSheet(int);

signals:
    void set_hot_image_widget_one();
    void set_hot_image_widget_two();
    void set_hot_image_widget_three();
    void set_hot_image_widget_four();

private:
    Ui::HistoryDialog *ui;
    QFileSystemModel *dirModel; // 文件系统模型
    QString currentFolderPath;  // 当前文件夹路径
    QIdentityProxyModel *proxyModel;
};

#endif // HISTORYDIALOG_H
