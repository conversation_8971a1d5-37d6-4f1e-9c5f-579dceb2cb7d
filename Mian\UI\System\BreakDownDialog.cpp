﻿#include "BreakDownDialog.h"
#include "ui_BreakDownDialog.h"
#include "qcustomplot.h"
#include <QChartView>
#include "tools/Global.h"
BreakDownDialog::BreakDownDialog(QWidget* parent) : QDialog(parent),
ui(new Ui::BreakDownDialog)
{
    ui->setupUi(this);
    this->setWindowTitle("故障管理");
    this->setWindowFlags(this->windowFlags() | Qt::WindowMaximizeButtonHint);
    this->setWindowFlags(Qt::Window);

    ui->dateTimeEdit_start_time->setCalendarPopup(true);
    ui->dateTimeEdit_start_time->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_start_time->setDateTime(QDateTime::currentDateTime());

    ui->dateTimeEdit_stop_time->setCalendarPopup(true);
    ui->dateTimeEdit_stop_time->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_stop_time->setDateTime(QDateTime::currentDateTime());

    ui->dateTimeEdit_Fault_time->setCalendarPopup(true);
    ui->dateTimeEdit_Fault_time->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_Fault_time->setDateTime(QDateTime::currentDateTime());

    ui->dateTimeEdit_delect->setCalendarPopup(true);
    ui->dateTimeEdit_delect->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_delect->setDateTime(QDateTime::currentDateTime());



    CreatpieSetView();
    Init_widget_graph_view();
    FaultRecord_data = new sqlite_FaultRecord_data("SqliteData/"); // 创建故障记录表
    FaultRecord_data->use_CustomSqlTableModel(ui->tableView_Fault);

    auto_delect_data = new QTimer();
    connect(auto_delect_data, SIGNAL(timeout()), this, SLOT(on_pushButton_delect_clicked()));

    auto_delect_data->start(1000);
}

BreakDownDialog::~BreakDownDialog()
{
    delete ui;
    delete FaultRecord_data;
}

void BreakDownDialog::CreatpieSetView()
{
    QPieSeries* pieview = new QPieSeries();
    // 设置中间圆与大圆比例
    pieview->setHoleSize(0.35);
    // 扇形及扇形数据
    QPieSlice* pie1 = new QPieSlice();
    pie1->setValue(25);
    pie1->setLabel("文件上传监控");
    pie1->setLabelVisible(); // 显示饼状区对应的lable
    pie1->setColor(QColor("#44cb9cf"));
    pie1->setLabelColor(QColor("#44cb9cf"));
    pieview->append(pie1);

    QPieSlice* pie2 = new QPieSlice();
    pie2->setValue(25);
    pie2->setLabel("数据传输监控");
    pie2->setLabelVisible();
    pie2->setColor(QColor("#53b666"));
    pie2->setLabelColor(QColor("#53b666"));
    pieview->append(pie2);

    QPieSlice* pie3 = new QPieSlice();
    pie3->setValue(25);
    pie3->setLabel("传感器状态监控");
    pie3->setLabelVisible();
    pie3->setColor(QColor("#53b777"));
    pie3->setLabelColor(QColor("#53b777"));
    pieview->append(pie3);

    QPieSlice* pie4 = new QPieSlice();
    pie4->setValue(25);
    pie4->setLabel("KV通道状态监控");
    pie4->setLabelVisible();
    pie4->setColor(QColor("#53b888"));
    pie4->setLabelColor(QColor("#53b888"));
    pieview->append(pie4);

    QChart* chart = new QChart();
    chart->setTitle("故障告警");
    chart->setTitleBrush(QColor("#808396"));
    chart->setAnimationOptions(QChart::SeriesAnimations); // 设置图表的动画选项
    chart->legend()->setAlignment(Qt::AlignBottom);       // 设置图例标签属性
    chart->legend()->setBackgroundVisible(false);
    chart->legend()->setFont(QFont("黑体", 8));
    chart->legend()->setLabelColor(QColor("#808396"));
    chart->addSeries(pieview);

    chart->setContentsMargins(0, 0, 0, 0);
    chart->setMargins(QMargins(5, 5, 5, 5));

    QChartView* chartview = new QChartView(ui->widget_bingtu);
    chartview->setRenderHint(QPainter::Antialiasing);          // 设置抗锯齿渲染
#pragma warning(suppress : 4996)
    chartview->setRenderHint(QPainter::NonCosmeticDefaultPen); // 设置非装饰性默认笔
    chartview->setChart(chart);

    QVBoxLayout* layout = new QVBoxLayout(ui->widget_bingtu);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->addWidget(chartview, 1);
    chartview->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    connect(ui->widget_bingtu, &QWidget::customContextMenuRequested, [=]() {
        int minDimension = qMin(chartview->width(), chartview->height());
        qreal relativeSize = qBound(0.5, minDimension / 500.0, 0.9);
        pieview->setPieSize(relativeSize);
        });

    connect(pie1, &QPieSlice::clicked, this, &BreakDownDialog::OnSliceClicked);
    connect(pie2, &QPieSlice::clicked, this, &BreakDownDialog::OnSliceClicked);
    connect(pie3, &QPieSlice::clicked, this, &BreakDownDialog::OnSliceClicked);
    connect(pie4, &QPieSlice::clicked, this, &BreakDownDialog::OnSliceClicked);
}
void BreakDownDialog::OnSliceClicked()
{
    QPieSlice* slice = qobject_cast<QPieSlice*>(sender()); // 切换切片的展开状态
    if (slice)
    {
        slice->setExploded(!slice->isExploded());
    }
}
void BreakDownDialog::Init_widget_graph_view()
{
    // 添加画布
    ui->widget_tubiao->addGraph();
    QSharedPointer<QCPAxisTickerText> textTicker(new QCPAxisTickerText);
    textTicker->addTick(1, "文件上传监控");
    textTicker->addTick(2, "数据传输监控");
    textTicker->addTick(3, "传感器状态监控");
    textTicker->addTick(4, "KV通道状态监控");
    ui->widget_tubiao->xAxis->setTicker(textTicker);
    QFont font = ui->widget_tubiao->xAxis->tickLabelFont();
    font.setPointSize(7);
    ui->widget_tubiao->xAxis->setTickLabelFont(font);

    QSharedPointer<QCPAxisTickerFixed> fixedTicker(new QCPAxisTickerFixed);
    fixedTicker->setTickStep(10);
    fixedTicker->setScaleStrategy(QCPAxisTickerFixed::ssNone); // 设置不自动调整
    ui->widget_tubiao->yAxis->setTicker(fixedTicker);

    ui->widget_tubiao->xAxis->setLabel("x轴");
    ui->widget_tubiao->yAxis->setLabel("y轴（次数累计/次）");
    ui->widget_tubiao->yAxis->setRange(0, 100);

    ui->widget_tubiao->replot();
}

void BreakDownDialog::on_pushButton_query_clicked()
{
    QDateTime DateStr1 = ui->dateTimeEdit_start_time->dateTime();
    QString timeStr1 = DateStr1.toString("yyyy-MM-dd HH:mm:ss");
    QDateTime DateStr2 = ui->dateTimeEdit_stop_time->dateTime();
    QString timeStr2 = DateStr2.toString("yyyy-MM-dd HH:mm:ss");
    FaultRecord_data->select_usetime_SqlTableModel(timeStr1, timeStr2);
}

void BreakDownDialog::on_pushButton_delect_clicked()
{

    //删除所有早于当前时间之前的子目录
    QDateTime month_file = QDateTime::currentDateTime().addDays(-(systemconfigfdata.systemdata.fault_management_day));
    //QDateTime DateStr1 = ui->dateTimeEdit_Fault_time->dateTime();
    QString timeStr1 = month_file.toString("yyyy-MM-dd HH:mm:ss");
    FaultRecord_data->deleteOldsql(timeStr1);
}

void BreakDownDialog::on_pushButton_Fault_add_clicked()
{

    QDateTime DateStr1 = ui->dateTimeEdit_Fault_time->dateTime();
    QString timeStr1 = DateStr1.toString("yyyy-MM-dd HH:mm:ss");
    struct FaultRecord_SQ_GROUP data = { ui->lineEdit_Fault_number->text(), ui->lineEdit_Fault_type->text(), ui->lineEdit_Fault_content->text(), ui->lineEdit_Fault_grade->text(), timeStr1, "beijing" };
    FaultRecord_data->insertCountLimit(data);
}


