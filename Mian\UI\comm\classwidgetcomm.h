﻿#ifndef CLASSWIDGETCOMM_H
#define CLASSWIDGETCOMM_H

#include <QWidget>
#include "tools/Comm_Protocol/CommInterface.h"
#include <QTimer>
#include "tools/XC_DataHanding/xcProtocolDJFS.h"
#include "tools/XC_DataHanding/xcProtocol_DataExchange.h"

namespace Ui
{
    class classWidgetComm;
}


//采集卡6路温度
typedef struct 
{
    float Temp[6];
}six_Temp_struct;

// 采集卡16路电压
typedef struct
{
    float Votage[16];
}AgvVoltage_struct;

// 采集卡温控参数
typedef struct
{
    float P;//参数P
    float I;//参数I
    float D;//参数D
    float targetTemp;//目标温度
    float currTemp;//当前温度
    float heatPower;//加热功率
}cardTempC_struct;

//常温源温度
typedef struct
{
    float LowTemp1;
    float LowTemp2;
    float LowTemp3;
    float LowTemp4;
}LowTemp;

// GPS 信息
typedef struct
{
    // 经纬纬度  东经 北纬 +；  西经 南纬 -；
    float longitude;// 经度
    float latitude; // 纬度
    unsigned int UTC_Time;// UTC时间 ；格式：0xHHMMSSss
    unsigned int UTC_Date;// UTC日期； 格式：0xYYYYMMDD
    unsigned int CenturySecond;// 世纪秒
    float HDOP;
    float HeightSeaLevel; // 海平面高度
    float HeightEarthLevel;// 地平面高度
}GPSParam_struct;

//天线角度参数
typedef struct
{
    float AntTgtAng;//目标角度
    float antCurrAng;// 当前角度
    uint32 m_StepPerRound;// 天线电机步数每圈
    uint32 m_StepSpeedSet;// 天线电机速度参数
    float ANT_AngleOffset;// 天线角度补偿
}AntAngParam_struct;

// 气象6要素
typedef struct 
{
    float Humidity;    //湿度	
    float Temp;        //温度
    float AirPress;    //气压	
    float WindSpeed;   //风速	
    float WindDir;     //风向	
    float Rain10Minute;//10分钟降水量
}WeathersixEles_struct;

typedef struct {
    uint32 Speed;//风机转速
    float PwmValue;// 风机功率
}windTurbParam_struct;

typedef struct
{
    uchar colCardStat;//采集卡状态
    uchar antMotStat;//角度电机状态
    uchar metSixElemsStat;//气象六要素状态
    uchar WindTurbStat;//风机状态
}workStatus_struct,PowerStatus_struct;



class classWidgetComm : public QWidget
{
    Q_OBJECT

public:
    CommInterface *CommInterface_t;
    QTimer *m_Tmr;
    bool Connection_status = false;
    bool facility_connect_status = false;

public:
    explicit classWidgetComm(QWidget *parent = nullptr);
    ~classWidgetComm();

    /***  读取标志位设置  ***/
    // 采集卡滤波系数读取标志位
    void set_SamCardFiltCoef_read_fun(bool data);
    // 设置六路接收机温度读取标志位
    void set_SixTemp_read_fun(bool data);
    // 设置LV1电压数据读取标志位
    void set_AgvVoltage_read(bool data);
    // 采集卡温控读取标志位
    void set_AdcCardTempCtrl_read_fun(bool data);
    // 噪声源读取标志位
    void set_NoiseSrcSw_read_fun(bool data);
    // GPS 读取标志位
    void set_GPSParam_read_fun(bool data);
    // 天线角度电机参数读取标志位
    void set_AntAngParam_read_fun(bool data);
    // 常温源平均温度读取标志位
    void set_fTempLowAverage_read_fun(bool data);
    // 气象六要素读取标志位
    void set_sixWeather_read_fun(bool data);
    // 风机参数读取标志位
    void set_windTurbParam_read_fun(bool data);
    // 工作状态读取标志位
    void set_workStatus_read_fun(bool data);
    // 电源状态读取标志位
    void set_PowerStatus_read_fun(bool data);
    // 当前工作模式读取标志位
    void set_currWordMode_read_fun(bool data);
    // 常温源亮温读取标志位
    void set_fistTempBt_read_fun(bool data);


    /***************************************/


    /***          写入参数设置          ***/
    // 设置采集卡滤波器系数
    void set_SamCardFiltCoef_fun(float value);
    // 设置采集卡温控1参数  P  I  D  目标温度
    void set_AdcCardTempCtrl1_fun(float P, float I, float D, float TargetTemp);
    // 设置采集卡温控2参数  P  I  D  目标温度
    void set_AdcCardTempCtrl2_fun(float P, float I, float D, float TargetTemp);
    // 设置噪声源开关
    void set_NoiseSrcSw_fun(bool value);
    // 设置天线角度  value<360 value>=0;
    void set_AntTgtAng_fun(float value);
    // 设置风机开关  value<=100 value>=0;
    void set_FanPower_dun(float value);
    // 设置工作模式 0手动模式 1 自动模式  2定标模式，在目前程序中默认使用手动模式，不会更改
    void set_WorkMode_fun(uchar value=0);
    // 设置设备重启
    void set_DevRestart_fun(); 
    // 设置清空
    void  set_sendClear_fun();
    // 设置发送
    void set_sendsetting_fun();
    /***************************************/

    /***           读取参数值            ***/
     // 读取采集卡滤波器系数
    float get_SamCardFiltCoef_fun();
    // 读取接收机6路温度
    six_Temp_struct get_SixTemp_fun();
    // 读取接收机16路均值电压
    AgvVoltage_struct get_AgvVoltage_fun();
    // 读取采集卡温控数值 value 0为K通道温控  1为V通道温控
    cardTempC_struct get_cardTempC_fun(uchar value);
    // 读取噪声源开关状态
    bool get_NoiseSrcSw_fun();
    // 读取GPS信息
    GPSParam_struct get_GPSParam_fun();
    // 读取天线角度电机参数
    AntAngParam_struct get_AntAngParam_fun();

    // 读取4个常温源温度
    LowTemp get_fTempLow_fun();
    // 读取常温源平均温度
    float get_fTempLowAverage_fun();
    // 读取气象6要素
    WeathersixEles_struct get_WeathersixEles_fun();
    // 读取风机参数
    windTurbParam_struct get_windTurbParam_fun();
    // 读取外设工作状态
    workStatus_struct get_workStatus_fun();
    // 读取外设电源状态
    PowerStatus_struct get_PowerStatus_fun();
    // 读取当前工作模式 
    uchar get_currWordMode_fun();
    // 读取第一路常温源亮温值
    float get_fistTempBt_fun();
    /***************************************/


signals:
    // 采集卡滤波系数更新信号
    void signal_SamCardFiltCoef_data(QVector<ushort>);

    // 接收机6路温度数据更新信号
    void signal_SixTemp_buff_data(QVector<ushort>);

    // 接收机16路均值电压数据更新信号
    void signal_RadioVolAverage_data(QVector<ushort>);

    // 采集卡温控1参数更新信号
    void signal_AdcCardTempCtrl1_data(QVector<ushort>);
    // 采集卡温控2参数更新信号
    void signal_AdcCardTempCtrl2_data(QVector<ushort>);

    // 噪声源开关状态更新信号
    void signal_NoiseSrcSw_data(QVector<ushort>);

    // GPS信息更新信号
    void signal_GPS_buff_data(QVector<ushort>);

    // 天线角度电机参数更新信号
    void signal_AntAng_data(QVector<ushort>);

    // 4个常温源温度
    void signal_fTemp_Low_data(QVector<ushort>);

    // 常温源平均温度更新信号
    void signal_fTemp_LowAverage_data(QVector<ushort>);

    // 气象6要素更新信号
    void siganl_sixWeather_data(QVector<ushort>);

    // 风机参数更新信号
    void signal_windTurbParam_data(QVector<ushort>);

    // 工作状态更新信号
    void signal_WorkStatus_data(QVector<ushort>);

    // 电源状态更新信号
    void signal_PowerStatus_data(QVector<ushort>);

    // 当前工作模式更新信号
    void signal_workMode_data(QVector<ushort>);

    // 第一路常温源亮温值更新信号
    void signal_fistTempBt_data(QVector<ushort>);


private slots:
    /**
     * @brief 槽函数 建立连接或者断开连接
     */
    void on_comm_pushButton_clicked();

    void slot_errorOccurred_update(const QString& info);

    /**
     * @brief 槽函数 数据更新（接收数据和发射数据，以及错误信息）
     * @param info 要更新的内容
     */
    void slot_Information_update(const QString &info);
    /**
     * @brief 槽函数 定时器 获取设备ID，并确定和设备连接
     */
    void slot_Timeout_getID();
    /**
     * @brief 槽函数 通讯接收槽函数
     * @param qb 要更新的内容
     */
    void slot_Info_Receive(QByteArray qb);

    void slot_comm_recv(QByteArray qb);
    void slot_Timeout_recv();

private:
    Ui::classWidgetComm *ui;
    QByteArray qb_revbuff;

    QVector<ushort> sendbuff;

    /*** 设备参数设置相关  ***/
    //采集卡滤波器系数标志位
    bool SamCardFiltCoef_flag;
    //采集卡温度参数标志位
    bool cardTempC_flag = true;
    //接收机平均电压标志位
    bool AgvVoltage_flag;
    //采集卡温控参数标志位
    bool AdcCardTempCtrl_flag;
    //噪声源开关标志位
    bool NoiseSrcSw_flag;
    //GPS参数标志位
    bool GPSParam_flag = true;
    //天线角度电机参数标志位
    bool AntAngParam_flag;
    //常温源温度标志位
    bool fTempLow_falg = true;
    //常温源平均温度标志位
    bool fTempLowAverage_flag = true;
    //气象6要素标志位
    bool WeathersixEles_flag = true;
    //风机参数标志位
    bool windTurbParam_flag;
    //外设工作状态标志位
    bool workStatus_flag = true;
    //外设电源状态标志位
    bool PowerStatus_flag = true;
    //当前工作模式标志位
    bool currWordMode_flag;
    //第一路常温源亮温标志位
    bool fistTempBt_flag = true;

    /************************/

    uint m_RecvDelay = 0;
    QTimer* m_Tmr1;


    /**********/
    six_Temp_struct six_Temp_value;


    /**
     * @brief 发送数据
     * @param pData 需要发射的数据指针
     * @param len   需要发射的数据长度
     */
    void SendData(uchar* pData, uint len);

    // 读取主板信息
    void Read_MainBoard_Info(QVector<ushort> vect);

    // 写如主板信息
    void Write_MainBoard_Info(QVector<ushort> vect);

    // 获取指定id类
    CxcDataExchange* Get_mainBoard_info(uint16 id);

    
};

#endif // CLASSWIDGETCOMM_H
