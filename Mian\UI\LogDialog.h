﻿#ifndef LOGDIALOG_H
#define LOGDIALOG_H

#include <QDialog>
#include <QTimer>
#include <QDate>
namespace Ui
{
    class LogDialog;
}


#define Log_Sort_Info_Erro  QString("一般错误:")
#define Log_Sort_Error      QString("致命错误:")
#define Log_Sort_Info       QString("一般信息:")
#define Log_Sort_Debug      QString("调试信息:")
#define Log_Sort_Warning    QString("警告信息:")




class LogDialog : public QDialog
{
    Q_OBJECT

public:
    explicit LogDialog(QWidget* parent = nullptr);
    ~LogDialog();
    void open_qmessage(QString title, QString message, QString sure);
    QColor rgbStringToColor(const QString& rgbString);

private slots:
    void on_pushButton_status_add_clicked(); // 添加日志信息

    void on_pushButton_status_delete_clicked(); // 删除日志信息

    void on_pushButton_clear_clicked(); // 清空日志信息

    void on_pushButton_status_save_clicked(); // 保存日志信息文件

public slots:
    void slot_auto_delect_data();
    void set_time_table_data();//日志表格数据添加 定时器
    void set_logRecord_color(QColor);
    void add_Operational_information(QString);//日志表格添加错误信息内容

private:
    Ui::LogDialog* ui;
    QTimer* auto_delect_data;
    QTimer* time_table_data;

    int i = 0;

    QVector<QColor> c_olor;
};

#endif // LOGDIALOG_H
