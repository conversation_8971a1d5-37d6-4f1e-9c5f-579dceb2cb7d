﻿#ifndef CALIBRATE_DATA_STORAGE_H
#define CALIBRATE_DATA_STORAGE_H

#include <tools/data_treating/cpublic_struct.h>
#include <QObject>
#include <QDir>
#include <cmath>
#include <QDateTime>
#include <QTextStream>
#include <QDomDocument>
#include <QFile>
struct Calibrate_data
{
    QString CALTime;    // 含义： 定标日期及时间       格式为yyyy-mm-dd  hh:mm:ss
    QString CALType;    // 含义： 定标方法            ABSOLUTE：绝对定标； GAIN：内置黑体定标； NOISE：噪声注入定标；TIPPING：天空倾斜定标；OTHER：其他
    int Record;         // 含义： 记录序号
    QString DataType;   // 含义： 定标参数类型        定标参数类型，Alpha非线性修正参数Noise Tn	噪声二极管亮温值（单位为K）Gain	接收机增益系数TSysN	系统噪声温度（单位为K）
    double ch_freq[14]; // 含义： 频率1的定标参数      频率n的定标参数
};

class Calibrate_data_storage : public QObject
{
    Q_OBJECT
public:
    explicit Calibrate_data_storage(QString file_path, struct FILENAME file_name, QObject *parent = nullptr);

    // 日文件
    bool Generate_new_Calibrate_data(QVector<struct Calibrate_data> calibrate_data);
    // 分钟文件
    bool Generate_new_Calibrate_Minutes_file(QVector<struct Calibrate_data> calibrate_data);

    void set_device_and_type(QString device, QString type);
    void set_file_path(QString path);         // 设置文件路径
    void set_file_name(struct FILENAME name); // 设置文件名
    void set_Minutes_file_en(bool en);        // 设置分钟文件存储使能
    void set_Day_file_en(bool en);            // 设置日文件存储使能
    void set_beijing_or_utc(bool choose);     // 设置文件路径及文件名中的时间时使用北京时还是UTC时间  true beijing false utc

    struct FILENAME filename; // 文件名称信息结构体
    QString cur_day_filename; // 当前日文件名称
    QString filepath;         // 文件保存路径
    QFile *file;              // 记录日文件

    QString device = "radiometer";
    QString type = "MFile";

    bool txt_or_xml = false; // true txt   false xml
    void set_save_mode(bool mode);

signals:

public slots:
    void save_data(QVector<struct Calibrate_data> calibrate_data);

private:
    bool Insert_new_Calibrate_Basic_parameters();                                  // 在日文件中插入一条基础数据
    bool Insert_new_Calibrate_data(QVector<struct Calibrate_data> calibrate_data); // 在日文件中插入一条新的亮温数据
    QString Generate_file_name(struct FILENAME file_name);                         // 生成文件名
    bool isPureAscii(const QString &str);                                          // 判断字符串是否是ASCII码
    QString Control_decimal_bit(double data, int bit, bool status);                // 控制小数位

    QString deal_int_data(int data);
    QString deal_qstring_data(QString data);
    /*在田间日文件时判断本地路径下是否已经存在日文件，不考虑时分秒，但要验证其合法性*/
    bool comparefilenames(const QString &filename1, const QString &filename2);                                      // 对比文件名
    bool processFileName(const QString &filename, bool &timevalid, QString &processedbasename, QString &extension); // 处理并验证文件名，生成忽略时分秒后的新的文件名。
    // int ch = 14;
    int Record_number = 1; // 记录日文件的行号

    bool Minutes_file_en = false; // 是否保存为分钟文件
    bool Day_file_en = false;     // 是否保存为日文件
    bool use_beijing_utc = true;  // 北京时和utc时的标志位

    double fre[14] = {22.24, 23.04, 23.84, 25.440, 26.240, 27.840, 31.400, 51.260, 52.280, 53.860, 54.940, 56.660, 57.300, 58.800};
};

#endif // CALIBRATE_DATA_STORAGE_H
