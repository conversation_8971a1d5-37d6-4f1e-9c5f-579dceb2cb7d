﻿#include "UploadMonitorDialog.h"
#include "ui_UploadMonitorDialog.h"
#include "tools/Global.h"
UploadMonitorDialog::UploadMonitorDialog(QWidget* parent) : QDialog(parent),
ui(new Ui::UploadMonitorDialog)
{
    ui->setupUi(this);
    this->setWindowTitle("上传监控");
    this->setWindowFlags(this->windowFlags() | Qt::WindowMaximizeButtonHint);
    this->setWindowFlags(Qt::Window);
    ui->dateTimeEdit_ftp_starttime->setCalendarPopup(true);
    ui->dateTimeEdit_ftp_starttime->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_ftp_starttime->setDateTime(QDateTime::currentDateTime());

    ui->dateTimeEdit_ftp_stoptime->setCalendarPopup(true);
    ui->dateTimeEdit_ftp_stoptime->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_ftp_stoptime->setDateTime(QDateTime::currentDateTime());

    ui->dateTimeEdit_ftp_delete->setCalendarPopup(true);
    ui->dateTimeEdit_ftp_delete->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_ftp_delete->setDateTime(QDateTime::currentDateTime());

    CreatpieSetView();
    FtpUploadRecord_data = new sqlite_FtpUploadRecord_data("SqliteData/"); // 创建FTP上传记录表
    FtpUploadRecord_data->use_CustomSqlTableModel(ui->tableView);

    auto_delect_data = new QTimer();
    connect(auto_delect_data, SIGNAL(timeout()), this, SLOT(on_pushButton_delete_clicked()));

    auto_delect_data->start(1000);
}

UploadMonitorDialog::~UploadMonitorDialog()
{
    delete ui;
    delete FtpUploadRecord_data;
}

void UploadMonitorDialog::CreatpieSetView()
{
    QPieSeries* pieview = new QPieSeries();
    // 设置中间圆与大圆比例
    pieview->setHoleSize(0.35);
    // 扇形及扇形数据
    QPieSlice* pie1 = new QPieSlice();
    pie1->setValue(70);
    pie1->setLabel("以上传文件数397090");
    pie1->setLabelVisible(); // 显示饼状区对应的lable
    pie1->setColor(QColor("#44cb9cf"));
    pie1->setLabelColor(QColor("#44cb9cf"));
    pieview->append(pie1);

    QPieSlice* pie2 = new QPieSlice();
    pie2->setValue(30);
    pie2->setLabel("未上传文件数:1265409");
    pie2->setLabelVisible();
    pie2->setColor(QColor("#53b666"));
    pie2->setLabelColor(QColor("#53b666"));
    pieview->append(pie2);

    QChart* chart = new QChart();
    chart->setTitle("本月上传情况统计");
    chart->setTitleBrush(QColor("#808396"));
    chart->setAnimationOptions(QChart::SeriesAnimations); // 设置图表的动画选项
    chart->legend()->setAlignment(Qt::AlignBottom);       // 设置图例标签属性
    chart->legend()->setBackgroundVisible(false);
    chart->legend()->setFont(QFont("黑体", 8));
    chart->legend()->setLabelColor(QColor("#808396"));
    chart->addSeries(pieview);

    QChartView* chartview = new QChartView(ui->widget);
    chartview->setRenderHint(QPainter::Antialiasing);          // 设置抗锯齿渲染
#pragma warning(suppress : 4996)
    chartview->setRenderHint(QPainter::NonCosmeticDefaultPen); // 设置非装饰性默认笔
    chartview->setChart(chart);
    chartview->setFixedSize(611, 351);
    chartview->resize(611, 351);

    connect(pie1, &QPieSlice::clicked, this, &UploadMonitorDialog::OnSliceClicked);
    connect(pie2, &QPieSlice::clicked, this, &UploadMonitorDialog::OnSliceClicked);
}

void UploadMonitorDialog::OnSliceClicked()
{
    QPieSlice* slice = qobject_cast<QPieSlice*>(sender()); // 切换切片的展开状态
    if (slice)
    {
        slice->setExploded(!slice->isExploded());
    }
}

void UploadMonitorDialog::on_pushButton_ftp_add_clicked()
{
   
}

void UploadMonitorDialog::on_pushButton_delete_clicked()
{
    QDateTime DateStr1 = ui->dateTimeEdit_ftp_delete->dateTime();
    QString timeStr1 = DateStr1.toString("yyyy-MM-dd HH:mm:ss");

    FtpUploadRecord_data->deleteOldsql(timeStr1);
}

void UploadMonitorDialog::on_pushButton_search_status_clicked()
{

    FtpUploadRecord_data->select_usestatus_SqlTableModel(ui->lineEdit_ftp_search_status->text());
}

void UploadMonitorDialog::on_pushButton_search_get_status_clicked()
{
    QDateTime DateStr1 = ui->dateTimeEdit_ftp_starttime->dateTime();
    QString timeStr1 = DateStr1.toString("yyyy-MM-dd HH:mm:ss");
    QDateTime DateStr2 = ui->dateTimeEdit_ftp_stoptime->dateTime();
    QString timeStr2 = DateStr2.toString("yyyy-MM-dd HH:mm:ss");
    QMap<QString, int> data = FtpUploadRecord_data->get_startTostop_type(timeStr1, timeStr2);
    for (auto it = data.begin(); it != data.end(); ++it)
    {
        qDebug() << "    " << it.key() << "NUM  " << it.value();
    }
}

void UploadMonitorDialog::on_pushButton_search_clicked()
{
    QDateTime DateStr1 = ui->dateTimeEdit_ftp_starttime->dateTime();
    QString timeStr1 = DateStr1.toString("yyyy-MM-dd HH:mm:ss");
    QDateTime DateStr2 = ui->dateTimeEdit_ftp_stoptime->dateTime();
    QString timeStr2 = DateStr2.toString("yyyy-MM-dd HH:mm:ss");
    FtpUploadRecord_data->select_usetime_SqlTableModel(timeStr1, timeStr2, ui->lineEdit_ftp_search_status->text());
}
