
#include <QApplication>
#include "mainwindow.h"
#include <QDir>
#include "tools/Global.h"



int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // Initialize global systemconfigfdata after QApplication
    systemconfigfdata = new CSystemConfigData();

    QDir currentDir = QDir::current();
    // 构建文件路径（确保目录存在后在操作）
    QFileInfo fileInfo(currentDir, "VectorIcon/sensor.png");
    QString filePath = fileInfo.absoluteFilePath();

    app.setWindowIcon(QIcon(filePath));
    MainWindow *w = new MainWindow() ;
    w->show();

    int result = app.exec();

    // Clean up
    delete systemconfigfdata;
    systemconfigfdata = nullptr;

    return result;
}
