﻿#include "FMGParameterDialog.h"
#include <QDebug>
#include "tools/Global.h"

FMGParameterDialog::FMGParameterDialog(QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);

    connect(ui.pushButton_set_parameter, &QPushButton::clicked, [this]() {
        ui.stackedWidget->setCurrentIndex(0);
        });
    connect(ui.pushButton_temperature_control, &QPushButton::clicked, [this]() {
        ui.stackedWidget->setCurrentIndex(1);
        });
    connect(ui.pushButton_equipment_data, &QPushButton::clicked, [this]() {
        ui.stackedWidget->setCurrentIndex(2);
        });

    connect(ui.pushButton_read_parameter, &QPushButton::clicked, this, &FMGParameterDialog::slot_read_param);
    connect(ui.pushButton_write_parameter, &QPushButton::clicked, this, &FMGParameterDialog::slot_write_param);
    connect(ui.comboBox_auto_time, SIGNAL(currentIndexChanged(int)), this, SLOT(on_comboBox_auto_real_time(int)));
    on_comboBox_auto_real_time(0);//十分钟


}

FMGParameterDialog::~FMGParameterDialog()
{}

void FMGParameterDialog::slot_write_param()
{

    if (widgetComm != NULL && widgetComm->Connection_status == true && widgetComm->facility_connect_status == true)
    {

        float temp_rawDataFilCoef;
        UiID = ui.stackedWidget->currentIndex();
        switch (UiID)
        {
        case 0:

            BTempParmConf_value.brightTempFilCoef = ui.BtCalcFilCoef->value(); // 亮温滤波系数
            BTempParmConf_value.constTempSrcAng = ui.ObsCtSrcAng->value();   // 观测时常温源角度
            BTempParmConf_value.noiseSrcAngle = ui.obsNoiSrcAng->value();     // 观测时噪声源角度
            BTempParmConf_value.skyAngle = ui.obsSkyAng->value();          // 观测时对天空角度
            BTempParmConf_value.stayDuration = ui.obsStayTime->value();      // 观测停留时长
            BTempParmConf_value.intervalDur = ui.obsTimeIntv->value();       // 观测间隔时长
            BTempParmConf_value.innerCalibrMode = ui.intCalMode->currentIndex();    // 内定标模式

            temp_rawDataFilCoef = ui.rawDataFilCoef->value();

            widgetComm->set_sendClear_fun();
            widgetComm->set_SamCardFiltCoef_fun(temp_rawDataFilCoef);//设置原始数据滤波系数
            widgetComm->set_AntTgtAng_fun(ui.tarAng->value());
            widgetComm->set_FanPower_dun(ui.workPow->value());
            widgetComm->set_sendsetting_fun();
            break;
        case 1:
            widgetComm->set_sendClear_fun();
            widgetComm->set_AdcCardTempCtrl1_fun(ui.setTempCtrlP_P1->value(), ui.setTempCtrlP_I1->value(), ui.setTempCtrlP_D1->value(), ui.setTempCtrlP_Tag1->value());
            widgetComm->set_AdcCardTempCtrl2_fun(ui.setTempCtrlP_P2->value(), ui.setTempCtrlP_I2->value(), ui.setTempCtrlP_D2->value(), ui.setTempCtrlP_Tag2->value());
            widgetComm->set_sendsetting_fun();
            break;
        case 2:
            break;
        default:
            break;
        }
    }
    else
    {
        QMessageBox::warning(nullptr, "告警", "通讯连接失败", QMessageBox::Ok);
    }
}



void FMGParameterDialog::slot_read_obsPara()
{
    float samcard = widgetComm->get_SamCardFiltCoef_fun();
    AntAngParam_struct value1 = widgetComm->get_AntAngParam_fun();
    windTurbParam_struct value2 = widgetComm->get_windTurbParam_fun();

    ui.rawDataFilCoef->setValue(samcard);
    ui.currAng->setText(QString::number(value1.antCurrAng));
    ui.motRes->setText(QString::number(value1.m_StepPerRound));
    ui.motRpm->setText(QString::number(value1.m_StepSpeedSet));
    ui.angComp->setText(QString::number(value1.ANT_AngleOffset));

    ui.currSpd->setText(QString::number(value2.Speed));

    disconnect(widgetComm, &classWidgetComm::set_SamCardFiltCoef_fun, this, &FMGParameterDialog::slot_read_obsPara);
    disconnect(widgetComm, &classWidgetComm::set_AntTgtAng_fun, this, &FMGParameterDialog::slot_read_obsPara);
    disconnect(widgetComm, &classWidgetComm::set_FanPower_dun, this, &FMGParameterDialog::slot_read_obsPara);

    widgetComm->set_SamCardFiltCoef_read_fun(false);
    widgetComm->set_AntAngParam_read_fun(false);
    widgetComm->set_windTurbParam_read_fun(false);
}

void FMGParameterDialog::slot_read_tempCtrlPara()
{
    cardTempC_struct valueK = widgetComm->get_cardTempC_fun(0);
    cardTempC_struct valueV = widgetComm->get_cardTempC_fun(1);

    ui.setTempCtrlP_P1->setValue(valueK.P);
    ui.setTempCtrlP_I1->setValue(valueK.I);
    ui.setTempCtrlP_D1->setValue(valueK.D);
    ui.setTempCtrlP_Tag1->setValue(valueK.targetTemp);
    ui.currTemp_1->setText(QString::number(valueK.currTemp));
    ui.currPow_1->setText(QString::number(valueK.heatPower));

    ui.setTempCtrlP_P2->setValue(valueV.P);
    ui.setTempCtrlP_I2->setValue(valueV.I);
    ui.setTempCtrlP_D2->setValue(valueV.D);
    ui.setTempCtrlP_Tag2->setValue(valueV.targetTemp);
    ui.currTemp_2->setText(QString::number(valueV.currTemp));
    ui.currPow_2->setText(QString::number(valueV.heatPower));

    disconnect(widgetComm, &classWidgetComm::set_AdcCardTempCtrl_read_fun, this, &FMGParameterDialog::slot_read_tempCtrlPara);
    widgetComm->set_AdcCardTempCtrl_read_fun(false);
}

void FMGParameterDialog::slot_read_param()
{
    if (widgetComm != NULL && widgetComm->Connection_status == true && widgetComm->facility_connect_status == true)
    {
        UiID = ui.stackedWidget->currentIndex();
        switch (UiID)
        {
        case 0:
            connect(widgetComm, &classWidgetComm::signal_SamCardFiltCoef_data, this, &FMGParameterDialog::slot_read_obsPara);
            connect(widgetComm, &classWidgetComm::signal_AntAng_data, this, &FMGParameterDialog::slot_read_obsPara);
            connect(widgetComm, &classWidgetComm::signal_windTurbParam_data, this, &FMGParameterDialog::slot_read_obsPara);

            widgetComm->set_SamCardFiltCoef_read_fun(true);
            widgetComm->set_AntAngParam_read_fun(true);
            widgetComm->set_windTurbParam_read_fun(true);
            break;
        case 1:
            connect(widgetComm, &classWidgetComm::signal_AdcCardTempCtrl1_data, this, &FMGParameterDialog::slot_read_tempCtrlPara);
            widgetComm->set_AdcCardTempCtrl_read_fun(true);
            break;
        case 2:
            break;
        default:
            break;
        }
    }
    else
    {
        QMessageBox::warning(nullptr, "告警", "通讯连接失败", QMessageBox::Ok);
    }

}

void FMGParameterDialog::on_comboBox_auto_real_time(int index)
{
    if (index == 0)
    {
        index_seconds = 10*60;
    }
    else if (index == 1)
    {
        index_seconds = 30 * 60;
    }
    else if (index == 2)
    {
        index_seconds = 1 * 3600;
    }
    else if (index == 3)
    {
        index_seconds = 2 * 3600;
    }
    else if (index == 4)
    {
        index_seconds = 4 * 3600;
    }
    else if (index == 5)
    {
        index_seconds = 8 * 3600;
    }
    else if (index == 6)
    {
        index_seconds = 12 * 3600;
    }
    else if (index == 7)
    {
        index_seconds = 24 * 3600;
    }
}