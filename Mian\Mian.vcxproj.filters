﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>qml;cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>qrc;rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Form Files">
      <UniqueIdentifier>{99349809-55BA-4b9d-BF79-8FDBB0286EB3}</UniqueIdentifier>
      <Extensions>ui</Extensions>
    </Filter>
    <Filter Include="Translation Files">
      <UniqueIdentifier>{639EADAA-A684-42e4-A9AD-28FC9BCB8F7C}</UniqueIdentifier>
      <Extensions>ts</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mainwindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Comm_Protocol\CommInterface.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Comm_Protocol\SerialInterface.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Comm_Protocol\TcpClientInterface.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Comm_Protocol\UdpInterface.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Configdata\CControlSetParameterData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Configdata\CFMGParameterData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Configdata\CSystemConfigData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Configdata\printscreen.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Data_storage\qtsqlitedb.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Data_storage\sensor_status_record.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Data_storage\sqlite_faultrecord_data.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Data_storage\sqlite_ftpuploadrecord_data.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Data_storage\sqlite_systemmaintenance_data.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Data_storage\sqlite_user_data.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Data_storage\txt_routinemaintenance_data.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Data_storage\txt_staterecord_data.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\data_treating\Calibrate_data_storage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\data_treating\device_status_data_storage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\data_treating\lv1_data_storage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\data_treating\lv2_data_storage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\data_treating\microwave_radiometer_data.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\ftpClient\CurlHandle.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\ftpClient\FTPClient.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\ftpClient\FTPClientInterface.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\ftpClient\FTPconfig.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\ads_globals.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\AutoHideDockContainer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\AutoHideSideBar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\AutoHideTab.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\DockAreaTabBar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\DockAreaTitleBar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\DockAreaWidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\DockComponentsFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\DockContainerWidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\DockFocusController.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\DockingStateReader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\DockManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\DockOverlay.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\DockSplitter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\DockWidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\DockWidgetTab.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\ElidingLabel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\FloatingDockContainer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\FloatingDragPreview.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\IconProvider.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\PushButton.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\header\ResizeHandle.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Qwt_image\DirectoryScanner.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Qwt_image\labeledspectrogram.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Qwt_image\plot.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Qwt_image\spectrogramdata.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Qwt_image\ThreadPool.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\XC_DataHanding\xcProtocol_DataExchange.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\XC_DataHanding\xcProtocolDJFS.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\XC_DataHanding\xcSupportFunction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\XC_DataHanding\xcSysVar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\changesystimer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\Global.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\ObservationController.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\QCustomButton.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\comm\classwidgetcomm.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\Set\FMGParameterDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\Set\FMGWidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\Set\FTPDataDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\Set\FTPDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\BrightCalcClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\System\BreakDownDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\System\DailyMaintenanceDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\System\SensorStatusRecodeDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\System\SystemMaintenanceDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\System\UploadMonitorDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\ControlSetParameterDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\DialogIap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\flowlayout.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\HistoryDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\LogDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\SensorStatusDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\SystemRegister.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="qcustomplot.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\SystemConfiguration\SystemConfigurationDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\SystemConfiguration\CColorCardDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="UI\SystemConfiguration\HandCalibrationDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="mainwindow.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Comm_Protocol\CommInterface.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Comm_Protocol\SerialInterface.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Comm_Protocol\TcpClientInterface.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Comm_Protocol\UdpInterface.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Configdata\CControlSetParameterData.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Configdata\CFMGParameterData.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Configdata\CSystemConfigData.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Configdata\printscreen.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Data_storage\CustomSqlTableModel.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Data_storage\largetablemodel.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Data_storage\qtsqlitedb.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Data_storage\sensor_status_record.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Data_storage\sqlite_faultrecord_data.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Data_storage\sqlite_ftpuploadrecord_data.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Data_storage\sqlite_systemmaintenance_data.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Data_storage\sqlite_user_data.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Data_storage\txt_routinemaintenance_data.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Data_storage\txt_staterecord_data.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\data_treating\Calibrate_data_storage.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\data_treating\device_status_data_storage.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\data_treating\lv1_data_storage.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\data_treating\lv2_data_storage.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\data_treating\microwave_radiometer_data.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\ftpClient\FTPClientInterface.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\ads_globals.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\AutoHideDockContainer.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\AutoHideSideBar.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\AutoHideTab.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\DockAreaTabBar.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\DockAreaTitleBar.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\DockAreaTitleBar_p.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\DockAreaWidget.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\DockContainerWidget.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\DockFocusController.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\DockManager.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\DockOverlay.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\DockSplitter.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\DockWidget.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\DockWidgetTab.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\ElidingLabel.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\FloatingDockContainer.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\FloatingDragPreview.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\PushButton.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\header\ResizeHandle.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Qwt_image\DirectoryScanner.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Qwt_image\plot.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\Qwt_image\ThreadPool.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\changesystimer.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\ObservationController.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\QCustomButton.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\comm\classwidgetcomm.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\Set\FMGParameterDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\Set\FMGWidget.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\Set\FTPDataDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\Set\FTPDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="tools\BrightCalcClass.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\System\BreakDownDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\System\DailyMaintenanceDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\System\SensorStatusRecodeDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\System\SystemMaintenanceDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\System\UploadMonitorDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\ControlSetParameterDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\DialogIap.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\HistoryDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\LogDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\SensorStatusDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\SystemRegister.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="qcustomplot.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\SystemConfiguration\SystemConfigurationDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\SystemConfiguration\CColorCardDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="UI\SystemConfiguration\HandCalibrationDialog.h">
      <Filter>Header Files</Filter>
    </QtMoc>
  </ItemGroup>
  <ItemGroup>
    <QtUic Include="mainwindow.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\comm\classwidgetcomm.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\Set\FMGParameterDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\Set\FMGWidget.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\Set\FTPDataDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\Set\FTPDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\System\BreakDownDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\System\DailyMaintenanceDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\System\SensorStatusRecodeDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\System\SystemMaintenanceDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\System\UploadMonitorDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\ControlSetParameterDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\HistoryDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\LogDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\SensorStatusDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\SystemRegister.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\SystemConfiguration\SystemConfigurationDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\SystemConfiguration\CColorCardDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\SystemConfiguration\DialogButtonRight.ui">
      <Filter>Form Files</Filter>
    </QtUic>
    <QtUic Include="UI\SystemConfiguration\HandCalibrationDialog.ui">
      <Filter>Form Files</Filter>
    </QtUic>
  </ItemGroup>
  <ItemGroup>
    <QtRcc Include="res.qrc">
      <Filter>Resource Files</Filter>
    </QtRcc>
    <QtRcc Include="UI\Set\FMGParameterDialog.qrc">
      <Filter>Resource Files</Filter>
    </QtRcc>
    <QtRcc Include="tools\header\ads.qrc">
      <Filter>Resource Files</Filter>
    </QtRcc>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="tools\data_treating\cpublic_struct.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\ftpClient\CurlHandle.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\ftpClient\FTPClient.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\ftpClient\FTPconfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\header\DockComponentsFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\header\DockingStateReader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\header\IconProvider.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\Qwt_image\customfilemodel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\Qwt_image\labeledspectrogram.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\Qwt_image\spectrogramdata.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcCtrlProcess.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcDefine.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralAirFan.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralAngleMotor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralDataBoard.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralGPS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralInfrared.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralTemSource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcPeripheralWeather.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcProtocol_DataExchange.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcProtocolDJFS.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcSupportFunction.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcSysVar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\XC_DataHanding\xcType.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tools\Global.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="UI\flowlayout.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>