﻿#include "ObservationController.h"
#include <QNetworkDatagram>
#include <QProcess>
#include <QHostAddress>
#include <QDebug>
ObservationController::ObservationController(QObject *parent)
    : ObservationController(QHostAddress(QHostAddress::Any).toString(), 12345, parent)
{
}

ObservationController::ObservationController(const QString &ip, quint16 port, QObject *parent)
    : QObject(parent),
      m_udpSocket(new QUdpSocket(this)),
      m_listenIp(ip),
      m_listenPort(port)
{
    connect(m_udpSocket, &QUdpSocket::readyRead, this, [this](){
        processDatagram();
    });

    if(m_udpSocket->bind(QHostAddress(ip),port))
    {
        qDebug()<<"ip:%1\nport:%2"<<ip<<port<<"绑定成功";
    }
    else
    {
        qDebug()<<"ip:%1\nport:%2"<<ip<<port<<"绑定失败";
    }
}

ObservationController::~ObservationController()
{
    stop();
    delete m_udpSocket;
}

bool ObservationController::start()
{
    if (!m_udpSocket->bind(m_listenIp, m_listenPort))
    {
        qWarning() << "Failed to bind UDP" << m_listenIp.toString() << "port" << m_listenPort;
        return false;
    }
    startObserving();
    return true;
}

void ObservationController::stop()
{
    m_udpSocket->close();
    stopObserving();
}

void ObservationController::restart()
{
    stop();
    start();
}

void ObservationController::startObserving() {
    emit observationStarted();
}

void ObservationController::processDatagram()
{
    while (m_udpSocket->hasPendingDatagrams())
    {
        QNetworkDatagram datagram = m_udpSocket->receiveDatagram();
        QByteArray data = datagram.data();

        // 尝试解析JSON格式命令
        QJsonParseError jsonError;
        QJsonDocument doc = QJsonDocument::fromJson(data, &jsonError);

        if (jsonError.error == QJsonParseError::NoError)
        {
            QJsonObject obj = doc.object();
            QString command = obj["command"].toString();

            if (command == "start")
            {
                start();
            }
            else if (command == "stop")
            {
                stop();
            }
            else if (command == "restart")
            {
                restart();
            }
        }
        // 保持对文本命令的兼容
        else if (data == "start")
        {
            start();
        }
        else if (data == "stop")
        {
            stop();
        }
        else if (data == "restart")
        {
            restart();
        }
    }
}
