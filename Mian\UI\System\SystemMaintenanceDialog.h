﻿#ifndef SYSTEMMAINTENANCEDIALOG_H
#define SYSTEMMAINTENANCEDIALOG_H

#include <QDialog>
#include "tools/Global.h"
namespace Ui
{
    class SystemMaintenanceDialog;
}

class SystemMaintenanceDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SystemMaintenanceDialog(QWidget* parent = nullptr);
    ~SystemMaintenanceDialog();

private slots:
    void on_pushButton_device_maintain_recode_clicked();

    void on_pushButton_device_maintain_statistics_clicked();

    void on_pushButton_add_clicked();

    void on_pushButton_find_clicked();

    void on_pushButton_delect_clicked();


public slots:
    void set_SystemMaintenanceDialog_styleSheet(int);

private:
    Ui::SystemMaintenanceDialog* ui;
    QTimer* auto_delect_data;
};

#endif // SYSTEMMAINTENANCEDIALOG_H
