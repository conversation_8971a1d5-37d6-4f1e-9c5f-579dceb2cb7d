﻿#ifndef QCUSTOMBUTTON_H
#define QCUSTOMBUTTON_H
#include <QLabel>
#include <QTimer>
#include <QMouseEvent>
#include <QStyleOption>
#include <QPainter>
#include <QWidget>

class QCustomButton : public QWidget
{
    Q_OBJECT
public:
    explicit QCustomButton(QWidget* parent = nullptr, int m_width = 50, int m_height = 20);
    ~QCustomButton();

    void init();

signals:
    void stateChange(bool m_isOn);
protected:

    void paintEvent(QPaintEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;

public slots:

    void move_slot();
private:

    QLabel* myLable;
    QTimer timer;
    int m_width;
    int m_height;
    int dir;
    int position;
    int max;
    int min;
    int length;

};

#endif // QCUSTOMBUTTON_H
