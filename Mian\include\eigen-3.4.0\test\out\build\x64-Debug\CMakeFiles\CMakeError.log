Looking for a Fortran compiler failed with the following output:
-- The Fortran compiler identification is unknown
<PERSON><PERSON><PERSON> Error at CMakeLists.txt:2 (project):
  No CMAKE_Fortran_COMPILER could be found.

  Tell CMake where to find the compiler by setting either the environment
  variable "FC" or the CMake cache entry CMAKE_Fortran_COMPILER to the full
  path to the compiler, or to the compiler name if it is in the PATH.


-- Configuring incomplete, errors occurred!
See also "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/test/out/build/x64-Debug/CMakeFiles/CheckFortran/CMakeFiles/CMakeOutput.log".
See also "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/test/out/build/x64-Debug/CMakeFiles/CheckFortran/CMakeFiles/CMakeError.log".

Determining if the function sgemm_ exists failed with the following output:
Change Dir: E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/test/out/build/x64-Debug/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe cmTC_e9d57 && [1/2] Building C object CMakeFiles\cmTC_e9d57.dir\CheckFunctionExists.c.obj

[2/2] Linking C executable cmTC_e9d57.exe

FAILED: cmTC_e9d57.exe 

cmd.exe /C "cd . && "D:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\cmTC_e9d57.dir --rc=C:\PROGRA~2\WI3CF2~1\10\bin\100190~1.0\x64\rc.exe --mt=C:\PROGRA~2\WI3CF2~1\10\bin\100190~1.0\x64\mt.exe --manifests  -- "D:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\link.exe" /nologo CMakeFiles\cmTC_e9d57.dir\CheckFunctionExists.c.obj  /out:cmTC_e9d57.exe /implib:cmTC_e9d57.lib /pdb:cmTC_e9d57.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."

LINK Pass 1: command "D:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\link.exe /nologo CMakeFiles\cmTC_e9d57.dir\CheckFunctionExists.c.obj /out:cmTC_e9d57.exe /implib:cmTC_e9d57.lib /pdb:cmTC_e9d57.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTFILE:CMakeFiles\cmTC_e9d57.dir/intermediate.manifest CMakeFiles\cmTC_e9d57.dir/manifest.res" failed (exit code 1120) with the following output:
CheckFunctionExists.c.obj : error LNK2019: 无法解析的外部符号 sgemm_，函数 main 中引用了该符号

cmTC_e9d57.exe : fatal error LNK1120: 1 个无法解析的外部命令

ninja: build stopped: subcommand failed.




Determining if the include file pthread.h exists failed with the following output:
Change Dir: E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/test/out/build/x64-Debug/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe cmTC_ce55f && [1/2] Building C object CMakeFiles\cmTC_ce55f.dir\CheckIncludeFile.c.obj

FAILED: CMakeFiles/cmTC_ce55f.dir/CheckIncludeFile.c.obj 

"D:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\cl.exe"  /nologo   /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1 /showIncludes /FoCMakeFiles\cmTC_ce55f.dir\CheckIncludeFile.c.obj /FdCMakeFiles\cmTC_ce55f.dir\ /FS -c CheckIncludeFile.c

CheckIncludeFile.c(1): fatal error C1083: 无法打开包括文件: “pthread.h”: No such file or directory
ninja: build stopped: subcommand failed.




