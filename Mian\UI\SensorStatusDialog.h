﻿#ifndef SENSORSTATUSDIALOG_H
#define SENSORSTATUSDIALOG_H

#include <QDialog>
#include "tools/Global.h"
#include "UI/flowlayout.h"
#include "ui_SensorStatusDialog.h"
#include <QFormLayout>
#include "UI/comm/classwidgetcomm.h"
#include <QTimer>
namespace Ui
{
    class SensorStatusDialog;
}

class SensorStatusDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SensorStatusDialog(QWidget* parent = nullptr);
    ~SensorStatusDialog();

public slots:
    void set_SensorStatusDialog_stylesheet(int);
    void slot_Sensor_Status_Timer_outtime();

private slots:
    // 槽函数，采集卡电源状态获取
    void slot_Power_update_receive();
    // 槽函数，采集卡状态获取
    void slot_Status_update_receive();
    // 槽函数，气象六要素获取
    void slot_Six_update_receive();
    // 槽函数，GPS数据获取
    void slot_GPS_update_receive();
    // 槽函数，接收机温度获取
    void slot_radioTemp_update_receive();
    // 槽函数，黑体温度获取
    void slot_boldface_update_receive();
    // 槽函数，常温源温度获取
    void slot_normalTemp_update_receive();
    // 槽函数，风机状态获取
    void slot_FAN_update_receive();
    // 槽函数，噪声源开关获取
    void slot_NoiseSrcSw_update_receive();
    // 槽函数，天线角度获取
    void slot_antAng_update_receive();

private:
    Ui::SensorStatusDialog* ui;

    QTimer* qTimer_t;

    /**      传感器状态相关      **/
    // 电源相关
    QString Power_Name[4] = { "：采集卡工作电源", "：天线电机工作电源", "：六要素GPS工作电源", "：风机工作电源" };
    FlowLayout* widget_1_flowLayout;
    QFormLayout* Power_layout[4];
    QLabel* Power_lable[4];
    QLabel* Power_Led_lable[4];
    // 状态相关
    QString Status_Name[4] = { "：采集卡工作状态", "：天线电机工作状态","：六要素GPS工作状态", "：风机工作状态" };
    FlowLayout* widget_2_flowLayout;
    QFormLayout* Status_layout[4];
    QLabel* Status_lable[4];
    QLabel* Status_Led_lable[4];

    /**  气象六要素和GPS相关  **/
    // 气象六要素相关
    QString Six_Name[6] = { "湿度：", "温度：", "气压：", "风速：", "风向：", "10分钟降水量：" };
    FlowLayout* widget_3_flowLayout;
    QFormLayout* Six_layout[6];
    QLabel* Six_lable[6];
    QLabel* Six_Led_lable[6];
    // GPS数据相关
    QString GPS_Name[8] = { "经度：", "纬度：", "时间：", "日期：", "世纪秒：", "HDOP：", "海拔：", "地拔：" };
    FlowLayout* widget_4_flowLayout;
    QFormLayout* GPS_layout[8];
    QLabel* GPS_lable[8];
    QLabel* GPS_Led_lable[8];

    /**      其他传感器     **/
    // 接收机温度
    QString radioTemp_Name[6] = { "K通道温度1：", "K通道温度2：", "K通道温度3：", "V通道温度1：", "V通道温度2：", "V通道温度3：" };
    FlowLayout* widget_5_flowLayout;
    QFormLayout* radioTemp_layout[6];
    QLabel* radioTemp_lable[6];
    QLabel* radioTemp_Led_lable[6];

    QString boldface_Name[4] = { "黑体温度1：","黑体温度2：","黑体温度3：" ,"黑体温度4：" };
    QFormLayout* boldface_layout[4];
    QLabel* boldface_lable[4];
    QLabel* boldface_Led_lable[4];

    // 常温源温度
    QString normalTemp_Name = "常温源温度：";
    QFormLayout* normalTemp_layout;
    QLabel* normalTemp_lable;
    QLabel* normalTemp_Led_lable;


    // 其他
    FlowLayout* widget_7_flowLayout;

    // 风机转速
    QString FAN_Name = "风机转速：";
    QFormLayout* FAN_layout;
    QLabel* FAN_lable;
    QLabel* FAN_Led_lable;

    // 噪声源开关
    QString NoiseSrcSw_Name = "噪声源开关：";
    QFormLayout* NoiseSrcSw_layout;
    QLabel* NoiseSrcSw_lable;
    QLabel* NoiseSrcSw_Led_lable;

    // 当前天线角度
    QString antAng_Name = "天线角度：";
    QFormLayout* antAng_layout;
    QLabel* antAng_lable;
    QLabel* antAng_Led_lable;



    six_Temp_struct six_temp;
    bool NoiseSrcSw;
    GPSParam_struct gps;
    AntAngParam_struct  antAng;
    LowTemp lowtemp;
    float lowAveTemp;
    WeathersixEles_struct weathersix;
    windTurbParam_struct fan;
    workStatus_struct work;
    PowerStatus_struct power;


    classWidgetComm* sensorComm;

    bool isConnected = false;

    // 指示灯设置
    void setLED(QLabel* label, int color, int size);

    // 传感器获取数据
    void Sensor_Status_update_send();
    // 传感器更新数据
    void Sensor_Status_update_data();
    // 更新电源信息
    void Power_update_data(QLabel* label, uchar data);
    // 更新状态信息
    void Status_update_data(QLabel* label, uchar data);
    // 更新气象六要素信息
    void Six_update_data();
    // 更新GPS信息
    void GPS_update_data();
    // 更新通道温度信息
    void RadioTemp_update_data();

};

#endif // SENSORSTATUSDIALOG_H
