﻿/****************************************************/
/***
这个界面主要是用来设置定标相关的内容
主要功能包括：手动定标，黑体定标，斜天空倾斜定标

***/
/****************************************************/
#include "ControlSetParameterDialog.h"
#include "tools/Global.h"
#include <QThread>

ControlSetParameterDialog::ControlSetParameterDialog(QWidget *parent) : QDialog(parent),
                                                                        ui(new Ui::ControlSetParameterDialog)
{
    ui->setupUi(this);
    this->setWindowTitle("系统定标");
    this->setWindowFlags(this->windowFlags() | Qt::WindowMaximizeButtonHint);
    this->setWindowFlags(Qt::Window);

    ui->tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    connect(ui->comboBox_dingbiao_mode, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &ControlSetParameterDialog::slot_changeCalibrationMode);
    connect(ui->spinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &ControlSetParameterDialog::slot_updateSkyTiltAngle);
    connect(ui->checkBox_autoMode, &QCheckBox::stateChanged, this, &ControlSetParameterDialog::slot_changeAutoMode);

    slot_changeCalibrationMode();
    slot_updateSkyTiltAngle();
    slot_changeAutoMode();
    slot_updateParameter();
    Calibration_param_Read_fun();

    connect(ui->doubleSpinBox_yedan_angle, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &ControlSetParameterDialog::slot_updateParameter);
    connect(ui->doubleSpinBox_normal_temp_angle, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &ControlSetParameterDialog::slot_updateParameter);
    connect(ui->doubleSpinBox_noise_angle, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &ControlSetParameterDialog::slot_updateParameter);
    connect(ui->doubleSpinBox_yedan_bt, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &ControlSetParameterDialog::slot_updateParameter);
    connect(ui->spinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &ControlSetParameterDialog::slot_updateParameter);
    connect(ui->doubleSpinBox_skTiltAng1, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &ControlSetParameterDialog::slot_updateParameter);
    connect(ui->doubleSpinBox_skTiltAng2, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &ControlSetParameterDialog::slot_updateParameter);
    connect(ui->doubleSpinBox_skTiltAng3, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &ControlSetParameterDialog::slot_updateParameter);
    connect(ui->doubleSpinBox_skTiltAng4, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &ControlSetParameterDialog::slot_updateParameter);
    connect(ui->doubleSpinBox_skTiltAng5, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &ControlSetParameterDialog::slot_updateParameter);
    connect(ui->doubleSpinBox_look_time, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &ControlSetParameterDialog::slot_updateParameter);

    connect(ui->pushButton_readData, &QPushButton::clicked, this, &ControlSetParameterDialog::slot_readButtonTriggered);
    connect(ui->pushButton_settlement, &QPushButton::clicked, this, &ControlSetParameterDialog::slot_paramCalcButtonTriggered);
    connect(ui->pushButton_autoCalib, &QPushButton::clicked, this, &ControlSetParameterDialog::slot_changeAutoCalibrationMode);
    connect(ui->pushButton_updateSave, &QPushButton::clicked, this, &ControlSetParameterDialog::slot_saveButtonTriggered);
    connect(ui->pushButton_paramClear, &QPushButton::clicked, this, &ControlSetParameterDialog::slot_clearButtonTriggered);
    connect(ui->pushButton_ClibParamRead, &QPushButton::clicked, this, &ControlSetParameterDialog::slot_readCalibParamButtonTriggered);

    m_Timer = new QTimer(this);
    connect(m_Timer, &QTimer::timeout, this, &ControlSetParameterDialog::slot_timerTimeout);
}

ControlSetParameterDialog::~ControlSetParameterDialog()
{
    delete ui;
}

/**
 * @brief 设置天线角度和噪声源槽函数
 *
 * 根据传入的参数设置天线角度和噪声源，并在设置成功后断开信号槽连接，同时更新天线角度的UI显示。
 *
 * @param var 包含天线角度和噪声源相关信息的QVector<ushort>类型参数
 */
void ControlSetParameterDialog::solt_setAntennaAngleAndNoiseSrc(QVector<ushort> var)
{
    if (widgetComm->get_NoiseSrcSw_fun() == m_NoiseSrcSw && widgetComm->get_AntAngParam_fun().antCurrAng == m_AntTgtAng)
    {
        // 天线角度电机参数读取标志位
        widgetComm->set_AntAngParam_read_fun(false);
        // 噪声源读取标志位
        widgetComm->set_NoiseSrcSw_read_fun(false);

        if (m_NoiseSrcSw == true)
        {
            log_record("设置噪声源开启成功");
        }
        else
        {
            log_record("设置噪声源关闭成功");
        }

        log_record("设置天线目标角度成功");
        if (ui->comboBox_dingbiao_mode->currentIndex() == 0)
        {
            switch (m_BbCalibrationStep)
            {
            case SET_LN2:
            case ReadBbVoltageLN2:
                log_record("天线已转项液氮源");
                break;
            case SET_HOT:
            case ReadBbVoltageNT:
                log_record("天线已转项常温源");
                break;
            case SET_NOISE:
            case ReadBbVoltageNoiseSrc:
                log_record("天线已转项噪声源");
                break;
            }
        }
        else if (ui->comboBox_dingbiao_mode->currentIndex() == 1)
        {
            switch (m_SkyTiltCalibrationStep)
            {
            case ReadBbVoltageSkyTilt1:
                log_record("天线已转项角度1");
                break;
            case ReadBbVoltageSkyTilt2:
                log_record("天线已转项角度2");
                break;
            case ReadBbVoltageSkyTilt3:
                log_record("天线已转项角度3");
                break;
            case ReadBbVoltageSkyTilt4:
                log_record("天线已转项角度4");
                break;
            case ReadBbVoltageSkyTilt5:
                log_record("天线已转项角度5");
                break;
            }
        }

        // 天线和噪声源设置成功，断开信号槽连接
        disconnect(widgetComm, &classWidgetComm::signal_AntAng_data, this, &ControlSetParameterDialog::solt_setAntennaAngleAndNoiseSrc);
        disconnect(widgetComm, &classWidgetComm::signal_NoiseSrcSw_data, this, &ControlSetParameterDialog::solt_setAntennaAngleAndNoiseSrc);
    }
    // 更新天线角度UI显示
    ui->lineEdit_antAng->setText(QString::number(widgetComm->get_AntAngParam_fun().antCurrAng, 'f', 1));
}

/**
 * @brief ControlSetParameterDialog类的槽函数，处理定时器超时事件
 *
 * 根据当前的校准步骤（m_BbCalibrationStep）执行相应的操作。
 *
 * @details
 * - 当校准步骤为NoCail时，设置天线角度和噪声源，并将校准步骤设置为SET_LN2。
 * - 当校准步骤为SET_LN2时，如果噪声源开关和天线角度设置正确，则设置读取LN2状态下的基带电压，并将校准步骤设置为ReadBbVoltageLN2。
 * - 当校准步骤为ReadBbVoltageLN2时，等待一定时间（m_ObsTime）后，停止读取电压，并设置天线角度和噪声源，将校准步骤设置为SET_HOT。
 * - 当校准步骤为SET_HOT时，如果噪声源开关和天线角度设置正确，则设置读取HOT状态下的基带电压，并将校准步骤设置为ReadBbVoltageNT。
 * - 当校准步骤为ReadBbVoltageNT时，等待一定时间（m_ObsTime）后，停止读取电压和温度，并设置天线角度和噪声源，将校准步骤设置为SET_NOISE。
 * - 当校准步骤为SET_NOISE时，如果噪声源开关和天线角度设置正确，则设置读取噪声源状态下的基带电压，并将校准步骤设置为ReadBbVoltageNoiseSrc。
 * - 当校准步骤为ReadBbVoltageNoiseSrc时，等待一定时间（m_ObsTime）后，停止读取电压，并将校准步骤设置为BbCalibrationCalculate。
 * - 当校准步骤为BbCalibrationCalculate时，执行校准计算，并将校准步骤重置为NoCail，停止定时器。
 */
void ControlSetParameterDialog::slot_timerTimeout()
{

    if (ui->comboBox_dingbiao_mode->currentIndex() == 0)
    {
        switch (m_BbCalibrationStep)
        {
        case NoCail:
            setAntennaAngleAndNoiseSrc(m_LN2_Angle, false);
            m_BbCalibrationStep = SET_LN2;
            break;
        case SET_LN2:

            if (widgetComm->get_NoiseSrcSw_fun() == m_NoiseSrcSw && widgetComm->get_AntAngParam_fun().antCurrAng == m_AntTgtAng)
            {
                setReadBbVoltageLN2();
                m_BbCalibrationStep = ReadBbVoltageLN2;
                m_CurTime = 0;
            }
            break;
        case ReadBbVoltageLN2:
            m_CurTime++;
            if (m_CurTime >= m_ObsTime)
            {
                widgetComm->set_AgvVoltage_read(false);
                disconnect(widgetComm, &classWidgetComm::signal_RadioVolAverage_data, this, &ControlSetParameterDialog::slot_updateVoltageData);

                setAntennaAngleAndNoiseSrc(m_NT_Angle, false);
                m_BbCalibrationStep = SET_HOT;
            }
            break;
        case SET_HOT:
            if (widgetComm->get_NoiseSrcSw_fun() == m_NoiseSrcSw && widgetComm->get_AntAngParam_fun().antCurrAng == m_AntTgtAng)
            {
                setReadBbVoltageNT();
                m_BbCalibrationStep = ReadBbVoltageNT;
                m_CurTime = 0;
            }
            break;
        case ReadBbVoltageNT:
            m_CurTime++;
            if (m_CurTime >= m_ObsTime)
            {
                widgetComm->set_AgvVoltage_read(false);
                widgetComm->set_fTempLowAverage_read_fun(false);
                widgetComm->set_fistTempBt_read_fun(false);

                disconnect(widgetComm, &classWidgetComm::signal_RadioVolAverage_data, this, &ControlSetParameterDialog::slot_updateVoltageData);
                disconnect(widgetComm, &classWidgetComm::signal_fTemp_LowAverage_data, this, &ControlSetParameterDialog::slot_updateBrightTempAndAvgTemp);
                disconnect(widgetComm, &classWidgetComm::signal_fistTempBt_data, this, &ControlSetParameterDialog::slot_updateBrightTempAndAvgTemp);

                setAntennaAngleAndNoiseSrc(m_Noise_Angle, true);
                m_BbCalibrationStep = SET_NOISE;
            }
            break;
        case SET_NOISE:
            if (widgetComm->get_NoiseSrcSw_fun() == m_NoiseSrcSw && widgetComm->get_AntAngParam_fun().antCurrAng == m_AntTgtAng)
            {
                setReadBbVoltageNoiseSrc();
                m_BbCalibrationStep = ReadBbVoltageNoiseSrc;
                m_CurTime = 0;
            }
            break;
        case ReadBbVoltageNoiseSrc:
            m_CurTime++;
            if (m_CurTime >= m_ObsTime)
            {
                widgetComm->set_AgvVoltage_read(false);
                disconnect(widgetComm, &classWidgetComm::signal_RadioVolAverage_data, this, &ControlSetParameterDialog::slot_updateVoltageData);
                m_BbCalibrationStep = BbCalibrationCalculate;
            }
            break;
        case BbCalibrationCalculate:
            Settlement_fun();
            log_record("液氮定标完成");
            slot_changeAutoCalibrationMode();
            m_BbCalibrationStep = NoCail;
            m_Timer->stop();
            break;
        }
    }
    else if (ui->comboBox_dingbiao_mode->currentIndex() == 1)
    {
        switch (m_SkyTiltCalibrationStep)
        {
        case NoSkyTiltCail:
            setAntennaAngleAndNoiseSrc(m_SkyTiltAng1, false);
            m_SkyTiltCalibrationStep = SET_SKYTILT1;
            break;
        case SET_SKYTILT1:
            if (widgetComm->get_NoiseSrcSw_fun() == m_NoiseSrcSw && widgetComm->get_AntAngParam_fun().antCurrAng == m_AntTgtAng)
            {
                m_SkyTiltCalibrationStep = ReadBbVoltageSkyTilt1;
                setReadVoltageAngle();
                m_CurTime = 0;
            }
            break;
        case ReadBbVoltageSkyTilt1:
            m_CurTime++;
            if (m_CurTime >= m_ObsTime)
            {
                widgetComm->set_AgvVoltage_read(false);
                disconnect(widgetComm, &classWidgetComm::signal_RadioVolAverage_data, this, &ControlSetParameterDialog::slot_updateVoltageData);
                setAntennaAngleAndNoiseSrc(m_SkyTiltAng2, false);
                m_SkyTiltCalibrationStep = SET_SKYTILT2;
            }
            break;
        case SET_SKYTILT2:
            if (widgetComm->get_NoiseSrcSw_fun() == m_NoiseSrcSw && widgetComm->get_AntAngParam_fun().antCurrAng == m_AntTgtAng)
            {
                m_SkyTiltCalibrationStep = ReadBbVoltageSkyTilt2;
                setReadVoltageAngle();
                m_CurTime = 0;
            }
            break;
        case ReadBbVoltageSkyTilt2:
            m_CurTime++;
            if (m_CurTime >= m_ObsTime)
            {
                widgetComm->set_AgvVoltage_read(false);
                disconnect(widgetComm, &classWidgetComm::signal_RadioVolAverage_data, this, &ControlSetParameterDialog::slot_updateVoltageData);
                setAntennaAngleAndNoiseSrc(m_SkyTiltAng3, false);
                m_SkyTiltCalibrationStep = SET_SKYTILT3;
            }
            break;
        case SET_SKYTILT3:
            if (widgetComm->get_NoiseSrcSw_fun() == m_NoiseSrcSw && widgetComm->get_AntAngParam_fun().antCurrAng == m_AntTgtAng)
            {
                m_SkyTiltCalibrationStep = ReadBbVoltageSkyTilt3;
                setReadVoltageAngle();
                m_CurTime = 0;
            }
            break;
        case ReadBbVoltageSkyTilt3:
            m_CurTime++;
            if (m_CurTime >= m_ObsTime)
            {
                widgetComm->set_AgvVoltage_read(false);
                disconnect(widgetComm, &classWidgetComm::signal_RadioVolAverage_data, this, &ControlSetParameterDialog::slot_updateVoltageData);
                if (m_SkyTiltAngNum > 3)
                {
                    setAntennaAngleAndNoiseSrc(m_SkyTiltAng4, false);
                    m_SkyTiltCalibrationStep = SET_SKYTILT4;
                }
                else
                {
                    m_SkyTiltCalibrationStep = SkyTiltCalibrationCalculate;
                }
            }
            break;
        case SET_SKYTILT4:
            if (widgetComm->get_NoiseSrcSw_fun() == m_NoiseSrcSw && widgetComm->get_AntAngParam_fun().antCurrAng == m_AntTgtAng)
            {
                m_SkyTiltCalibrationStep = ReadBbVoltageSkyTilt4;
                setReadVoltageAngle();
                m_CurTime = 0;
            }
            break;
        case ReadBbVoltageSkyTilt4:
            m_CurTime++;
            {
                widgetComm->set_AgvVoltage_read(false);
                disconnect(widgetComm, &classWidgetComm::signal_RadioVolAverage_data, this, &ControlSetParameterDialog::slot_updateVoltageData);
                if (m_SkyTiltAngNum > 4)
                {
                    setAntennaAngleAndNoiseSrc(m_SkyTiltAng5, false);
                    m_SkyTiltCalibrationStep = SET_SKYTILT5;
                }
                else
                {
                    m_SkyTiltCalibrationStep = SkyTiltCalibrationCalculate;
                }
            }
            break;
        case SET_SKYTILT5:
            if (widgetComm->get_NoiseSrcSw_fun() == m_NoiseSrcSw && widgetComm->get_AntAngParam_fun().antCurrAng == m_AntTgtAng)
            {
                m_SkyTiltCalibrationStep = ReadBbVoltageSkyTilt5;
                setReadVoltageAngle();
                m_CurTime = 0;
            }
            break;
        case ReadBbVoltageSkyTilt5:
            m_CurTime++;
            if (m_CurTime >= m_ObsTime)
            {
                widgetComm->set_AgvVoltage_read(false);
                disconnect(widgetComm, &classWidgetComm::signal_RadioVolAverage_data, this, &ControlSetParameterDialog::slot_updateVoltageData);
                m_SkyTiltCalibrationStep = SkyTiltCalibrationCalculate;
            }
            break;
        case SkyTiltCalibrationCalculate:
            Settlement_fun();
            log_record("天空倾斜定标完成");
            slot_changeAutoCalibrationMode();
            m_SkyTiltCalibrationStep = NoSkyTiltCail;
            m_Timer->stop();
            break;
        }
    }
}

/**
 * @brief 更新电压数据槽函数
 *
 * 根据当前校准步骤，更新校准数据中的电压数据。
 */
void ControlSetParameterDialog::slot_updateVoltageData(QVector<ushort> var)
{
    AgvVoltage_struct AgvVoltage = widgetComm->get_AgvVoltage_fun();
    if (ui->comboBox_dingbiao_mode->currentIndex() == 0)
    {
        switch (m_BbCalibrationStep)
        {
        case ReadBbVoltageLN2:
            for (int i = 0; i < 16; i++)
            {
                m_calibData.lnSrcVolt[i] = AgvVoltage.Votage[i];
            }
            table_update_fun(Value_LN2_Voltage, m_calibData.lnSrcVolt);
            log_record("液氮源电压读取成功");
            if (m_AutoCalibFlag == false)
            {
                m_LiqN2SrcReadFlag = true;
            }
            break;
        case ReadBbVoltageNT:
            for (int i = 0; i < 16; i++)
            {
                m_calibData.normTempSrcVolt[i] = AgvVoltage.Votage[i];
            }
            table_update_fun(Value_AT_Voltage, m_calibData.normTempSrcVolt);
            log_record("常温源电压读取成功");
            if (m_AutoCalibFlag == false)
            {
                m_NoiseSrcReadFlag = true;
            }
            break;
        case ReadBbVoltageNoiseSrc:
            for (int i = 0; i < 16; i++)
            {
                m_calibData.noiseSrcVolt[i] = AgvVoltage.Votage[i];
            }
            table_update_fun(Value_Noise_Voltage, m_calibData.noiseSrcVolt);
            log_record("噪声源电压读取成功");
            if (m_AutoCalibFlag == false)
            {
                m_SrcTempReadFlag = true;
            }
            break;
        }
    }
    else if (ui->comboBox_dingbiao_mode->currentIndex() == 1)
    {
        switch (m_SkyTiltCalibrationStep)
        {
        case ReadBbVoltageSkyTilt1:
            for (int i = 0; i < 16; i++)
            {
                m_calibData.SkyTiltAng1[i] = AgvVoltage.Votage[i];
            }
            table_update_fun(Value_Angle1, m_calibData.SkyTiltAng1);
            log_record("角度1源电压读取成功");
            break;
        case ReadBbVoltageSkyTilt2:
            for (int i = 0; i < 16; i++)
            {
                m_calibData.SkyTiltAng2[i] = AgvVoltage.Votage[i];
            }
            table_update_fun(Value_Angle2, m_calibData.SkyTiltAng2);
            log_record("角度2源电压读取成功");
            break;
        case ReadBbVoltageSkyTilt3:
            for (int i = 0; i < 16; i++)
            {
                m_calibData.SkyTiltAng3[i] = AgvVoltage.Votage[i];
            }
            table_update_fun(Value_Angle3, m_calibData.SkyTiltAng3);
            log_record("角度3源电压读取成功");
            break;
        case ReadBbVoltageSkyTilt4:
            for (int i = 0; i < 16; i++)
            {
                m_calibData.SkyTiltAng4[i] = AgvVoltage.Votage[i];
            }
            table_update_fun(Value_Angle4, m_calibData.SkyTiltAng4);
            log_record("角度4源电压读取成功");
            break;
        case ReadBbVoltageSkyTilt5:
            for (int i = 0; i < 16; i++)
            {
                m_calibData.SkyTiltAng5[i] = AgvVoltage.Votage[i];
            }
            table_update_fun(Value_Angle5, m_calibData.SkyTiltAng5);
            log_record("角度5源电压读取成功");
            break;
        }
    }

    if (m_AutoCalibFlag == false)
    {
        if (m_LiqN2SrcReadFlag&& m_NoiseSrcReadFlag&& m_SrcTempReadFlag)
        {
            ui->pushButton_settlement->setEnabled(true);
        }
        widgetComm->set_AgvVoltage_read(false);
        disconnect(widgetComm, &classWidgetComm::signal_RadioVolAverage_data, this, &ControlSetParameterDialog::slot_updateVoltageData);
    }
}

/**
 * @brief 更新亮温和平均温度的槽函数
 *
 * 根据输入的参数更新亮温和平均温度，并根据自动校准标志位（m_AutoCalibFlag）设置不同的温度值。
 */
void ControlSetParameterDialog::slot_updateBrightTempAndAvgTemp(QVector<ushort> var)
{
    for (int i = 0; i < 16; i++)
    {
        if (m_AutoCalibFlag)
        {
            m_calibData.normTempSrcTemp[i] = widgetComm->get_fistTempBt_fun();
        }
        else
        {
            m_calibData.normTempSrcTemp[i] = widgetComm->get_fTempLowAverage_fun() + 273;
        }
    }
    table_update_fun(Value_AT_Temp, m_calibData.normTempSrcTemp);
    if (m_AutoCalibFlag == false)
    {
        widgetComm->set_fTempLowAverage_read_fun(false);
        widgetComm->set_fistTempBt_read_fun(false);
    }
}

/**
 * @brief slot_changeCalibrationMode 函数用于更改校准模式
 *
 * 根据下拉框comboBox_dingbiao_mode的当前索引，切换不同的校准模式，显示或隐藏不同的UI组件。
 *
 * 当索引为0时，显示与“液氮模式”相关的UI组件，隐藏与“天空倾斜模式”相关的UI组件。
 * 当索引为1时，显示与“天空倾斜模式”相关的UI组件，隐藏与“液氮模式”相关的UI组件，并调用slot_updateSkyTiltAngle函数更新天空倾斜角度。
 */
void ControlSetParameterDialog::slot_changeCalibrationMode()
{
    QVariant v(0);
    QVariant k(1 | 32);

    switch (ui->comboBox_dingbiao_mode->currentIndex())
    {
    case 0:
        ui->label_28->show();
        ui->doubleSpinBox_yedan_angle->show();
        ui->label_26->show();
        ui->doubleSpinBox_normal_temp_angle->show();
        ui->label_29->show();
        ui->doubleSpinBox_noise_angle->show();
        ui->label_59->show();
        ui->doubleSpinBox_yedan_bt->show();

        ui->label->hide();
        ui->spinBox->hide();
        ui->label_2->hide();
        ui->label_3->hide();
        ui->label_4->hide();
        ui->doubleSpinBox_skTiltAng1->hide();
        ui->doubleSpinBox_skTiltAng2->hide();
        ui->doubleSpinBox_skTiltAng3->hide();
        ui->label_5->hide();
        ui->label_6->hide();
        ui->doubleSpinBox_skTiltAng4->hide();
        ui->doubleSpinBox_skTiltAng5->hide();

        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(1, 0), k, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(2, 0), k, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(3, 0), k, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(4, 0), v, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(5, 0), v, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(6, 0), v, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(7, 0), v, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(8, 0), v, Qt::UserRole - 1);
        break;
    case 1:
        ui->label_28->hide();
        ui->doubleSpinBox_yedan_angle->hide();
        ui->label_26->hide();
        ui->doubleSpinBox_normal_temp_angle->hide();
        ui->label_29->hide();
        ui->doubleSpinBox_noise_angle->hide();
        ui->label_59->hide();
        ui->doubleSpinBox_yedan_bt->hide();

        ui->label->show();
        ui->spinBox->show();
        ui->label_2->show();
        ui->label_3->show();
        ui->label_4->show();
        ui->doubleSpinBox_skTiltAng1->show();
        ui->doubleSpinBox_skTiltAng2->show();
        ui->doubleSpinBox_skTiltAng3->show();
        slot_updateSkyTiltAngle();

        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(1, 0), v, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(2, 0), v, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(3, 0), v, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(4, 0), k, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(5, 0), k, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(6, 0), k, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(7, 0), k, Qt::UserRole - 1);
        ui->comboBox_system_dingbiao_angle->model()->setData(ui->comboBox_system_dingbiao_angle->model()->index(8, 0), k, Qt::UserRole - 1);
        break;
    }
}

/**
 * @brief slot_changeAutoMode 槽函数，用于改变自动模式状态
 *
 * 当用户选择或取消选择自动模式时，该函数会被调用。
 * 根据自动模式的状态，启用或禁用界面上的按钮，并设置自动校准标志。
 */
void ControlSetParameterDialog::slot_changeAutoMode()
{
    if (ui->checkBox_autoMode->isChecked())
    {
        ui->pushButton_readData->setEnabled(false);
        ui->pushButton_settlement->setEnabled(false);
        ui->pushButton_autoCalib->setEnabled(true);
        ui->pushButton_updateSave->setEnabled(false);
        ui->comboBox_system_dingbiao_angle->setEnabled(false);


        m_AutoCalibFlag = true;
    }
    else
    {
        ui->pushButton_readData->setEnabled(true);
        ui->pushButton_settlement->setEnabled(false);
        ui->pushButton_autoCalib->setEnabled(false);
        ui->pushButton_updateSave->setEnabled(false);
        ui->comboBox_system_dingbiao_angle->setEnabled(true);

        m_AutoCalibFlag = false;
    }
}

/**
 * @brief 更新天顶倾斜角度的槽函数
 *
 * 根据spinBox的值更新天顶倾斜角度相关的UI元素显示状态。
 */
void ControlSetParameterDialog::slot_updateSkyTiltAngle()
{
    switch (ui->spinBox->value())
    {
    case 3:
        ui->label_5->hide();
        ui->label_6->hide();
        ui->doubleSpinBox_skTiltAng4->hide();
        ui->doubleSpinBox_skTiltAng5->hide();
        break;
    case 4:
        ui->label_5->show();
        ui->label_6->hide();
        ui->doubleSpinBox_skTiltAng4->show();
        ui->doubleSpinBox_skTiltAng5->hide();
        break;
    case 5:
        ui->label_5->show();
        ui->label_6->show();
        ui->doubleSpinBox_skTiltAng4->show();
        ui->doubleSpinBox_skTiltAng5->show();
        break;
    }
}

/**
 * @brief 更新参数槽函数
 *
 * 此函数用于更新参数对话框中的参数值，并将其存储在相应的成员变量中。
 */
void ControlSetParameterDialog::slot_updateParameter()
{
    m_LN2_Angle = ui->doubleSpinBox_yedan_angle->value();
    m_NT_Angle = ui->doubleSpinBox_normal_temp_angle->value();
    m_Noise_Angle = ui->doubleSpinBox_noise_angle->value();
    m_ln2Temp = ui->doubleSpinBox_yedan_bt->value();

    m_SkyTiltAngNum = ui->spinBox->value();
    m_SkyTiltAng1 = ui->doubleSpinBox_skTiltAng1->value();
    m_SkyTiltAng2 = ui->doubleSpinBox_skTiltAng2->value();
    m_SkyTiltAng3 = ui->doubleSpinBox_skTiltAng3->value();
    m_SkyTiltAng4 = ui->doubleSpinBox_skTiltAng4->value();
    m_SkyTiltAng5 = ui->doubleSpinBox_skTiltAng5->value();

    m_ObsTime = ui->doubleSpinBox_look_time->value();
}

/**
 * @brief slot_readButtonTriggered 函数处理
 *
 * 根据组合框当前选中的选项，设置基带校准步骤，并执行相应的操作。
 */
void ControlSetParameterDialog::slot_readButtonTriggered()
{
    switch (ui->comboBox_system_dingbiao_angle->currentIndex())
    {
    case 0:
        m_BbCalibrationStep = NoCail;
        m_SkyTiltCalibrationStep = NoSkyTiltCail;
        break;
    case 1:
        m_BbCalibrationStep = ReadBbVoltageLN2;
        setAntennaAngleAndNoiseSrc(m_LN2_Angle, false);
        setReadBbVoltageLN2();
        break;
    case 2:
        m_BbCalibrationStep = ReadBbVoltageNT;
        setAntennaAngleAndNoiseSrc(m_NT_Angle, false);
        setReadBbVoltageNT();
        break;
    case 3:
        m_BbCalibrationStep = ReadBbVoltageNoiseSrc;
        setAntennaAngleAndNoiseSrc(m_Noise_Angle, true);
        setReadBbVoltageNoiseSrc();
        break;
    case 4:
        m_SkyTiltCalibrationStep = ReadBbVoltageSkyTilt1;
        setAntennaAngleAndNoiseSrc(m_SkyTiltAng1, false);
        setReadVoltageAngle();
        break;
    case 5:
        m_SkyTiltCalibrationStep = ReadBbVoltageSkyTilt2;
        setAntennaAngleAndNoiseSrc(m_SkyTiltAng2, false);
        setReadVoltageAngle();
        break;
    case 6:
        m_SkyTiltCalibrationStep = ReadBbVoltageSkyTilt3;
        setAntennaAngleAndNoiseSrc(m_SkyTiltAng3, false);
        setReadVoltageAngle();
        break;
    case 7:
        m_SkyTiltCalibrationStep = ReadBbVoltageSkyTilt4;
        setAntennaAngleAndNoiseSrc(m_SkyTiltAng4, false);
        setReadVoltageAngle();
        break;
    case 8:
        m_SkyTiltCalibrationStep = ReadBbVoltageSkyTilt5;
        setAntennaAngleAndNoiseSrc(m_SkyTiltAng5, false);
        setReadVoltageAngle();
        break;
    }
}

/**
 * @brief 槽函数，用于处理参数计算按钮的触发事件
 *
 * 当参数计算按钮被点击时，调用此槽函数来执行相应的操作。
 *
 * 具体操作包括调用 Settlement_fun() 函数进行参数计算。
 */
void ControlSetParameterDialog::slot_paramCalcButtonTriggered()
{
    Settlement_fun();
}

/**
 * @brief slot_changeAutoCalibrationMode 槽函数，用于更改自动校准模式
 *
 * 当自动校准模式改变时，根据下拉框当前选中的索引值，更新校准步骤和启动定时器。
 */
void ControlSetParameterDialog::slot_changeAutoCalibrationMode()
{
    if (ui->comboBox_dingbiao_mode->currentIndex() == 0)
    {
        if (m_AutoCalibPushButtonFlag == false)
        {
            m_AutoCalibPushButtonFlag = true;
            m_BbCalibrationStep = NoCail;
            m_Timer->start(1000);
            log_record("自动定标开始");
            ui->pushButton_autoCalib->setText("停止定标");
            slot_changeCalibrationMode();
            slot_changeAutoMode();
        }
        else
        {
            ui->pushButton_autoCalib->setText("自动定标");
            m_AutoCalibPushButtonFlag = false;
            m_BbCalibrationStep = NoCail;
            log_record("自动定标停止");
            m_Timer->stop();
        }
        
    }
}

/**
 * @brief 保存按钮触发槽函数
 *
 * 当用户点击保存按钮时，该函数会被触发。
 * 该函数将调用三个校准参数设置函数，分别用于设置校准参数C、系统温度Tsys和校准参数Alpha。
 */
void ControlSetParameterDialog::slot_saveButtonTriggered()
{
    Calibration_param_C_fun();
    Calibration_param_Tsys_fun();
    Calibration_param_Alpha_fun();
}

/**
 * @brief 槽函数，用于处理“清除”按钮的点击事件
 *
 * 当用户点击“清除”按钮时，会调用此槽函数。该函数会将成员变量m_LiqN2SrcReadFlag、
 * m_NoiseSrcReadFlag和m_SrcTempReadFlag的值都设置为false，并调用表更新函数table_update_fun，
 * 传入Value_Clear和NULL作为参数，以清除表格中的数据。
 */
void ControlSetParameterDialog::slot_clearButtonTriggered()
{
    m_LiqN2SrcReadFlag = false;
    m_NoiseSrcReadFlag = false;
    m_SrcTempReadFlag = false;
    table_update_fun(Value_Clear, NULL);
    ui->textEdit->clear();
    slot_changeCalibrationMode();
    slot_changeAutoMode();
}

/**
 * @brief 槽函数：当“读取校准参数”按钮被触发时调用的函数
 *
 * 该函数首先调用 Calibration_param_Read_fun() 函数读取校准参数，
 * 然后将读取的校准参数赋值给 m_calibCorrect 结构体中的相应成员变量，
 * 最后调用 table_update_fun() 函数更新表格显示。
 */
void ControlSetParameterDialog::slot_readCalibParamButtonTriggered()
{
    Calibration_param_Read_fun();
    for (int i = 0; i < 16; i++)
    {
        m_calibCorrect.C[i] = calibrationValue_value.gainCoef_C[i];
        m_calibCorrect.Tn[i] = calibrationValue_value.Alpha_param[i];
        m_calibCorrect.Tsys[i] = calibrationValue_value.sysBtNoi_Tsys[i];
    }
    table_update_fun(Value_C, m_calibCorrect.C);
    table_update_fun(Value_Tn, m_calibCorrect.Tn);
    table_update_fun(Value_Tsys, m_calibCorrect.Tsys);
}

/**
 * @brief 设置天线角度和噪声源开关
 *
 * 该函数用于设置天线角度和噪声源开关。
 *
 * @param antAng 天线角度，范围在0到360度之间
 * @param isOpen 噪声源开关，true表示打开，false表示关闭
 */
void ControlSetParameterDialog::setAntennaAngleAndNoiseSrc(float antAng, bool isOpen)
{
    m_AntTgtAng = antAng;
    m_NoiseSrcSw = isOpen;
    // 设置清空
    widgetComm->set_sendClear_fun();
    // 设置天线角度  value<360 value>=0;
    widgetComm->set_AntTgtAng_fun(antAng);
    // 设置噪声源开关
    widgetComm->set_NoiseSrcSw_fun(isOpen);
    // 设置发送
    widgetComm->set_sendsetting_fun();

    // 天线角度电机参数读取标志位
    widgetComm->set_AntAngParam_read_fun(true);
    // 噪声源读取标志位
    widgetComm->set_NoiseSrcSw_read_fun(true);

    connect(widgetComm, &classWidgetComm::signal_AntAng_data, this, &ControlSetParameterDialog::solt_setAntennaAngleAndNoiseSrc);
    connect(widgetComm, &classWidgetComm::signal_NoiseSrcSw_data, this, &ControlSetParameterDialog::solt_setAntennaAngleAndNoiseSrc);
}

/**
 * @brief 设置参数对话框以读取LN2情况下的BB电压
 *
 * 此函数用于设置参数对话框以读取LN2情况下的BB电压。
 * 它会将当前校准步骤设置为读取BB电压的步骤，并启用AGV电压读取功能。
 * 然后，它会将校准数据中的LN2源电压清零，并更新电压表。
 * 最后，它将信号与槽连接，以便在接收到RadioVolAverage数据时更新电压数据。
 */
void ControlSetParameterDialog::setReadBbVoltageLN2()
{
    m_BbCalibrationStep = ReadBbVoltageLN2;
    widgetComm->set_AgvVoltage_read(true);
    memset(m_calibData.lnSrcVolt, 0, sizeof(float) * 16);
    table_update_fun(Value_LN2_Voltage, m_calibData.lnSrcVolt);
    connect(widgetComm, &classWidgetComm::signal_RadioVolAverage_data, this, &ControlSetParameterDialog::slot_updateVoltageData);
}

/**
 * @brief 设置参数对话框的读取黑体电压NT模式
 *
 * 设置参数对话框进入读取黑体电压NT模式，进行相关配置和数据初始化。
 */
void ControlSetParameterDialog::setReadBbVoltageNT()
{
    m_BbCalibrationStep = ReadBbVoltageNT;
    widgetComm->set_AgvVoltage_read(true);
    widgetComm->set_fTempLowAverage_read_fun(true);
    widgetComm->set_fistTempBt_read_fun(true);
    memset(m_calibData.normTempSrcVolt, 0, sizeof(float) * 16);
    memset(m_calibData.normTempSrcTemp, 0, sizeof(float) * 16);
    table_update_fun(Value_AT_Voltage, m_calibData.normTempSrcVolt);
    table_update_fun(Value_AT_Temp, m_calibData.normTempSrcTemp);
    connect(widgetComm, &classWidgetComm::signal_RadioVolAverage_data, this, &ControlSetParameterDialog::slot_updateVoltageData);

    connect(widgetComm, &classWidgetComm::signal_fTemp_LowAverage_data, this, &ControlSetParameterDialog::slot_updateBrightTempAndAvgTemp);
    connect(widgetComm, &classWidgetComm::signal_fistTempBt_data, this, &ControlSetParameterDialog::slot_updateBrightTempAndAvgTemp);
}

/**
 * @brief 设置读取基带电压噪声源
 *
 * 此函数用于设置基带电压噪声源的读取参数，并更新相关界面元素。
 */
void ControlSetParameterDialog::setReadBbVoltageNoiseSrc()
{
    m_BbCalibrationStep = ReadBbVoltageNoiseSrc;
    widgetComm->set_AgvVoltage_read(true);
    memset(m_calibData.noiseSrcVolt, 0, sizeof(float) * 16);
    table_update_fun(Value_Noise_Voltage, m_calibData.noiseSrcVolt);
    connect(widgetComm, &classWidgetComm::signal_RadioVolAverage_data, this, &ControlSetParameterDialog::slot_updateVoltageData);
}

void ControlSetParameterDialog::setReadVoltageAngle()
{
    widgetComm->set_AgvVoltage_read(true);
    memset(m_calibData.SkyTiltAng5, 0, sizeof(float) * 16);
    switch (m_SkyTiltCalibrationStep)
    {
    case ReadBbVoltageSkyTilt1:
        table_update_fun(Value_Angle1, m_calibData.SkyTiltAng1);
        break;
    case ReadBbVoltageSkyTilt2:
        table_update_fun(Value_Angle2, m_calibData.SkyTiltAng2);
        break;
    case ReadBbVoltageSkyTilt3:
        table_update_fun(Value_Angle3, m_calibData.SkyTiltAng3);
        break;
    case ReadBbVoltageSkyTilt4:
        table_update_fun(Value_Angle4, m_calibData.SkyTiltAng4);
        break;
    case ReadBbVoltageSkyTilt5:
        table_update_fun(Value_Angle5, m_calibData.SkyTiltAng5);
        break;
    }
    connect(widgetComm, &classWidgetComm::signal_RadioVolAverage_data, this, &ControlSetParameterDialog::slot_updateVoltageData);
}

/**
 * @brief 更新表格项的函数
 *
 * 根据传入的列索引和值数组更新表格中的内容。
 *
 * @param columnIndex 列索引
 * @param value[] 值数组
 */
void ControlSetParameterDialog::table_update_fun(uchar columnIndex, float value[])
{
    QTableWidgetItem *item;
    switch (columnIndex)
    {
    case Value_Tn:
        for (int i = 0; i < 16; i++)
        {
            item = ui->tableWidget->item(i, Value_Tn);
            if (item == nullptr)
            {
                item = new QTableWidgetItem();
                ui->tableWidget->setItem(i, Value_Tn, item);
            }
            item->setText(QString::number(m_calibCorrect.Tn[i], 'f', 3));
        }
        break;
    case Value_C:
        for (int i = 0; i < 16; i++)
        {
            item = ui->tableWidget->item(i, Value_C);
            if (item == nullptr)
            {
                item = new QTableWidgetItem();
                ui->tableWidget->setItem(i, Value_C, item);
            }
            item->setText(QString::number(m_calibCorrect.C[i], 'f', 3));
        }
        break;
    case Value_Tsys:
        for (int i = 0; i < 16; i++)
        {
            item = ui->tableWidget->item(i, Value_Tsys);
            if (item == nullptr)
            {
                item = new QTableWidgetItem();
                ui->tableWidget->setItem(i, Value_Tsys, item);
            }
            item->setText(QString::number(m_calibCorrect.Tsys[i], 'f', 3));
        }
        break;
    case Value_LN2_Voltage:
        for (int i = 0; i < 16; i++)
        {
            item = ui->tableWidget->item(i, Value_LN2_Voltage);
            if (item == nullptr)
            {
                item = new QTableWidgetItem();
                ui->tableWidget->setItem(i, Value_LN2_Voltage, item);
            }
            item->setText(QString::number(m_calibData.lnSrcVolt[i], 'f', 3));
        }
        break;
    case Value_AT_Voltage:
        for (int i = 0; i < 16; i++)
        {
            item = ui->tableWidget->item(i, Value_AT_Voltage);
            if (item == nullptr)
            {
                item = new QTableWidgetItem();
                ui->tableWidget->setItem(i, Value_AT_Voltage, item);
            }
            item->setText(QString::number(m_calibData.normTempSrcVolt[i], 'f', 3));
        }
        break;
    case Value_AT_Temp:
        for (int i = 0; i < 16; i++)
        {
            item = ui->tableWidget->item(i, Value_AT_Temp);
            if (item == nullptr)
            {
                item = new QTableWidgetItem();
                ui->tableWidget->setItem(i, Value_AT_Temp, item);
            }
            item->setText(QString::number(m_calibData.normTempSrcTemp[i], 'f', 2));
        }
        break;
    case Value_Noise_Voltage:
        for (int i = 0; i < 16; i++)
        {
            item = ui->tableWidget->item(i, Value_Noise_Voltage);
            if (item == nullptr)
            {
                item = new QTableWidgetItem();
                ui->tableWidget->setItem(i, Value_Noise_Voltage, item);
            }
            item->setText(QString::number(m_calibData.noiseSrcVolt[i], 'f', 3));
        }
        break;

    case Value_Angle1:
        for (int i = 0; i < 16; i++)
        {
            item = ui->tableWidget->item(i, Value_Angle1);
            if (item == nullptr)
            {
                item = new QTableWidgetItem();
                ui->tableWidget->setItem(i, Value_Angle1, item);
            }
            item->setText(QString::number(m_calibData.SkyTiltAng1[i], 'f', 3));
        }
        break;
    case Value_Angle2:
        for (int i = 0; i < 16; i++)
        {
            item = ui->tableWidget->item(i, Value_Angle2);
            if (item == nullptr)
            {
                item = new QTableWidgetItem();
                ui->tableWidget->setItem(i, Value_Angle2, item);
            }
            item->setText(QString::number(m_calibData.SkyTiltAng2[i], 'f', 3));
        }
        break;
    case Value_Angle3:
        for (int i = 0; i < 16; i++)
        {
            item = ui->tableWidget->item(i, Value_Angle3);
            if (item == nullptr)
            {
                item = new QTableWidgetItem();
                ui->tableWidget->setItem(i, Value_Angle3, item);
            }
            item->setText(QString::number(m_calibData.SkyTiltAng3[i], 'f', 3));
        }
        break;
    case Value_Angle4:
        for (int i = 0; i < 16; i++)
        {
            item = ui->tableWidget->item(i, Value_Angle4);
            if (item == nullptr)
            {
                item = new QTableWidgetItem();
                ui->tableWidget->setItem(i, Value_Angle4, item);
            }
            item->setText(QString::number(m_calibData.SkyTiltAng4[i], 'f', 3));
        }
    case Value_Angle5:
        for (int i = 0; i < 16; i++)
        {
            item = ui->tableWidget->item(i, Value_Angle5);
            if (item == nullptr)
            {
                item = new QTableWidgetItem();
                ui->tableWidget->setItem(i, Value_Angle5, item);
            }
            item->setText(QString::number(m_calibData.SkyTiltAng5[i], 'f', 3));
        }
        break;
    case Value_Clear:
        for (int i = 0; i < 16; i++)
        {
            for (int j = 0; j < 12; j++)
            {
                item = ui->tableWidget->item(i, j);
                if (item == nullptr)
                {
                    item = new QTableWidgetItem();
                    ui->tableWidget->setItem(i, Value_Noise_Voltage, item);
                }
                item->setText("");
            }
        }

        break;
    }
    ui->tableWidget->update();
}

// 计算参数
/**
 * @brief ControlSetParameterDialog::Settlement_fun
 *
 * 更新参数对话框中的结算函数。
 *
 * 该函数首先调用Settlement_C_fun()进行结算处理，然后更新C参数的值到表格中，
 * 接着调用Settlement_Tsys_fun()进行结算处理，并更新Tsys参数的值到表格中，
 * 最后调用Settlement_Alpha_fun()进行结算处理，并更新Tn参数的值到表格中。
 * 更新完成后，将更新保存按钮设为可用状态。
 */
void ControlSetParameterDialog::Settlement_fun()
{
    Settlement_C_fun();
    table_update_fun(Value_C, m_calibCorrect.C);
    Settlement_Tsys_fun();
    table_update_fun(Value_Tsys, m_calibCorrect.Tsys);
    Settlement_Alpha_fun();
    table_update_fun(Value_Tn, m_calibCorrect.Tn);
    ui->pushButton_updateSave->setEnabled(true);
}
// 计算参数C
/**
 * @brief 设置校准参数对话框的校准系数 C 的计算函数
 *
 * 该函数用于计算校准参数对话框中的校准系数 C。
 *
 * 通过遍历 0 到 15 的索引，计算每个索引对应的校准系数 C，
 * 并将其存储在 m_calibCorrect.C 数组中。同时，将计算得到的校准系数
 * 存储在 calibrationValue_value.gainCoef_C 数组中。
 *
 * 校准系数 C 的计算公式为：
 * C[i] = (m_calibData.normTempSrcVolt[i] - m_calibData.lnSrcVolt[i]) / (m_calibData.normTempSrcTemp[i] - m_ln2Temp)
 *
 */
void ControlSetParameterDialog::Settlement_C_fun()
{
    for (int i = 0; i < 16; i++)
    {
        m_calibCorrect.C[i] = (m_calibData.normTempSrcVolt[i] - m_calibData.lnSrcVolt[i]) / (m_calibData.normTempSrcTemp[i] - m_ln2Temp);
        calibrationValue_value.gainCoef_C[i] = m_calibCorrect.C[i];
    }
}
// 计算参数Tsys
/**
 * @brief 设置系统温度参数
 *
 * 该函数用于根据校准数据和校准校正系数，计算系统温度参数（Tsys），
 * 并将其保存到校准值结构体中。
 */
void ControlSetParameterDialog::Settlement_Tsys_fun()
{
    for (int i = 0; i < 16; i++)
    {
        m_calibCorrect.Tsys[i] = m_calibData.normTempSrcVolt[i] / m_calibCorrect.C[i] - m_calibData.normTempSrcTemp[i];
        calibrationValue_value.sysBtNoi_Tsys[i] = m_calibCorrect.Tsys[i];
    }
}
// 计算参数Alpha
/**
 * @brief 设置校准参数对话框中的Alpha参数
 *
 * 该函数用于计算并设置校准参数对话框中的Alpha参数。
 *
 * 通过遍历0到15的索引，计算每个索引对应的校准参数，并将这些参数存储在成员变量m_calibCorrect的Tn数组中。
 * 然后，将这些计算出的参数赋值给calibrationValue_value的Alpha_param数组。
 */
void ControlSetParameterDialog::Settlement_Alpha_fun()
{
    for (int i = 0; i < 16; i++)
    {
        m_calibCorrect.Tn[i] = (m_calibData.noiseSrcVolt[i] / m_calibCorrect.C[i] - m_calibCorrect.Tsys[i]) - (m_calibData.normTempSrcVolt[i] / m_calibCorrect.C[i] - m_calibCorrect.Tsys[i]);
        calibrationValue_value.Alpha_param[i] = m_calibCorrect.Tn[i];
    }
}

// 定标参数C保存
/**
 * @brief Calibration_param_C_fun 函数
 *
 * 该函数用于将校准参数写入到指定的INI配置文件中。
 *
 * @note 创建QSettings对象，指定INI文件路径和格式，将校准参数写入到配置文件中，并同步写入到文件。
 */
void ControlSetParameterDialog::Calibration_param_C_fun()
{
    // 创建QSettings对象，指定INI文件路径和格式
    QSettings settings("Dataconfig/saveCalibrationConfig.ini", QSettings::IniFormat);
    // 写入配置
    for (int i = 0; i < 16; i++)
    {
        settings.setValue(QString("PramaC/CH%1").arg(i + 1), calibrationValue_value.gainCoef_C[i]);
    }
    // 同步写入到文件
    settings.sync();
}

// 定标参数Tsys保存
/**
 * @brief 校准参数函数
 *
 * 此函数负责将校准参数保存到指定的INI配置文件中。
 */
void ControlSetParameterDialog::Calibration_param_Tsys_fun()
{
    // 创建QSettings对象，指定INI文件路径和格式
    QSettings settings("Dataconfig/saveCalibrationConfig.ini", QSettings::IniFormat);
    // 写入配置
    for (int i = 0; i < 16; i++)
    {
        settings.setValue(QString("PramaTsys/CH%1").arg(i + 1), calibrationValue_value.Alpha_param[i]);
    }
    // 同步写入到文件
    settings.sync();
}

// 定标参数Alpha保存
/**
 * @brief 控制设置参数对话框中的校准参数Alpha函数
 *
 * 该函数用于将校准参数Alpha的值写入到指定的INI配置文件中。
 *
 * @details
 * 1. 创建QSettings对象，并指定INI文件的路径和格式为"Dataconfig/saveCalibrationConfig.ini"。
 * 2. 使用循环遍历calibrationValue_value.sysBtNoi_Tsys数组，将每个元素的值写入到INI文件中。
 *   每个元素对应的键为"PramaTn/CH%1"，其中%1会被替换为当前元素的索引加1（从1开始）。
 * 3. 使用settings.sync()方法将写入的内容同步到文件中。
 */
void ControlSetParameterDialog::Calibration_param_Alpha_fun()
{
    // 创建QSettings对象，指定INI文件路径和格式
    QSettings settings("Dataconfig/saveCalibrationConfig.ini", QSettings::IniFormat);
    // 写入配置
    for (int i = 0; i < 16; i++)
    {
        settings.setValue(QString("PramaTn/CH%1").arg(i + 1), calibrationValue_value.sysBtNoi_Tsys[i]);
    }
    // 同步写入到文件
    settings.sync();
}

// 读取定标参数
/**
 * @brief 读取校准参数
 *
 * 该函数负责从指定的INI文件中读取校准参数，并将这些参数保存到类中相应的成员变量中。
 */
void ControlSetParameterDialog::Calibration_param_Read_fun()
{
    // 创建QSettings对象，指定INI文件路径和格式
    QSettings settings("Dataconfig/saveCalibrationConfig.ini", QSettings::IniFormat);
    // 写入配置
    for (int i = 0; i < 16; i++)
    {
        calibrationValue_value.gainCoef_C[i] = settings.value(QString("PramaC/CH%1").arg(i + 1)).toFloat();
        calibrationValue_value.Alpha_param[i] = settings.value(QString("PramaTsys/CH%1").arg(i + 1)).toFloat();
        calibrationValue_value.sysBtNoi_Tsys[i] = settings.value(QString("PramaTn/CH%1").arg(i + 1)).toFloat();
    }
}

void ControlSetParameterDialog::log_record(QString log)
{
    QTextCursor cursor(ui->textEdit->document());

    // 如果行数 > 300，删除第一行
    if (ui->textEdit->document()->blockCount() > 300) {
        cursor.movePosition(QTextCursor::Start);           // 移动到文档开头
        cursor.select(QTextCursor::LineUnderCursor);       // 选择第一行
        cursor.removeSelectedText();                       // 删除该行
        cursor.deleteChar();                               // 删除可能存在的换行符
    }

    // 添加新日志
    cursor.movePosition(QTextCursor::End);                 // 移动到末尾
    cursor.insertText(QDateTime::currentDateTime().toString("[yyyy-MM-dd hh:mm:ss.zzz]")+ log + "\n");
}
