﻿#ifndef LV2_DATA_STORAGE_H
#define LV2_DATA_STORAGE_H
#include <tools/data_treating/cpublic_struct.h>
#include <QObject>
#include <QDir>
#include <cmath>
#include <QDateTime>
#include <QTextStream>
#include <QVector>

struct Lv2_Meteorological_product_data
{

    QString Record;   // 含义：记录序号              内容：记录序号
    QString DateTime; // 含义：记录日期及时间         内容：yyyy-mm-dd  hh:mm:ss，规则详见表5
    int DataType;     // 含义：数据行类型码           内容：11及以上（代表多种廓线气象产品,详见表9）
    double SurTem;    // 含义：地面温度              内容：具体观测结果，单位为℃，保留2位小数
    double SurHum;    // 含义：地面湿度              内容：具体观测结果，单位为%RH，保留2位小数
    double SurPre;    // 含义：地面气压              内容：具体观测结果，单位为hPa，保留2位小数
    double Tir;       // 含义：红外温度              内容：具体观测结果，单位为℃，保留2位小数
    int Rain;         // 含义：是否降水              内容：具体观测结果，是否降水（1＝是，0＝否）
    double CloudBase; // 含义：云底高度              内容：具体观测结果，单位为km，保留2位小数
    double Vint;      // 含义：积分水汽              内容：具体观测结果，单位为mm，保留2位小数
    double Lqint;     // 含义：积分云液水            内容：具体观测结果，单位为mm，保留2位小数
    double H[83];     // 含义：第n层结数据           内容：具体观测结果
    int QCflag;       // 含义：质控码
};

class Lv2_data_storage : public QObject
{
    Q_OBJECT
public:
    explicit Lv2_data_storage(QString file_path, struct FILENAME file_name, QObject *parent = nullptr);

    // 日文件
    bool Generate_new_Lv2_Meteorological_product_data(struct Lv_Basic_parameters basic_parameters, QVector<struct Lv2_Meteorological_product_data> Meteorological_product);
    // 分钟文件
    bool Generate_new_Lv2_Minutes_file(struct Lv_Basic_parameters basic_parameters, QVector<struct Lv2_Meteorological_product_data> Meteorological_product);

    void set_file_path(QString path);         // 设置文件路径
    void set_file_name(struct FILENAME name); // 设置文件名
    void set_Minutes_file_en(bool en);        // 设置分钟文件存储使能
    void set_Day_file_en(bool en);            // 设置日文件存储使能
    void set_beijing_or_utc(bool choose);     // 设置文件路径及文件名中的时间时使用北京时还是UTC时间  true beijing false utc

    struct FILENAME filename; // 文件名称信息结构体
    QString cur_day_filename; // 当前日文件名称
    QString filepath;         // 文件保存路径
    QFile *file;              // 记录日文件

signals:
public slots:
    void save_data(struct Lv_Basic_parameters basic_parameters, QVector<struct Lv2_Meteorological_product_data> Meteorological_product);

private:
    bool Insert_new_Lv2_Meteorological_product_data(QVector<struct Lv2_Meteorological_product_data> Meteorological_product); // 在日文件中插入一条新的亮温数据
    QString Generate_file_name(struct FILENAME file_name);                                                                   // 生成文件名
    bool isPureAscii(const QString &str);                                                                                    // 判断字符串是否是ASCII码
    bool Insert_new_Lv2_Basic_parameters(struct Lv_Basic_parameters basic_parameters);                                       // 在日文件中插入一条基础数据
    QString Control_decimal_bit(double data, int bit, bool status);                                                          // 控制小数位
    QString deal_int_data(int data);
    QString deal_qstring_data(QString data);
    /*在田间日文件时判断本地路径下是否已经存在日文件，不考虑时分秒，但要验证其合法性*/
    bool comparefilenames(const QString &filename1, const QString &filename2);                                      // 对比文件名
    bool processFileName(const QString &filename, bool &timevalid, QString &processedbasename, QString &extension); // 处理并验证文件名，生成忽略时分秒后的新的文件名。
    // int ch = 14;
    int Record_number = 1; // 记录日文件的行号

    bool Minutes_file_en = false; // 是否保存为分钟文件
    bool Day_file_en = false;     // 是否保存为日文件
    bool use_beijing_utc = true;  // 北京时和utc时的标志位
};

#endif // Lv2_data_storage_H
