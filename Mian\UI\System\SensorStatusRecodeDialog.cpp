#include "SensorStatusRecodeDialog.h"
#include "ui_SensorStatusRecodeDialog.h"
#include "tools/Global.h"
#include <QCalendarWidget>
SensorStatusRecodeDialog::SensorStatusRecodeDialog(QWidget* parent) :
    QDialog(parent),
    ui(new Ui::SensorStatusRecodeDialog)
{
    ui->setupUi(this);
    ui->dateTimeEdit_start_time->setCalendarPopup(true);
    ui->dateTimeEdit_start_time->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_start_time->setDateTime(QDateTime::currentDateTime());

    ui->dateTimeEdit_stop_time->setCalendarPopup(true);
    ui->dateTimeEdit_stop_time->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    ui->dateTimeEdit_stop_time->setDateTime(QDateTime::currentDateTime());

    SensorStatusRecord_data = new  sensor_status_record("SqliteData/");
    SensorStatusRecord_data->use_CustomSqlTableModel(ui->tableView_sensor_record);
    auto_delect_data = new QTimer();
    connect(auto_delect_data, SIGNAL(timeout()), this, SLOT(slot_auto_delect_data()));

    auto_delect_data->start(1000);
}

SensorStatusRecodeDialog::~SensorStatusRecodeDialog()
{
    delete ui;
}

void SensorStatusRecodeDialog::on_pushButton_query_clicked()
{
    QDateTime DateStr1 = ui->dateTimeEdit_start_time->dateTime();
    QString timeStr1 = DateStr1.toString("yyyy-MM-dd HH:mm:ss");
    QDateTime DateStr2 = ui->dateTimeEdit_stop_time->dateTime();
    QString timeStr2 = DateStr2.toString("yyyy-MM-dd HH:mm:ss");
    SensorStatusRecord_data->select_usetime_SqlTableModel(timeStr1, timeStr2);
}

void SensorStatusRecodeDialog::slot_auto_delect_data()
{
    //删除所有早于当前时间之前的子目录
    QDateTime month_file = QDateTime::currentDateTime().addDays(-(systemconfigfdata->systemdata.sensor_status_day));

    SensorStatusRecord_data->deleteOldsql(month_file.toString("yyyy-MM-dd HH:mm:ss"));
}
