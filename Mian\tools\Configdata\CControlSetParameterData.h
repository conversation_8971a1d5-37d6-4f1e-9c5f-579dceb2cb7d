﻿#ifndef CCONTROLSETPARAMETERDATA_H
#define CCONTROLSETPARAMETERDATA_H

#include <QObject>
#include <QString>
#include <QFile>
#include <QSettings>
struct ControlSetParameData
{
    // 开关机参数
    int open_close_down;    // 开关机
    int close_down_process; // 关机步骤
    // 工作模式参数
    int currence_woke_mode;
    int return_woke_mode;
    // 系统参数设置
    double sampling_time;
    double sampling_rate;
    double black_num;
    double maintain_time;

    double return_mark;
    double return_sampling_time;
    double return_sampling_rate;
    double return_black_num;
    double return_maintain_time;
    // 设备运行时间
    QString sum_time;
    QString open_time;
    // 设备定标
    int dingbiao_type;
    double dingbiao_order;
    double jifen_time;
    double period;
    double return_dingbiao_type;
    double dingbiao_status;
    QString dingbiao_process;
    QString currence_jifen_time;
    QString currence_period;
    // 设置定标参数
    double set_accept_gain1;
    double set_accept_gain2;
    double set_accept_gain3;
    double set_accept_gain4;
    double set_accept_gain5;
    double set_accept_gain6;
    double set_accept_gain7;
    double set_accept_gain8;
    double set_accept_gain9;
    double set_accept_gain10;
    double set_accept_gain11;
    double set_accept_gain12;
    double set_accept_gain13;
    double set_accept_gain14;

    double set_diode1;
    double set_diode2;
    double set_diode3;
    double set_diode4;
    double set_diode5;
    double set_diode6;
    double set_diode7;
    double set_diode8;
    double set_diode9;
    double set_diode10;
    double set_diode11;
    double set_diode12;
    double set_diode13;
    double set_diode14;

    double set_noise_temp1;
    double set_noise_temp2;
    double set_noise_temp3;
    double set_noise_temp4;
    double set_noise_temp5;
    double set_noise_temp6;
    double set_noise_temp7;
    double set_noise_temp8;
    double set_noise_temp9;
    double set_noise_temp10;
    double set_noise_temp11;
    double set_noise_temp12;
    double set_noise_temp13;
    double set_noise_temp14;

    double set_line_paraeter1;
    double set_line_paraeter2;
    double set_line_paraeter3;
    double set_line_paraeter4;
    double set_line_paraeter5;
    double set_line_paraeter6;
    double set_line_paraeter7;
    double set_line_paraeter8;
    double set_line_paraeter9;
    double set_line_paraeter10;
    double set_line_paraeter11;
    double set_line_paraeter12;
    double set_line_paraeter13;
    double set_line_paraeter14;

    int dingbiao_return;

    // 定标参数查询
    double query_accept_gain1;
    double query_accept_gain2;
    double query_accept_gain3;
    double query_accept_gain4;
    double query_accept_gain5;
    double query_accept_gain6;
    double query_accept_gain7;
    double query_accept_gain8;
    double query_accept_gain9;
    double query_accept_gain10;
    double query_accept_gain11;
    double query_accept_gain12;
    double query_accept_gain13;
    double query_accept_gain14;

    double query_diode1;
    double query_diode2;
    double query_diode3;
    double query_diode4;
    double query_diode5;
    double query_diode6;
    double query_diode7;
    double query_diode8;
    double query_diode9;
    double query_diode10;
    double query_diode11;
    double query_diode12;
    double query_diode13;
    double query_diode14;

    double query_noise_temp1;
    double query_noise_temp2;
    double query_noise_temp3;
    double query_noise_temp4;
    double query_noise_temp5;
    double query_noise_temp6;
    double query_noise_temp7;
    double query_noise_temp8;
    double query_noise_temp9;
    double query_noise_temp10;
    double query_noise_temp11;
    double query_noise_temp12;
    double query_noise_temp13;
    double query_noise_temp14;

    double query_line_paraeter1;
    double query_line_paraeter2;
    double query_line_paraeter3;
    double query_line_paraeter4;
    double query_line_paraeter5;
    double query_line_paraeter6;
    double query_line_paraeter7;
    double query_line_paraeter8;
    double query_line_paraeter9;
    double query_line_paraeter10;
    double query_line_paraeter11;
    double query_line_paraeter12;
    double query_line_paraeter13;
    double query_line_paraeter14;

    // 系统标定
    int dingbiao_mode;
    double yedan_angle;
    double normal_temp_angle;
    double noise_angle;
    double yedan_temp;
    double look_time;
    int currence_tianxian_angle;
    int system_dingbiao_angle;
};
class CControlSetParameterData : public QObject
{
    Q_OBJECT
public:
    explicit CControlSetParameterData(QObject *parent = nullptr);
    ~CControlSetParameterData();
    void saveToFile();   // 保存数据到文件中
    void loadFromFile(); // 加载文件中数据
    ControlSetParameData parameterdata;

    void creat_configfile();

signals:

public slots:
};

#endif // CCONTROLSETPARAMETERDATA_H
