﻿#include "Global.h"
#include <QMessageBox>
#include <QFont>
#include <QCheckBox>
#include <QCalendarWidget>
#include <QLineEdit>
#include "BrightCalcClass.h"
CControlSetParameterData controlParameterdata;
CSystemConfigData* systemconfigfdata = nullptr;
CFMGParameterData fmgparameterdata;

QVector<double> timedataContainer_;                       // 时间数据（秒）
QVector<double> heightdataContainer_;                     // 高度数据
QVector<QVector<double>> tempdataContainer_;              // 温度数据（二维数组）
sqlite_USER_data *USER_data;                              // 创建用户管理表
sqlite_FaultRecord_data *FaultRecord_data;                // 创建故障记录表
sqlite_FtpUploadRecord_data *FtpUploadRecord_data;        // 创建FTP上传记录表
sqlite_SystemMaintenance_data *SystemMaintenance_data;    // 创建系统维护日志记录表
txt_staterecord_data *StateRecord_data_txt;               // 日志记录
txt_routinemaintenance_data *RoutineMaintenance_data_txt; // 日常维护记录

LogDialog* m_log;

sensor_status_record* SensorStatusRecord_data;

classWidgetComm *widgetComm; // 通讯连接对象

Lv1_data_storage* lv1_data_storage;
Lv2_data_storage* lv2_data_storage;

Device_status_data_storage* device_status_data_storage;
Calibrate_data_storage* calibrate_data_storage;

QColor color_dead_error;
QColor color_general_information;
QColor color_general_error;
QColor color_debug_information;
QColor color_warn_information;

QString str_dead_error;
QString str_general_information;
QString str_general_error;
QString str_debug_information;
QString str_warn_information;

QString log_currentFilePath;

int index_seconds;//时间自动刷新
bool  Login_validation = false;
QString  g_ftp_name;
QString  g_ftp_ip;
QString  g_ftp_port;
QString  g_download_catalogue;


struct BTempParmConf_Struct BTempParmConf_value;
struct calibSettings_Struct calibSettings_value;
struct calibrationValue_Struct calibrationValue_value;

float BrightTemp_value[16];



Global::Global()
{

}
Global::~Global()
{
}
void Global::open_qmessage(QString title, QString message, QString sure)
{
    QMessageBox msgbox;
    msgbox.setWindowTitle(title);
    msgbox.setText(message);
    if (title == "警告")
    {
        msgbox.setIcon(QMessageBox::Warning);
    }
    else
    {
        msgbox.setIcon(QMessageBox::Question);
    }
    msgbox.addButton(sure, QMessageBox::ActionRole);
    msgbox.exec();
}

void Global::Container_removal(QVector<ushort>* vect)
{
    std::sort(vect->begin(), vect->end());
    auto unitime = std::unique(vect->begin(), vect->end());
    vect->erase(unitime, vect->end());
}

void Global::set_FMG_tableWidget(QTableWidget *tab)
{
    QCheckBox *checkbox = new QCheckBox();
    QCheckBox *checkbox1 = new QCheckBox();
    QCheckBox *checkbox2 = new QCheckBox();
    QCheckBox *checkbox3 = new QCheckBox();
    QCheckBox *checkbox4 = new QCheckBox();
    QCheckBox *checkbox5 = new QCheckBox();
    QCheckBox *checkbox6 = new QCheckBox();
    QCheckBox *checkbox7 = new QCheckBox();
    QCheckBox *checkbox8 = new QCheckBox();
    QCheckBox *checkbox9 = new QCheckBox();
    QCheckBox *checkbox10 = new QCheckBox();
    QCheckBox *checkbox11 = new QCheckBox();
    QCheckBox *checkbox12 = new QCheckBox();
    QCheckBox *checkbox13 = new QCheckBox();
    QCheckBox *checkbox14 = new QCheckBox();
    QCheckBox *checkbox15 = new QCheckBox();
    QCheckBox *checkbox16 = new QCheckBox();
    QCheckBox *checkbox17 = new QCheckBox();
    QCheckBox *checkbox18 = new QCheckBox();
    QCheckBox *checkbox19 = new QCheckBox();
    QCheckBox *checkbox20 = new QCheckBox();
    QCheckBox *checkbox21 = new QCheckBox();
    QCheckBox *checkbox22 = new QCheckBox();

    //    QTableWidgetItem *checkboxitem = new QTableWidgetItem();
    //    checkboxitem->setFlags(Qt::ItemIsUserCheckable | Qt::ItemIsEnabled);
    //    ui->tableWidget->setItem(0,0,checkboxitem);
    checkbox->setText("采集卡滤波系数");
    checkbox1->setText("接收机温度1");
    checkbox2->setText("接收机温度2");
    checkbox3->setText("接收机温度3");
    checkbox4->setText("接收机温度4");
    checkbox5->setText("接收机温度5");
    checkbox6->setText("接收机温度6");
    checkbox7->setText("接收机电压均值1");
    checkbox8->setText("接收机电压均值2");
    checkbox9->setText("接收机电压均值3");
    checkbox10->setText("接收机电压均值4");
    checkbox11->setText("接收机电压均值5");
    checkbox12->setText("接收机电压均值6");
    checkbox13->setText("接收机电压均值7");
    checkbox14->setText("接收机电压均值8");
    checkbox15->setText("接收机电压均值9");
    checkbox16->setText("接收机电压均值10");
    checkbox17->setText("接收机电压均值11");
    checkbox18->setText("接收机电压均值12");
    checkbox19->setText("接收机电压均值13");
    checkbox20->setText("接收机电压均值14");
    checkbox21->setText("接收机电压均值15");
    checkbox22->setText("接收机电压均值16");

    tab->setCellWidget(0, 0, checkbox);
    tab->setCellWidget(1, 0, checkbox1);
    tab->setCellWidget(2, 0, checkbox2);
    tab->setCellWidget(3, 0, checkbox3);
    tab->setCellWidget(4, 0, checkbox4);
    tab->setCellWidget(5, 0, checkbox5);
    tab->setCellWidget(6, 0, checkbox6);
    tab->setCellWidget(7, 0, checkbox7);
    tab->setCellWidget(8, 0, checkbox8);
    tab->setCellWidget(9, 0, checkbox9);
    tab->setCellWidget(10, 0, checkbox10);
    tab->setCellWidget(11, 0, checkbox11);
    tab->setCellWidget(12, 0, checkbox12);
    tab->setCellWidget(13, 0, checkbox13);
    tab->setCellWidget(14, 0, checkbox14);
    tab->setCellWidget(15, 0, checkbox15);
    tab->setCellWidget(16, 0, checkbox16);
    tab->setCellWidget(17, 0, checkbox17);
    tab->setCellWidget(18, 0, checkbox18);
    tab->setCellWidget(19, 0, checkbox19);
    tab->setCellWidget(20, 0, checkbox20);
    tab->setCellWidget(21, 0, checkbox21);
    tab->setCellWidget(22, 0, checkbox22);
}
void Global::set_alendarWidget_time(QLineEdit* lineedit)
{
    QDialog dialog;
    dialog.setWindowTitle("日历");
    QCalendarWidget* calendarWidget = new QCalendarWidget(&dialog);

    QVBoxLayout* mainLayout = new QVBoxLayout(&dialog); // 垂直布局

    mainLayout->addWidget(calendarWidget);

    QObject::connect(calendarWidget, &QCalendarWidget::selectionChanged, &dialog, [&]()
        {
            QDate selectDate = calendarWidget->selectedDate();
            lineedit->setText(selectDate.toString("yyyy-MM-dd") + " " + "00:00:00"); });

    // 操作按钮
    QHBoxLayout* buttonLayout = new QHBoxLayout;
    QPushButton* cancelBtn = new QPushButton("取消", &dialog);
    QPushButton* confirmBtn = new QPushButton("确定", &dialog);
    buttonLayout->addWidget(cancelBtn);
    buttonLayout->addWidget(confirmBtn);
    mainLayout->addLayout(buttonLayout);

    // 信号连接
    QObject::connect(cancelBtn, &QPushButton::clicked, &dialog, &QDialog::reject);

    QObject::connect(confirmBtn, &QPushButton::clicked, &dialog, [&]()
        { dialog.accept(); });

    dialog.exec();
}
void Global::saveToFile()
{
    QSettings settings("Dataconfig/CFMGParameterData.ini", QSettings::IniFormat);
    // 参数设置中亮温设置结构体
    settings.setValue("FMGParatemerData/brightTempFilCoef", BTempParmConf_value.brightTempFilCoef);
    settings.setValue("FMGParatemerData/constTempSrcAng", BTempParmConf_value.constTempSrcAng);
    settings.setValue("FMGParatemerData/noiseSrcAngle", BTempParmConf_value.noiseSrcAngle);
    settings.setValue("FMGParatemerData/skyAngle", BTempParmConf_value.skyAngle);
    settings.setValue("FMGParatemerData/stayDuration", BTempParmConf_value.stayDuration);
    settings.setValue("FMGParatemerData/intervalDur", BTempParmConf_value.intervalDur);
    settings.setValue("FMGParatemerData/innerCalibrMode", BTempParmConf_value.innerCalibrMode);
    // 定标设置中结构体
    settings.setValue("FMGParatemerData/LNsrcAng", calibSettings_value.LNsrcAng);
    settings.setValue("FMGParatemerData/ATsrcAng", calibSettings_value.ATsrcAng);
    settings.setValue("FMGParatemerData/noiSrcAng", calibSettings_value.noiSrcAng);
    settings.setValue("FMGParatemerData/nitroSrcTemp", calibSettings_value.nitroSrcTemp);
    settings.setValue("FMGParatemerData/skyTilt1", calibSettings_value.skyTilt1);
    settings.setValue("FMGParatemerData/skyTilt2", calibSettings_value.skyTilt2);
    settings.setValue("FMGParatemerData/skyTilt3", calibSettings_value.skyTilt3);
    settings.setValue("FMGParatemerData/skyTilt4", calibSettings_value.skyTilt4);
    settings.setValue("FMGParatemerData/skyTilt4", calibSettings_value.stayDuration);
    //定标参数结构体
   /* for (int i = 0; i < 17; i++)
    {
        settings.setValue("FMGParatemerData/brightTempFilCoef", calibrationValue_value.lnSrcVolt[i]);
        settings.setValue("FMGParatemerData/constTempSrcAng", calibrationValue_value.normTempSrcVolt[i]);
        settings.setValue("FMGParatemerData/noiseSrcAngle", calibrationValue_value.normTempSrcTemp[i]);
        settings.setValue("FMGParatemerData/skyAngle", calibrationValue_value.noiseSrcVolt[i]);
        settings.setValue("FMGParatemerData/stayDuration", calibrationValue_value.gainCoef_C[i]);
        settings.setValue("FMGParatemerData/intervalDur", calibrationValue_value.sysBtNoi_Tsys[i]);
        settings.setValue("FMGParatemerData/innerCalibrMode", calibrationValue_value.Alpha_param[i]);
    }*/
   
}
void Global::loadFromFile()
{
    QString filePath = "F:/QtMicrowaveRadiation/ControlSetParameData.ini";
    QSettings settings("Dataconfig/CFMGParameterData.ini", QSettings::IniFormat);
    // 参数设置中亮温设置结构体
    BTempParmConf_value.brightTempFilCoef = settings.value("FMGParatemerData/brightTempFilCoef").toFloat();
    BTempParmConf_value.constTempSrcAng = settings.value("FMGParatemerData/constTempSrcAng").toFloat();
    BTempParmConf_value.noiseSrcAngle = settings.value("FMGParatemerData/noiseSrcAngle").toFloat();
    BTempParmConf_value.skyAngle = settings.value("FMGParatemerData/skyAngle").toFloat();
    BTempParmConf_value.stayDuration = settings.value("FMGParatemerData/stayDuration").toFloat();
    BTempParmConf_value.intervalDur = settings.value("FMGParatemerData/intervalDur").toFloat();
    BTempParmConf_value.innerCalibrMode = settings.value("FMGParatemerData/innerCalibrMode").toFloat();


    // 定标设置中结构体
    calibSettings_value.LNsrcAng = settings.value("FMGParatemerData/LNsrcAng").toFloat();
    calibSettings_value.ATsrcAng = settings.value("FMGParatemerData/ATsrcAng").toFloat();
    calibSettings_value.noiSrcAng = settings.value("FMGParatemerData/noiSrcAng").toFloat();
    calibSettings_value.nitroSrcTemp = settings.value("FMGParatemerData/nitroSrcTemp").toFloat();
    calibSettings_value.skyTilt1 = settings.value("FMGParatemerData/skyTilt1").toFloat();
    calibSettings_value.skyTilt2 = settings.value("FMGParatemerData/skyTilt2").toFloat();
    calibSettings_value.skyTilt3 = settings.value("FMGParatemerData/skyTilt3").toFloat();
    calibSettings_value.skyTilt4 = settings.value("FMGParatemerData/skyTilt4").toFloat();
    calibSettings_value.stayDuration = settings.value("FMGParatemerData/stayDuration").toFloat();
    //定标参数结构体
   /*for (int i = 0; i < 17; i++)
    {
        calibrationValue_value.lnSrcVolt[i] = settings.value("FMGParatemerData/LNsrcAng").toFloat();
        calibrationValue_value.normTempSrcVolt[i] = settings.value("FMGParatemerData/ATsrcAng").toFloat();
        calibrationValue_value.normTempSrcTemp[i] = settings.value("FMGParatemerData/noiSrcAng").toFloat();
        calibrationValue_value.noiseSrcVolt[i] = settings.value("FMGParatemerData/nitroSrcTemp").toFloat();
        calibrationValue_value.gainCoef_C[i] = settings.value("FMGParatemerData/skyTilt1").toFloat();
        calibrationValue_value.sysBtNoi_Tsys[i] = settings.value("FMGParatemerData/skyTilt2").toFloat();
        calibrationValue_value.Alpha_param[i] = settings.value("FMGParatemerData/skyTilt3").toFloat();

    }*/
}

void Global::creat_configfile()
{
    QDir currentDir = QDir::current();
    // 创建目录（相对路径）
    QString dirpath = "Dataconfig";
    if (!currentDir.exists(dirpath))
    {
        if (!currentDir.mkdir(dirpath))
        {
            qDebug() << "创建目录失败";
            return;
        }
        qDebug() << "创建目录成功";
    }
    // 构建文件路径（确保目录存在后在操作）
    QFileInfo fileInfo(currentDir, "Dataconfig/CFMGParameterData.ini");
    // QString filePath = currentDir.filePath("Dataconfig/CFMGParameterData.ini");
    QString filePath = fileInfo.absoluteFilePath();
    // 检查文件失败
    if (fileInfo.exists())
    {
        qDebug() << "文件已存在，无需再创建";
        return;
    }
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly))
    {
        file.close();
        qDebug() << "创建成功";
    }
    else
    {
        qDebug() << "创建失败";
    }
}
