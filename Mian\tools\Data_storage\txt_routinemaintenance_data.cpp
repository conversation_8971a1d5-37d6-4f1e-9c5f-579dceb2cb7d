﻿#include "tools/Data_storage/txt_routinemaintenance_data.h"

txt_routinemaintenance_data::txt_routinemaintenance_data(QObject *parent) : QObject(parent)
{
}

txt_routinemaintenance_data::~txt_routinemaintenance_data()
{
    QObject::disconnect(coon);
}

void txt_routinemaintenance_data::use_QStandardItemModel(QTreeView *view, QString filepath)
{
    if (StandardItemModel_status)
        return;
    currentFolder = filepath;
    // 创建目录结构
    QDir dir(filepath);
    if (!dir.exists())
        dir.mkpath(filepath);

    /*---------- 文件树视图 ----------*/
    StandardItemModel = new QStandardItemModel();
    treeview = view;
    StandardItemModel->setHorizontalHeaderLabels({"文件名", "维护时间"});
    treeview->setModel(StandardItemModel);
    treeview->setEditTriggers(QAbstractItemView::NoEditTriggers);
    coon = connect(treeview, &QTreeView::doubleClicked, this, &txt_routinemaintenance_data::openFile);

    view->header()->setSectionResizeMode(QHeaderView::Stretch);

    reloadFiles();
}

bool txt_routinemaintenance_data::save_RoutineMaintenance_dataTotxt(RoutineMaintenance_SQ_GROUP data, QString time_type, const QStringList &text, const QVector<QString> &title)
{
    if (data.content.isEmpty() || data.personnel.isEmpty() || data.type.isEmpty() || data.u_time.isEmpty())
    {
        return false;
    }
    if (text.isEmpty() && data.title.isEmpty())
    {
        return false;
    }

    QString timestamp = "";
    if (time_type == "UTC")
    {
        timestamp = QDateTime::currentDateTimeUtc().toString("yyyyMMddHHmmss");
    }
    else if (time_type == "beijing")
    {
        timestamp = QDateTime::currentDateTime().toString("yyyyMMddHHmmss");
    }
    else
    {
        if (!QDateTime::fromString(time_type, "yyyy-MM-dd HH:mm:ss").isValid())
        {
            return false;
        }
        QDateTime temp = QDateTime::fromString(time_type, "yyyy-MM-dd HH:mm:ss");
        timestamp = temp.toString("yyyyMMddHHmmss");
    }
    QString fileName = QString("日常维护_%1.txt").arg(timestamp);
    QString filePath = QDir(currentFolder).filePath(fileName);

    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        reloadFiles();
    }
    else
    {
        QMessageBox::critical(nullptr, "错误", QString("创建失败:\n%1").arg(file.errorString()));
    }
    QTextStream out(&file);
    out.setCodec("UTF-8");
    out.setGenerateByteOrderMark(true);

    for (int i = 0; i < title.count(); i++)
    {
        out << title[i] << "\n";
    }
    QString temp = (u8"维护类型:");
    out << temp << data.type << "\n";
    temp = (u8"维护人员:");
    out << temp << data.personnel << "\n";
    temp = (u8"维护时间:");
    out << temp << data.u_time << "\n";
    temp = (u8"维护标题:");
    out << temp << data.title << "\n";
    temp = (u8"维护内容:");
    out << temp << "\n";

    if (!text.isEmpty())
    {
        for (int i = 0; i < text.count(); ++i)
        {
            out << text[i] << "\n";
        }
    }
    if (!data.content.isEmpty())
    {
        out << data.content << "\n";
    }

    file.close();
    return true;
}

void txt_routinemaintenance_data::reloadFiles()
{
    StandardItemModel->clear();
    StandardItemModel->setHorizontalHeaderLabels({"文件名", "维护时间"});

    QStandardItem *rootItem = new QStandardItem(QIcon(":/icons/folder.png"),
                                                QFileInfo(currentFolder).fileName());
    rootItem->setData(currentFolder, Qt::UserRole);
    StandardItemModel->appendRow(rootItem);

    QDirIterator it(currentFolder, QDir::Files | QDir::NoDotAndDotDot, QDirIterator::Subdirectories);
    while (it.hasNext())
    {
        QString path = it.next();
        QFileInfo info(path);
        if (info.suffix().toLower() == "txt")
        {

            QString filename = info.fileName();
            QString timestr = filename.mid(5, 14);
            QDateTime filetime = QDateTime::fromString(timestr, "yyyyMMddHHmmss");
            if (filetime.isValid())
            {
                QStandardItem *fileItem = new QStandardItem(QIcon(":/icons/file.png"), info.fileName());
                fileItem->setData(path, Qt::UserRole);         // 存储完整路径
                fileItem->setData(filetime, Qt::UserRole + 1); // 存储精确时间

                QStandardItem *timeItem = new QStandardItem(filetime.toString("yyyy-MM-dd HH:mm:ss"));
                rootItem->appendRow({fileItem, timeItem});
            }

            //            QDateTime modTime = info.lastModified();
        }
    }
    treeview->expandAll();
}

void txt_routinemaintenance_data::deleteOldFiles(QString time)
{
    QDateTime cutoff = QDateTime::fromString(time, "yyyy-MM-dd HH:mm:ss");
    int deletedCount = 0;
    QStandardItem *root = StandardItemModel->item(0);
    for (int i = root->rowCount() - 1; i >= 0; --i)
    {
        QStandardItem *fileItem = root->child(i, 0);
        QDateTime fileTime = fileItem->data(Qt::UserRole + 1).toDateTime();

        if (fileTime < cutoff)
        {
            QString path = fileItem->data(Qt::UserRole).toString();
            QFile file(path);
            if (file.remove())
            {
                root->removeRow(i);
                deletedCount++;
            }
            else
            {
                //QMessageBox::warning(nullptr, "警告", QString("无法删除:\n%1").arg(path));
            }
        }
    }

    //QMessageBox::information(nullptr, "完成", QString("已删除 %1 个文件").arg(deletedCount));
}

bool txt_routinemaintenance_data::select_usetime_StandardItemModel(QString start_time, QString stop_time)
{

    reloadFiles();
    if (start_time == "begin" && stop_time == "end")
        return true;
    QDateTime start = QDateTime::fromString(start_time, "yyyy-MM-dd HH:mm:ss");
    QDateTime end = QDateTime::fromString(stop_time, "yyyy-MM-dd HH:mm:ss");
    QStandardItem *root = StandardItemModel->item(0);
    for (int i = 0; i < root->rowCount(); ++i)
    {
        QStandardItem *fileItem = root->child(i, 0);

        QDateTime fileTime = fileItem->data(Qt::UserRole + 1).toDateTime();
        bool visible = (fileTime >= start) && (fileTime <= end);
        treeview->setRowHidden(i, root->index(), !visible);
    }
}
/*
 *更新文件存储路径
 * 参数:
 *    filepath    新的文件路径
 * 返回值: true 更新成功
 *        false 失败
 */
bool txt_routinemaintenance_data::update_filepath(QString filepath)
{
    // 创建目录结构
    QDir dir(filepath);
    if (!dir.exists())
    {
        if (dir.mkpath(filepath))
        {
            currentFolder = filepath;
            reloadFiles();
            return true;
        }
        else
            return false;
    }
    currentFolder = filepath;
    reloadFiles();
    return true;
}

void txt_routinemaintenance_data::openFile(const QModelIndex &index)
{
    QString path = index.sibling(index.row(), 0).data(Qt::UserRole).toString();
    QDesktopServices::openUrl(QUrl::fromLocalFile(path));
}
/*
 *设置最后一行的颜色
 * 参数:
 *    c 颜色
 * 返回值: true 设置完成
 *        false 行列越界
 */
bool txt_routinemaintenance_data::set_column_color(QVector<QColor> c)
{
    if (c.count() != StandardItemModel->columnCount())
        return false;
    StandardItemModel->blockSignals(true);

    for (int i = 0; i < StandardItemModel->columnCount(); i++)
    {
        StandardItemModel->item(StandardItemModel->rowCount() - 1, i)->setForeground(QBrush(c[i]));
    }

    StandardItemModel->blockSignals(false);
    return true;
}
/*
 *设置指定单元项的颜色
 * 参数:
 *    row    行
 *    Column 列
 *    c      颜色
 * 返回值: true 设置成功
 *        false 失败
 */
bool txt_routinemaintenance_data::set_column_color(int row, int column, const QColor &c)
{
    if (StandardItemModel->rowCount() < row && StandardItemModel->columnCount() < column)
    {
        StandardItemModel->blockSignals(true);
        StandardItemModel->item(row, column)->setForeground(QBrush(c));
        StandardItemModel->blockSignals(false);
        return true;
    }
    return false;
}
