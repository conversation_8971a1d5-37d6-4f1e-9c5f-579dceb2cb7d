﻿#ifndef SYSTEMREGISTER_H
#define SYSTEMREGISTER_H

#include <QWidget>
#include <QLabel>
#include <QLineEdit>
#include <QStatusBar>
namespace Ui
{
    class SystemRegister;
}

class SystemRegister : public QWidget
{
    Q_OBJECT

public:
    explicit SystemRegister(QWidget *parent = nullptr);
    ~SystemRegister();
    int root = 0;
    void open_qmessage(QString title, QString message, QString sure);
    void set_textChange_status(const QString &text);

signals:
    void SystemRegistertoMainWindow();

    void User_query_signal();

    void signal_CalibrationParameterdata();

private slots:

    void on_pushButton_Register_clicked();

    void on_pushButton_delect_user_clicked(); // 删除用户

    void on_pushButton_add_user_clicked(); // 添加用户

    void on_pushButton_register_clicked(); // 用户登录

    void on_pushButton_remove_clicked(); // 删除用户界面

    void on_pushButton_return_clicked(); // 返回登录界面

    void on_pushButton_alter_make_clicked(); // 修改密码

    void on_pushButton_alter_clicked(); // 修改界面

    void on_lineEdit_user_name_textEdited(const QString &arg1); // 当lineEdit控件里面的文本实时发生变化时，发出信号。

    void on_lineEdit_user_password_textEdited(const QString &arg1);

private:
    Ui::SystemRegister *ui;
    QLabel *display1;
    QLabel *display2;
};

#endif // SYSTEMREGISTER_H
