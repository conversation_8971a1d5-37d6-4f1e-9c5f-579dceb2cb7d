﻿#include "classwidgetcomm.h"
#include "ui_classwidgetcomm.h"
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QDebug>
#include <QDateTime>
#include "tools/XC_DataHanding/xcSysVar.h"
#include "tools/Global.h"

classWidgetComm::classWidgetComm(QWidget *parent)
    : QWidget(parent), ui(new Ui::classWidgetComm)
{
    ui->setupUi(this);
    setWindowTitle("通讯设置");
    setWindowModality(Qt::ApplicationModal);

    // 初始化隐藏IP/端口控件
    ui->ipPortLayout->parentWidget()->setVisible(false);

    QSerialPort temp_serial;
    // 添加串口选项时不触发显示逻辑
    bool oldState = ui->comm_comboBox->blockSignals(true);
    foreach (const QSerialPortInfo &Info, QSerialPortInfo::availablePorts())
    {
        temp_serial.setPort(Info);
        if (temp_serial.open(QIODevice::ReadWrite))
        {
            ui->comm_comboBox->addItem(Info.portName());
            temp_serial.close();
        }
    }
    ui->comm_comboBox->blockSignals(oldState);

    // 重新连接信号
    /*connect(ui->comm_comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(comboBoxSlot()));*/
    connect(ui->comm_comboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), [this](int index)
            {
        QString currentText = ui->comm_comboBox->currentText();
        bool isNetwork = (currentText == "TCP" || currentText == "UDP");
        // 直接控制布局内控件的可见性
        for(int i = 0; i < ui->ipPortLayout->count(); i++) {
            QWidget* widget = ui->ipPortLayout->itemAt(i)->widget();
            if(widget) widget->setVisible(isNetwork);
        } });
    CommInterface_t = new CommInterface(CommInterface::TCP_CLIENT);
    Connection_status = false;

    connect(CommInterface_t, &CommInterface::errorOccurred, this, &classWidgetComm::slot_errorOccurred_update);
    // connect(CommInterface_t, &CommInterface::dataReceived, this, &classWidgetComm::slot_comm_recv);
    connect(CommInterface_t, &CommInterface::dataReceived, this, &classWidgetComm::slot_Info_Receive);
    this->on_comm_pushButton_clicked();
    m_Tmr = new QTimer(this);
    connect(this->m_Tmr, &QTimer::timeout, this, &classWidgetComm::slot_Timeout_getID);
    m_Tmr->setInterval(1000);
    m_Tmr->setTimerType(Qt::PreciseTimer);
    m_Tmr->start();

    // m_Tmr1 = new QTimer(this);
    // connect(this->m_Tmr1, &QTimer::timeout, this, &classWidgetComm::slot_Timeout_recv);
    // m_Tmr1->setInterval(5);
    // m_Tmr1->setTimerType(Qt::PreciseTimer);
    // m_Tmr1->start();
}

classWidgetComm::~classWidgetComm()
{
    delete ui;
}

void classWidgetComm::set_SamCardFiltCoef_read_fun(bool data)
{
    SamCardFiltCoef_flag = data;
}

void classWidgetComm::set_SixTemp_read_fun(bool data)
{
    cardTempC_flag = data;
}

void classWidgetComm::set_AgvVoltage_read(bool data)
{
    AgvVoltage_flag = data;
}

void classWidgetComm::set_AdcCardTempCtrl_read_fun(bool data)
{
    AdcCardTempCtrl_flag = data;
}

void classWidgetComm::set_NoiseSrcSw_read_fun(bool data)
{
    NoiseSrcSw_flag = data;
}

void classWidgetComm::set_GPSParam_read_fun(bool data)
{
    GPSParam_flag = data;
}

void classWidgetComm::set_AntAngParam_read_fun(bool data)
{
    AntAngParam_flag = data;
}

void classWidgetComm::set_fTempLowAverage_read_fun(bool data)
{
    fTempLowAverage_flag = data;
}

void classWidgetComm::set_sixWeather_read_fun(bool data)
{
    WeathersixEles_flag = data;
}

void classWidgetComm::set_windTurbParam_read_fun(bool data)
{
    windTurbParam_flag = data;
}

void classWidgetComm::set_workStatus_read_fun(bool data)
{
    workStatus_flag = data;
}

void classWidgetComm::set_PowerStatus_read_fun(bool data)
{
    PowerStatus_flag = data;
}

void classWidgetComm::set_currWordMode_read_fun(bool data)
{
    currWordMode_flag = data;
}

void classWidgetComm::set_fistTempBt_read_fun(bool data)
{
    fistTempBt_flag = data;
}

void classWidgetComm::SendData(uchar *pData, uint len)
{
    auto str = QByteArray::fromRawData((char *)pData, len);
    if (Connection_status == false)
    {
        this->slot_Information_update("Send error：通讯未建立");
    }
    else
    {
        CommInterface_t->send(str);
        this->slot_Information_update("Send：" + str.toHex());
    }
}

void classWidgetComm::Read_MainBoard_Info(QVector<ushort> vect)
{
    uchar Buff[4096];
    uint BuffSize;
    CxcProtocolDJFS myProtocol((void *)tableDJFSToolToMainBoard);

    unsigned int i = 0;
    for (i = 0; i < vect.size(); i++)
        myProtocol.DataIDArray[i] = vect[i];
    myProtocol.DataIDArray[i] = 0;
    myProtocol.Encode(Buff, enumFrameRead, enumFrameDirServer2Local, 0, &BuffSize);
    SendData(Buff, BuffSize);
}

void classWidgetComm::Write_MainBoard_Info(QVector<ushort> vect)
{
    uchar Buff[4096];
    uint BuffSize;
    CxcProtocolDJFS myProtocol((void *)tableDJFSToolToMainBoard);
    unsigned int i = 0;
    for (i = 0; i < vect.size(); i++)
        myProtocol.DataIDArray[i] = vect[i];
    myProtocol.DataIDArray[i] = 0;
    myProtocol.Encode(Buff, enumFrameWrite, enumFrameDirServer2Local, 0, &BuffSize);
    SendData(Buff, BuffSize);
}

CxcDataExchange *classWidgetComm::Get_mainBoard_info(uint16 id)
{
    CxcDataExchange *pDataDef = GetDataBlockByID((void *)tableDJFSToolToMainBoard, id);

    return pDataDef;
}

void classWidgetComm::set_SamCardFiltCoef_fun(float value)
{
    g_perDataBoard.RadioVolFilterParam = value;
    sendbuff.append(0x2100);
}

void classWidgetComm::set_AdcCardTempCtrl1_fun(float P, float I, float D, float TargetTemp)
{
    g_perDataBoard.m_fPID_P[0] = P;
    g_perDataBoard.m_fPID_I[0] = I;
    g_perDataBoard.m_fPID_D[0] = D;
    g_perDataBoard.Temp_Target[0] = TargetTemp;

    sendbuff.append(0x2150);
    sendbuff.append(0x2151);
    sendbuff.append(0x2152);
    sendbuff.append(0x2153);
}

void classWidgetComm::set_AdcCardTempCtrl2_fun(float P, float I, float D, float TargetTemp)
{
    g_perDataBoard.m_fPID_P[1] = P;
    g_perDataBoard.m_fPID_I[1] = I;
    g_perDataBoard.m_fPID_D[1] = D;
    g_perDataBoard.Temp_Target[1] = TargetTemp;

    sendbuff.append(0x2156);
    sendbuff.append(0x2157);
    sendbuff.append(0x2158);
    sendbuff.append(0x2159);
}

void classWidgetComm::set_NoiseSrcSw_fun(bool value)
{
    if (value)
    {
        g_SysVar.TTL_OutBit = 0x0f;
    }
    else
    {
        g_SysVar.TTL_OutBit = 0x00;
    }
    sendbuff.append(0x2181);
}

void classWidgetComm::set_AntTgtAng_fun(float value)
{
    g_CtrlProcess.ANT_AngleTarget = value;
    sendbuff.append(0x2400);
}

void classWidgetComm::set_FanPower_dun(float value)
{
    g_perAirFan.PwmValue = value;
    sendbuff.append(0x2701);
}

void classWidgetComm::set_WorkMode_fun(uchar value)
{
    g_CtrlProcess.RunMode = value;
    sendbuff.append(0x1030);
}

void classWidgetComm::set_DevRestart_fun()
{
#pragma warning(suppress : 4996)
    strcpy((char *)g_SysVar.FirewareUpdateCmd, "reboot");
    sendbuff.append(0x8004);
}

void classWidgetComm::set_sendClear_fun()
{
    sendbuff.clear();
}

void classWidgetComm::set_sendsetting_fun()
{
    Write_MainBoard_Info(sendbuff);
}

float classWidgetComm::get_SamCardFiltCoef_fun()
{
    return g_perDataBoard.RadioVolFilterParam;
}

six_Temp_struct classWidgetComm::get_SixTemp_fun()
{
    six_Temp_struct data;
    for (int i = 0; i < 6; i++)
    {
        data.Temp[i] = g_perDataBoard.fTemValue[i];
    }
    return data;
}

AgvVoltage_struct classWidgetComm::get_AgvVoltage_fun()
{
    AgvVoltage_struct data;
    for (int i = 0; i < 16; i++)
    {
        data.Votage[i] = g_perDataBoard.RadioVolAverage[i];
    }
    return data;
}

cardTempC_struct classWidgetComm::get_cardTempC_fun(uchar value)
{
    cardTempC_struct data;
    if (value == 0 || value == 1)
    {
        data.P = g_perDataBoard.m_fPID_P[value];
        data.I = g_perDataBoard.m_fPID_I[value];
        data.D = g_perDataBoard.m_fPID_D[value];
        data.targetTemp = g_perDataBoard.Temp_Target[value];
        data.currTemp = g_perDataBoard.Temp_Curr[value];
        data.heatPower = g_perDataBoard.m_fHeatPower[value];
        return data;
    }
    return cardTempC_struct();
}

bool classWidgetComm::get_NoiseSrcSw_fun()
{
    if (g_SysVar.TTL_OutBit == 0x0f)
    {
        return true;
    }
    return false;
}

GPSParam_struct classWidgetComm::get_GPSParam_fun()
{
    GPSParam_struct data;
    data.longitude = g_perGPS.longitude;
    data.latitude = g_perGPS.latitude;
    data.UTC_Time = g_perGPS.UTC_Time;
    data.UTC_Date = g_perGPS.UTC_Date;
    data.CenturySecond = g_perGPS.CenturySecond;
    data.HDOP = g_perGPS.HDOP;
    data.HeightEarthLevel = g_perGPS.HeightSeaLevel;
    data.HeightSeaLevel = g_perGPS.HeightEarthLevel;
    return data;
}

AntAngParam_struct classWidgetComm::get_AntAngParam_fun()
{
    AntAngParam_struct data;
    data.AntTgtAng = g_CtrlProcess.ANT_AngleTarget;
    data.antCurrAng = g_perAngleMotor.ANT_AngleCurr;
    data.m_StepPerRound = g_perAngleMotor.m_StepPerRound;
    data.m_StepSpeedSet = g_perAngleMotor.m_StepSpeedSet;
    data.ANT_AngleOffset = g_perAngleMotor.ANT_AngleOffset;
    return data;
}

LowTemp classWidgetComm::get_fTempLow_fun()
{
    LowTemp temp;
    temp.LowTemp1 = g_perTemSource.fTemValue[0];
    temp.LowTemp2 = g_perTemSource.fTemValue[1];
    temp.LowTemp3 = g_perTemSource.fTemValue[2];
    temp.LowTemp4 = g_perTemSource.fTemValue[3];

    return temp;
}

float classWidgetComm::get_fTempLowAverage_fun()
{
    return g_perTemSource.fTemp_LowAverage;
}

WeathersixEles_struct classWidgetComm::get_WeathersixEles_fun()
{
    WeathersixEles_struct data;
    data.Humidity = g_perWeather.Humidity;
    data.Temp = g_perWeather.Temp;
    data.AirPress = g_perWeather.AirPress;
    data.WindSpeed = g_perWeather.WindSpeed;
    data.WindDir = g_perWeather.WindDir;
    data.Rain10Minute = g_perWeather.Rain10Minute;
    return data;
}

windTurbParam_struct classWidgetComm::get_windTurbParam_fun()
{
    windTurbParam_struct data;
    data.Speed = g_perAirFan.Speed;
    data.PwmValue = g_perAirFan.PwmValue;
    return data;
}

workStatus_struct classWidgetComm::get_workStatus_fun()
{
    workStatus_struct data;
    data.colCardStat = g_perDataBoard.m_Status;
    data.antMotStat = g_perAngleMotor.m_Status;
    data.metSixElemsStat = g_perWeather.m_Status;
    data.WindTurbStat = g_perAirFan.m_Status;
    return data;
}

PowerStatus_struct classWidgetComm::get_PowerStatus_fun()
{
    PowerStatus_struct data;
    data.colCardStat = g_perDataBoard.m_PowerSet;
    data.antMotStat = g_perAngleMotor.m_PowerSet;
    data.metSixElemsStat = g_perWeather.m_PowerSet;
    data.WindTurbStat = g_perAirFan.m_PowerSet;
    return data;
}

uchar classWidgetComm::get_currWordMode_fun()
{
    return g_CtrlProcess.RunMode;
}

float classWidgetComm::get_fistTempBt_fun()
{
    return g_CtrlProcess.m_arrayTofNormalTem[0];
}

void classWidgetComm::on_comm_pushButton_clicked()
{
    if (Connection_status == false)
    {
        if (ui->comm_comboBox->currentText() == "TCP")
        {
            CommInterface_t->InterfaceTypeToggle(CommInterface::TCP_CLIENT);
            if (CommInterface_t->open(ui->ipLineEdit->text(), ui->portSpinBox->value()))
            {
                Connection_status = true;
                ui->comm_pushButton->setText("断开");
                ui->comm_comboBox->setEnabled(false);
                this->slot_Information_update(ui->comm_comboBox->currentText() + "：连接成功");
            }
        }
        else if (ui->comm_comboBox->currentText() == "UDP")
        {
            CommInterface_t->InterfaceTypeToggle(CommInterface::UDP_CLIENT);
            if (CommInterface_t->open(ui->ipLineEdit->text(), ui->portSpinBox->value()))
            {
                Connection_status = true;
                ui->comm_pushButton->setText("断开");
                ui->comm_comboBox->setEnabled(false);
                this->slot_Information_update(ui->comm_comboBox->currentText() + "：连接成功");
            }
        }

        else
        {
            CommInterface_t->InterfaceTypeToggle(CommInterface::SERIAL);
            if (CommInterface_t->open(ui->comm_comboBox->currentText(), 8080))
            {
                Connection_status = true;
                ui->comm_pushButton->setText("断开");
                ui->comm_comboBox->setEnabled(false);
                this->slot_Information_update(ui->comm_comboBox->currentText() + "：连接成功");
            }
        }
    }
    else
    {
        CommInterface_t->close();
        this->slot_Information_update(ui->comm_comboBox->currentText() + "：已断开");
        Connection_status = false;
        facility_connect_status = false;
        ui->comm_pushButton->setText("连接");
        ui->comm_comboBox->setEnabled(true);
    }
}

void classWidgetComm::slot_errorOccurred_update(const QString &info)
{
    CommInterface_t->close();
    this->slot_Information_update(ui->comm_comboBox->currentText() + "：已断开");
    Connection_status = false;
    facility_connect_status = false;
    ui->comm_pushButton->setText("连接");
    ui->comm_comboBox->setEnabled(true);

    // 注意，这里添加日志调用
    if (ui->comm_textEdit->document()->lineCount() > 300)
        ui->comm_textEdit->clear();
    ui->comm_textEdit->append(QDateTime::currentDateTime().toString("[yyyy-MM-dd hh:mm:ss.zzz]") + info);
}

void classWidgetComm::slot_Information_update(const QString &info)
{
    // 注意，这里添加日志调用
    if (ui->comm_textEdit->document()->lineCount() > 300)
        ui->comm_textEdit->clear();
    ui->comm_textEdit->append(QDateTime::currentDateTime().toString("[yyyy-MM-dd hh:mm:ss.zzz]") + info);
}

void classWidgetComm::slot_Timeout_getID()
{
    QVector<ushort> buff;

    if (Connection_status == true)
    {
        if (g_SysVar.StrSoftwareVersion[0] != '\0')
        {
            facility_connect_status = true;
        }
        else
        {
            buff.append(0x4F00);
            buff.append(0x8000);
            buff.append(0x8001);
            Read_MainBoard_Info(buff);
            facility_connect_status = false;
        }
    }
    else
    {
        g_SysVar.StrSoftwareVersion[0] = '\0';
    }

    if (facility_connect_status == true)
    {
        // 采集卡滤波系数
        if (SamCardFiltCoef_flag)
        {
            buff.append(0x2100);
        }
        // 采集卡温度参数
        if (cardTempC_flag)
        {
            for (int i = 0; i < 6; i++)
            {
                buff.append(0x2111 + i);
            }
        }
        // 接收机平均电压标志位
        if (AgvVoltage_flag)
        {
            for (int i = 0; i < 16; i++)
            {
                buff.append(0x2120 + i);
            }
        }
        // 采集卡温控参数标志位
        if (AdcCardTempCtrl_flag)
        {
            for (int i = 0; i < 12; i++)
            {
                buff.append(0x2150 + i);
            }
        }
        // 噪声源开关标志位
        if (NoiseSrcSw_flag)
        {
            buff.append(0x2181);
        }
        // GPS信息标志位
        if (GPSParam_flag)
        {
            for (int i = 0; i < 8; i++)
            {
                buff.append(0x2300 + i);
            }
        }
        // 天线角度标志位
        if (AntAngParam_flag)
        {
            for (int i = 0; i < 7; i++)
            {
                buff.append(0x2400 + i);
            }
        }

        if (fTempLow_falg)
        {
            buff.append(0x2501);
            buff.append(0x2502);
            buff.append(0x2503);
            buff.append(0x2504);
        }

        // 常温源平均温度标志位
        if (fTempLowAverage_flag)
        {
            buff.append(0x2505);
        }
        // 气象6要素标志位
        if (WeathersixEles_flag)
        {
            for (int i = 0; i < 6; i++)
            {
                buff.append(0x2607 + i);
            }
        }
        // 风扇状态标志位
        if (windTurbParam_flag)
        {
            buff.append(0x2700);
            buff.append(0x2701);
        }
        // 外设工作状态标志位
        if (workStatus_flag)
        {
            buff.append(0x1020);
            buff.append(0x1023);
            buff.append(0x1025);
            buff.append(0x1026);
        }
        // 工作模式标志位
        if (currWordMode_flag)
        {
            buff.append(0x1040);
            buff.append(0x1043);
            buff.append(0x1045);
            buff.append(0x1046);
        }
        // 第一路电源状态标志位
        if (PowerStatus_flag)
        {
            buff.append(0x4210);
        }

        // 排序并去重复值
        Global::Container_removal(&buff);
        Read_MainBoard_Info(buff);
    }
}

void classWidgetComm::slot_Info_Receive(QByteArray qb)
{
    this->slot_Information_update("Rec：" + qb.toHex());
    // 协议解析
    CxcProtocolDJFS myProtocol((void *)tableDJFSToolToMainBoard);
    Decode_return *data;
    QVector<ushort> SamCardFiltCoef, Temp_six_buff, RadioVolAverage, AdcCardTempCtrl1, AdcCardTempCtrl2, NoiseSrcSw,
        GPS_buff, AntAng, fTemp_Low,fTemp_LowAverage, sixWeather, fan_buff, ip_buff, WorkStatus, workMode, PowerStatus,
        veerFwInfo, NoiseTemp;
    data = myProtocol.Decode((uchar *)(qb.data()), 0, qb.size());
    if (eumErrNone == data->m_FrameErr)
    {
        uchar Buff[4096];
        uint BuffSize;

        if (myProtocol.m_FrameDir == enumFrameDirLocal2Server)
        {
            // 心跳包
            if (myProtocol.m_FrameType == enumFrameHeart)
            {
                myProtocol.Encode(Buff, enumFrameHeart, myProtocol.m_FrameDir ^ 0x80, 0, &BuffSize);
                SendData(Buff, BuffSize);
            }
            //  写回复
            else if (myProtocol.m_FrameType == enumFrameWrite)
            {
                for (ushort var = 0; var < data->mylen; var++)
                {
                    if (data->myid[var] == 0x2100)
                    {
                        SamCardFiltCoef.append(data->myid[var]);
                    }

                    // 6路温度信号
                    if (data->myid[var] >= 0x2111 && data->myid[var] <= 0x2116)
                    {
                        Temp_six_buff.append(data->myid[var]);
                    }
                    // 16路均值电压
                    if (data->myid[var] >= 0x2120 && data->myid[var] <= 0x212f)
                    {
                        RadioVolAverage.append(data->myid[var]);
                    }
                    // 采集卡温控1
                    if (data->myid[var] >= 0x2150 && data->myid[var] <= 0x2155)
                    {
                        AdcCardTempCtrl1.append(data->myid[var]);
                    }
                    // 采集卡温控2
                    if (data->myid[var] >= 0x2156 && data->myid[var] <= 0x215b)
                    {
                        AdcCardTempCtrl2.append(data->myid[var]);
                    }
                    // 噪声源开关
                    if (data->myid[var] == 0x2181)
                    {
                        NoiseSrcSw.append(data->myid[var]);
                    }
                    // GPS参数
                    if (data->myid[var] >= 0x2300 && data->myid[var] <= 0x2307)
                    {
                        GPS_buff.append(data->myid[var]);
                    }
                    // 天线角度
                    if (data->myid[var] >= 0x2400 && data->myid[var] <= 0x2406)
                    {
                        AntAng.append(data->myid[var]);
                    }

                    // 4个常温源温度
                    if (data->myid[var] >= 0x2501 &&data->myid[var] <= 0x2504)
                    {
                        fTemp_Low.append(data->myid[var]);
                    }
                    // 常温源平均温度
                    if (data->myid[var] == 0x2505)
                    {
                        fTemp_LowAverage.append(data->myid[var]);
                    }
                    // 气象六要素
                    if (data->myid[var] >= 0x2607 && data->myid[var] <= 0x260c)
                    {
                        sixWeather.append(data->myid[var]);
                    }
                    // 风机
                    if (data->myid[var] >= 0x2700 && data->myid[var] <= 0x2701)
                    {
                        fan_buff.append(data->myid[var]);
                    }
                    // 本机参数
                    if (data->myid[var] >= 0x1010 && data->myid[var] <= 0x1015)
                    {
                        ip_buff.append(data->myid[var]);
                    }
                    // 外设工作状态
                    if (data->myid[var] >= 0x1020 && data->myid[var] <= 0x1026)
                    {
                        WorkStatus.append(data->myid[var]);
                    }
                    // 工作模式
                    if (data->myid[var] == 0x1030)
                    {
                        workMode.append(data->myid[var]);
                    }
                    // 外设电源状态
                    if (data->myid[var] >= 0x1040 && data->myid[var] <= 0x1046)
                    {
                        PowerStatus.append(data->myid[var]);
                    }
                    // 版本字符串
                    if (data->myid[var] >= 0x8000 && data->myid[var] <= 0x8005)
                    {
                        veerFwInfo.append(data->myid[var]);
                    }
                    // 第1路常温源温度值
                    if (data->myid[var] == 0x4f00)
                    {
                        NoiseTemp.append(data->myid[var]);
                    }
                }

                if (SamCardFiltCoef.size())
                    emit signal_SamCardFiltCoef_data(SamCardFiltCoef);
                if (Temp_six_buff.size())
                    emit signal_SixTemp_buff_data(Temp_six_buff);
                if (RadioVolAverage.size())
                    emit signal_RadioVolAverage_data(RadioVolAverage);
                if (AdcCardTempCtrl1.size())
                    emit signal_AdcCardTempCtrl1_data(AdcCardTempCtrl1);
                if (AdcCardTempCtrl2.size())
                    emit signal_AdcCardTempCtrl2_data(AdcCardTempCtrl2);
                if (NoiseSrcSw.size())
                    emit signal_NoiseSrcSw_data(NoiseSrcSw);
                if (GPS_buff.size())
                    emit signal_GPS_buff_data(GPS_buff);
                if (AntAng.size())
                    emit signal_AntAng_data(AntAng);
                if (fTemp_Low.size())
                    emit signal_fTemp_Low_data(fTemp_Low);
                if (fTemp_LowAverage.size())
                    emit signal_fTemp_LowAverage_data(fTemp_LowAverage);
                if (sixWeather.size())
                    emit siganl_sixWeather_data(sixWeather);
                if (fan_buff.size())
                    emit signal_windTurbParam_data(fan_buff);
                /*if (ip_buff.size())
                    emit signal_ip_buff_data(ip_buff);*/
                if (WorkStatus.size())
                    emit signal_WorkStatus_data(WorkStatus);
                if (workMode.size())
                    emit signal_workMode_data(workMode);
                if (PowerStatus.size())
                    emit signal_PowerStatus_data(PowerStatus);
                /*if (veerFwInfo.size())
                    emit signal_veerFwInfo_data(veerFwInfo);*/
                if (NoiseTemp.size())
                    emit signal_fistTempBt_data(NoiseTemp);
            }
            else if (myProtocol.m_FrameType == enumFrameFirmWareUpdateBack) //
            {
                // 固件更新 信号
            }
        }
        else if (myProtocol.m_FrameDir == enumFrameDirDataBoard2Server)
        {
            if (myProtocol.m_FrameType == enumFrameFirmWareUpdateBack) //
            {
                // 固件更新 信号
            }
        }
    }
    delete (data);
}

/***    当为串口时需要一下定时器和超时函数    ***/
void classWidgetComm::slot_comm_recv(QByteArray qb)
{
    qb_revbuff += qb;
    m_RecvDelay = 5;
}

void classWidgetComm::slot_Timeout_recv()
{
    if (m_RecvDelay > 0)
    {
        m_RecvDelay--;
        if (m_RecvDelay == 0)
        {
            slot_Info_Receive(qb_revbuff);
            qb_revbuff.clear();
        }
    }
}
