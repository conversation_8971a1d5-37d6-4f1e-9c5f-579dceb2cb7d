﻿#ifndef DEVICE_STATUS_DATA_STORAGE_H
#define DEVICE_STATUS_DATA_STORAGE_H
#include <tools/data_treating/cpublic_struct.h>
#include <QObject>
#include <QDir>
#include <cmath>
#include <QDateTime>
#include <QTextStream>
#include <QDomDocument>
#include <QFile>
struct Device_status_data
{
    int Record;        // 含义：记录序号	记录序号
    QString DateTime;  // 含义：记录日期及时间	格式为yyyy-mm-dd  hh:mm:ss
    int General;       // 含义：总状态	0：正常，1：异常
    int EServo;        // 含义：俯仰转台	0：正常，1：异常，-1：无此项
    int AServo;        // 含义：方位转台	0：正常，1：异常，-1：无此项
    int RCV0;          // 含义：水汽观测接收机	0：正常，1：异常，-1：无此项
    int RCV1;          // 含义：温度观测接收机	0：正常，1：异常，-1：无此项
    double TRec1;      // 含义：水汽通道接收机温度	水汽通道接收机温度，单位为K
    double TRec2;      // 含义：氧气通道接收机温度	氧气通道接收机温度，单位为K
    int SRec1;         // 含义：水汽通道接收机热稳定性	0：正常，1：异常，-1：无此项
    int SRec2;         // 含义：氧气通道接收机热稳定性	0：正常，1：异常，-1：无此项
    int LO;            // 含义：接收本振	0：正常，1：异常，-1：无此项
    int BIB;           // 含义：内标定源	0：正常，1：异常，-1：无此项
    double TAmb1;      // 含义：内置黑体温度1	内置黑体温度1，单位为K，-1：无此项
    double TAmb2;      // 含义：内置黑体温度2	内置黑体温度2，单位为K，-1：无此项
    double TAmb3;      // 含义：内置黑体温度3	内置黑体温度3，单位为K，-1：无此项
    double TAmb4;      // 含义：内置黑体温度4	内置黑体温度4，单位为K，-1：无此项
    int SurTem;        // 含义：地面温度	0：正常，1：异常，-1：无此项
    int SurHum;        // 含义：地面湿度	0：正常，1：异常，-1：无此项
    int SurPre;        // 含义：地面气压	0：正常，1：异常，-1：无此项
    int Rain;          // 含义：降雨	0：正常，1：异常，-1：无此项
    int Tir;           // 含义：测云组件	0：正常，1：异常，-1：无此项
    int TimeSync;      // 含义：时间同步组件	0：正常，1：异常，-1：无此项
    int ECM;           // 含义：防雨雾控制组件	0：正常，1：异常，-1：无此项
    int ExPower;       // 含义：外接电源	0：正常，1：异常，-1：无此项
    int Communication; // 含义：通讯状态	0：正常，1：异常，-1：无此项
};

class Device_status_data_storage : public QObject
{
    Q_OBJECT
public:
    explicit Device_status_data_storage(QString file_path, struct FILENAME file_name, QObject *parent = nullptr);
    // 日文件
    bool Generate_new_Device_status_data(struct Device_status_data device_status_data);
    // 分钟文件
    bool Generate_new_Device_status_Minutes_file(struct Device_status_data device_status_data);

    void set_device_and_type(QString device, QString type);
    void set_file_path(QString path);         // 设置文件路径
    void set_file_name(struct FILENAME name); // 设置文件名
    void set_Minutes_file_en(bool en);        // 设置分钟文件存储使能
    void set_Day_file_en(bool en);            // 设置日文件存储使能
    void set_beijing_or_utc(bool choose);     // 设置文件路径及文件名中的时间时使用北京时还是UTC时间  true beijing false utc

    struct FILENAME filename; // 文件名称信息结构体
    QString cur_day_filename; // 当前日文件名称
    QString filepath;         // 文件保存路径
    QFile *file;              // 记录日文件

    QString device = "radiometer";
    QString type = "MFile";
    bool txt_or_xml = false; // true txt   false xml
    void set_save_mode(bool mode);

signals:

public slots:
    void save_data(struct Device_status_data device_status_data);

private:
    bool Insert_new_Device_status_Basic_parameters();                                 // 在日文件中插入一条基础数据
    bool Insert_new_Device_status_data(struct Device_status_data device_status_data); // 在日文件中插入一条新的亮温数据
    QString Generate_file_name(struct FILENAME file_name);                            // 生成文件名
    bool isPureAscii(const QString &str);                                             // 判断字符串是否是ASCII码
    QString Control_decimal_bit(double data, int bit, bool status);                   // 控制小数位

    QString deal_int_data(int data);
    QString deal_qstring_data(QString data);
    /*在田间日文件时判断本地路径下是否已经存在日文件，不考虑时分秒，但要验证其合法性*/
    bool comparefilenames(const QString &filename1, const QString &filename2);                                      // 对比文件名
    bool processFileName(const QString &filename, bool &timevalid, QString &processedbasename, QString &extension); // 处理并验证文件名，生成忽略时分秒后的新的文件名。
    // int ch = 14;
    int Record_number = 1; // 记录日文件的行号

    bool Minutes_file_en = false; // 是否保存为分钟文件
    bool Day_file_en = false;     // 是否保存为日文件
    bool use_beijing_utc = true;  // 北京时和utc时的标志位
};

#endif // DEVICE_STATUS_DATA_STORAGE_H
