﻿#include "CColorCardDialog.h"
#include "ui_CColorCardDialog.h"

CColorCardDialog::CColorCardDialog(QWidget *parent) : QDialog(parent),
                                                      ui(new Ui::CColorCardDialog)
{
    ui->setupUi(this);
    this->setWindowFlags(this->windowFlags() | Qt::WindowMaximizeButtonHint);
    this->setWindowFlags(Qt::Window);
    setInitialColors();
    // 连接信号槽
    connect(ui->tableWidget, &QTableWidget::itemChanged,
            this, &CColorCardDialog::onTableItemChanged);
}

CColorCardDialog::~CColorCardDialog()
{
    delete ui;
}

// 初始化表格数据
void CColorCardDialog::initializeTable()
{

    int rowCount = ui->tableWidget->rowCount();
    QStringList rgbValues = {"#FF0000", "rgb(0, 255, 0)", "#0000FF", "rgb(255, 255, 0)", "rgb(255, 0, 255)"};
    for (int i = 0; i < rowCount; ++i)
    {
        QTableWidgetItem *item = new QTableWidgetItem(QString("Item %1").arg(i + 1));
        ui->tableWidget->setItem(i, 0, item);
        QTableWidgetItem *rgbItem = new QTableWidgetItem(rgbValues[i]);
        ui->tableWidget->setItem(i, 1, rgbItem);
    }

    // 设置第一列的背景颜色
    setInitialColors();
}

// 设置初始颜色
void CColorCardDialog::setInitialColors()
{
    int rowCount = ui->tableWidget->rowCount();
    for (int row = 0; row < rowCount; ++row)
    {
        QTableWidgetItem *rgbItem = ui->tableWidget->item(row, 1); // 获取第二列的单元格
        if (rgbItem)                                               // 检查第二列的单元格是否存在
        {
            QColor color = parseColor(rgbItem->text()); // 解析第二列的文本为颜色
            if (color.isValid())
            {
                QTableWidgetItem *firstColItem = ui->tableWidget->item(row, 0);
                if (!firstColItem)
                {
                    firstColItem = new QTableWidgetItem();
                    ui->tableWidget->setItem(row, 0, firstColItem); // 将新单元格添加到表格中
                }
                firstColItem->setBackground(color); // 设置第一列单元格的背景颜色
                // 使用 setForeground 方法设置第一列单元格的文本颜色。如果背景颜色的亮度（lightness）大于 128（即较亮的颜色），将文本颜色设置为黑色；否则，设置为白色。
                // firstColItem->setForeground(color.lightness() > 128 ? Qt::black : Qt::white);
                firstColItem->setText(""); // 清空文本内容
            }
        }
    }
}
// 颜色解析函数
QColor CColorCardDialog::parseColor(const QString &colorStr)
{
    // 支持 #RRGGBB / rgb(r,g,b) / RRGGBB 格式
    if (colorStr.startsWith("#"))
    {
        return QColor(colorStr);
    }
    else if (colorStr.startsWith("rgb("))
    {
        QStringList parts = colorStr.mid(4, colorStr.length() - 5).split(',');
        if (parts.count() == 3)
        {
            return QColor(
                parts[0].trimmed().toInt(),
                parts[1].trimmed().toInt(),
                parts[2].trimmed().toInt());
        }
    }
    else if (colorStr.length() == 6)
    {
        bool isValidHex;
        colorStr.toInt(&isValidHex, 16);
        if (isValidHex)
            return QColor("#" + colorStr);
    }
    return QColor(); // 无效颜色
}

// 表格项更新槽函数
void CColorCardDialog::onTableItemChanged(QTableWidgetItem *item)
{
    // 只处理第二列（column index 1）的变化
    if (item->column() == 1)
    {
        int row = item->row();
        QColor color = parseColor(item->text());

        if (color.isValid())
        {
            // 获取或创建第一列的Item
            QTableWidgetItem *firstColItem = ui->tableWidget->item(row, 0);
            if (!firstColItem)
            {
                firstColItem = new QTableWidgetItem();
                ui->tableWidget->setItem(row, 0, firstColItem);
            }

            // 设置背景色和文字颜色
            firstColItem->setBackground(color);
            // firstColItem->setForeground(color.lightness() > 128 ? Qt::black : Qt::white);
            firstColItem->setText(""); // 清空文本内容
        }
    }
}

void CColorCardDialog::on_radioButton_clicked()
{
}

void CColorCardDialog::on_pushButton_create_clicked()
{
    double score_row = ui->doubleSpinBox_score->value();
    // int row_num = ui->tableWidget->rowCount();
    ui->tableWidget->setRowCount(score_row);
    double min_value = ui->lineEdit_min->text().toDouble();
    double max_value = ui->lineEdit_min->text().toDouble();
    double interval_value = (max_value - min_value) / score_row;
    double initial_value = min_value;
    double initial_value1 = min_value;
    double initial_value2 = min_value + 1.25;
    for (int row = 1; row < score_row; ++row)
    {
        QTableWidgetItem *item = new QTableWidgetItem();
        item->setData(Qt::DisplayRole, initial_value);
        initial_value += interval_value;
        ui->tableWidget->setItem(row, 2, item);
    }
    QTableWidgetItem *first_item = new QTableWidgetItem(QString("-∞"));
    ui->tableWidget->setItem(0, 2, first_item);
    for (int row = 0; row < score_row - 1; ++row)
    {
        QTableWidgetItem *item1 = new QTableWidgetItem();
        item1->setData(Qt::DisplayRole, initial_value1);
        initial_value1 += interval_value;
        ui->tableWidget->setItem(row, 3, item1);
    }
    QTableWidgetItem *last_item = new QTableWidgetItem(QString("+∞"));
    ui->tableWidget->setItem(score_row - 1, 3, last_item);

    for (int row = 1; row < score_row; ++row)
    {
        QTableWidgetItem *item2 = new QTableWidgetItem();
        item2->setData(Qt::DisplayRole, initial_value2);
        initial_value2 += interval_value;
        ui->tableWidget->setItem(row, 4, item2);
    }
    QTableWidgetItem *first_item1 = new QTableWidgetItem(QString("<=%").arg(min_value));
    ui->tableWidget->setItem(0, 4, first_item1);
    QTableWidgetItem *last_item1 = new QTableWidgetItem(QString(">%").arg(max_value));
    ui->tableWidget->setItem(score_row - 1, 4, last_item1);

    for (int row = 0; row < score_row - 1; ++row)
    {
        QTableWidgetItem *item3 = new QTableWidgetItem();
        item3->setData(Qt::DisplayRole, " ");

        ui->tableWidget->setItem(row, 1, item3);
    }
}
