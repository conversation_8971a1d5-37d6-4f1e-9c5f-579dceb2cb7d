﻿#include <Qwt/qwt_plot.h>
#include <Qwt/qwt_plot_spectrogram.h>
#include <Qwt/qwt_raster_data.h>

#include <qnumeric.h>
#include <Qwt/qwt_color_map.h>
#include <Qwt/qwt_plot_spectrogram.h>
#include <Qwt/qwt_scale_widget.h>
#include <Qwt/qwt_scale_draw.h>
#include <Qwt/qwt_plot_zoomer.h>
#include <Qwt/qwt_plot_panner.h>
#include <Qwt/qwt_plot_layout.h>
#include <Qwt/qwt_plot_renderer.h>

#include "spectrogramdata.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QTime>
#include "labeledspectrogram.h"
#include <Qwt/qwt_scale_engine.h>
#include <QContextMenuEvent>
#include <QMenu>
#include <cmath>
class MyZoomer;
class TimeScaleDraw;
class yleftScaleDraw;
class yrightScaleDraw;
/*该类为绘制qwt的主类*/
class Plot : public QwtPlot
{
    Q_OBJECT

public:
    enum ColorMap
    {
        RGBMap,
        IndexMap,
        HueMap,
        AlphaMap
    };

    Plot(int Spectrogram_number_t, QWidget* = NULL);
    QwtPlotSpectrogram* spectrogram() const { return d_spectrogram[0]; } // 获取绘图指针
    // 添加实时数据点（时间、高度层、温度向量）
    void addRealtimeData(int num, QDateTime time, const QVector<double>& heights, const QVector<double>& temps); // 添加实时绘图信息

    QDateTime uptime; // 存储实时绘图最后一次数据的时间，当时间不在同一天时清除绘图重新绘制

    void set_Data_smoothing_parameter(int data); // 设置等值线平滑最小0 不平滑，data越大，绘图效率越慢。

    QVector<QVector<double>> m_times, m_heights; // 存储当前绘图数据的时间及高度，最多两组绘图数据
    QVector<QVector<QVector<double>>> m_temps;   // 存储当前绘图的产品数据，最多两组绘图数据

    QVector<QVector<double>> get_m_times_data();          // 获取当前绘图的时间列表
    QVector<QVector<double>> get_m_heights_data();        // 获取当前绘图的高度列表
    QVector<QVector<QVector<double>>> get_m_temps_data(); // 获取当前绘图的产品列表
    // choose1,day
    QVector<int> choose1;
    QVector<int> choose2;
    void set_mode(QVector<int> d1, QVector<int> d2); // 设置绘图数据"2,11"温度,"3,12"水汽,"4,13"湿度,"5,14"水廓线，最多存在两种绘图
    QVector<int> Data_smoothing_parameter;           // 数据平滑参数，值越大越平滑，数据越失真，运行效率越慢
    int Multiple = 1;                                // 不用，
    void set_Multiple(int data);
    void set_contourLevels(QList<double> data); // 添加等值线数据

    bool Combination = false;             // trure,使用双层绘图
    void set_Combination(bool en);        // 双层绘图使能
    int Spectrogram_number = 1;           // 记录绘图层数
    void set_Spectrogram_number(int num); // 设置绘图层数

    void showContour(bool on);     // 显示等值线使能
    void showSpectrogram(bool on); // 显示绘图使能
    void setColorMap(int);         // 设置颜色映射
    void setAlpha(int);
    void clearSpectrogram();                                  // 清楚绘图及数据
    void set_label_enable(bool en);                           // 显示等值线标签使能
    void set_label_Density(int data);                         // 设置标签密度
    void set_x_rang(double min, double max, double stepSize); // 设置X轴显示范围
    void set_x_right(double data);                            // 设置x轴右移
    void set_x_left(double data);                             // 设置x轴左移

    void set_y_rang(double min, double max, double stepSize); // 设置y轴显示范围
    void set_y_up(double data);                               // 设置y轴上移
    void set_y_down(double data);                             // 设置y轴下移

    void contextMenuEvent(QContextMenuEvent* event);                                               // qwt右键菜单事件
    void set_title(QString text);                                                                  // 设置绘图标题
    void set_LinearColorMapRGB_en(bool en);                                                        // 自定义颜色绘图使能
    bool LinearColorMapRGB_en = false;                                                             // 使用默认颜色绘图数据
    void set_LinearColorMapRGB(QColor start_c, QColor end_c, QVector<QPair<double, QColor>> data); // 添加自定义绘图颜色数据
    QVector<QPair<double, QColor>> LinearColorMapRGB_data;                                         // 存储颜色绘图数据
    QColor startColor;                                                                             // 存储最小值颜色绘图数据
    QColor endColor;                                                                               // 存储最大值颜色绘图数据

    void set_label_color(QColor data);  // 设置坐标轴刻度线及标签颜色
    void set_yright_uint(QString name); // 设置Y轴右侧坐标轴的单位

public Q_SLOTS:
    void open_file(QString filepath); // 打开历史绘图数据
signals:
    void spectrogram_change(QString data);                                          // 用户右键切换绘图信号
    void cur_x_y1(double time, QVector<double> m_heights, QVector<double> m_temps); // 发送当前轮廓线数据
protected:
    void resizeEvent(QResizeEvent* e) override
    {
        // 1. 调用基类方法（无需返回）
        QwtPlot::resizeEvent(e);

        //            // 2. 获取新窗口尺寸
        //            int newWidth = e->size().width();
        //            int newHeight = e->size().height();
        //            qDebug() << newWidth << "        " << newHeight;

        //            // 3. 安全处理宽度为0的情况
        //            if (newWidth <= 0) newWidth = 1;

        //            // 4. 计算步长（确保是3600的倍数）
        //            double totalSeconds = 86400.0;
        //            double pixelsPerHour = newWidth / 24.0;  // 每小时的像素宽度
        //            double idealStepsPerHour = 60.0 / pixelsPerHour; // 理想步长（小时）
        //            int roundedSteps = std::ceil(idealStepsPerHour); // 向上取整
        //            double step = roundedSteps * 3600; // 转换为秒

        //            // 5. 设置坐标轴
        //            int temp = (newWidth/60)-1;
        //            if(temp>0)
        //            {
        //            setAxisScale(QwtPlot::xBottom, 0, totalSeconds, 86400/temp);
        //            setAxisScale(QwtPlot::yLeft, 0, 10, 1);
        //            }

        const std::vector<int> factors_of_720 = { 1, 2, 3, 4, 5, 6, 8, 9, 10, 12, 15, 16, 18, 20, 24, 30, 36, 40, 45, 48, 60, 72, 80, 90, 120, 144, 180, 240, 360, 720 };
        int max_ticks = e->size().width() / 60;
        if (max_ticks > 1)
        {
            double min_step = static_cast<double>(86400.0) / (max_ticks - 1);
            int m_min = static_cast<int>(std::ceil(min_step / 120));
            // step = std::min(step,86400);

            auto it = std::lower_bound(factors_of_720.begin(), factors_of_720.end(), m_min);
            if (it != factors_of_720.end())
            {
                int step = 120 * (*it);
                setAxisScale(QwtPlot::xBottom, 0, 86400, (step <= 96400 && 86400 % step == 0) ? step : 86400);
                setAxisScale(QwtPlot::yLeft, 0, 10, 1);
            }
        }

        // 6. 更新绘图
        replot();
    }

private:
    QVector<QwtPlotSpectrogram*> d_spectrogram; // 存储绘图窗口

    TimeScaleDraw* timeScaleDraw = nullptr;     // 自定义x轴标签对象
    MyZoomer* zoomer = nullptr;                 // 自定义鼠标标签对象
    QwtPlotPanner* panner = nullptr;            // 绘图平移交互对象
    QwtScaleWidget* rightAxis = nullptr;        // y轴右侧刻度线对象
    yleftScaleDraw* yleftscaledraw = nullptr;   // y轴左侧自定义标签对象
    yrightScaleDraw* yrightscaledraw = nullptr; // y轴右侧自定义标签对象

    int last_fx = 0;
    int d_mapType;
    int d_alpha;
};

// 自定义标签类用于显示鼠标当前位置的信息
class MyZoomer : public QwtPlotZoomer
{
    Q_OBJECT
public:
    MyZoomer(QWidget* canvas) : QwtPlotZoomer(canvas)
    {
        setTrackerMode(AlwaysOn);
    }

signals:
    void cur_x_y(int xVal, double yVal);

public:
    QString text = "";
    void triggerSignal(int x, double y)
    {
        Q_EMIT cur_x_y(x, y);
    }

    virtual QwtText trackerTextF(const QPointF& pos) const
    {
        // 坐标转换
        const QwtScaleMap xMap = plot()->canvasMap(QwtPlot::xBottom);
        const QwtScaleMap yMap = plot()->canvasMap(QwtPlot::yLeft);
        int xVal = pos.x();
        double yVal = pos.y();

        // 获取温度数据
        const Plot* myPlot = qobject_cast<const Plot*>(plot());
        const SpectrogramData* data = static_cast<const SpectrogramData*>(myPlot->spectrogram()->data());
        if (!data)
        {
            QString content;
            QTime time(0, 0, 0);
            time = time.addSecs(xVal);
            content += QString("时间: %1\n").arg(time.toString("hh:mm:ss"));
            content += QString("高度: %1 km\n").arg(yVal, 0, 'f', 2);
            content += QString("产品: nan");

            QwtText text(content);
            text.setBackgroundBrush(QColor(255, 255, 255, 200));
            text.setColor(Qt::darkBlue);
            // 触发信号（安全调用）
            const_cast<MyZoomer*>(this)->triggerSignal(static_cast<int>(xVal), yVal);
            return text;
        }

        const double temp = data->value(xVal, yVal);

        // 格式化文本
        QTime time(0, 0, 0);
        time = time.addSecs(xVal);

        QString content;
        content += QString("时间: %1\n").arg(time.toString("hh:mm:ss"));
        content += QString("高度: %1 km\n").arg(yVal, 0, 'f', 2);
        content += QString("产品: %1 ").arg(temp, 0, 'f', 3);

        QwtText text(content);
        text.setBackgroundBrush(QColor(255, 255, 255, 200));
        text.setColor(Qt::darkBlue);
        // 触发信号（安全调用）
        const_cast<MyZoomer*>(this)->triggerSignal(static_cast<int>(xVal), yVal);
        return text;
    }
};
// QwtScaleDraw 是 QWT 库中用于自定义坐标轴刻度绘制的重要类，其核心功能是为图表提供灵活的坐标轴显示控制。
// 组成元素​​：
// ​​刻度线​​（Ticks）：主刻度线和次刻度线；
// ​​标签​​（Labels）：刻度对应的数值或文本；
// ​​主干线​​（Backbone）：坐标轴的主轴线。
class TimeScaleDraw : public QwtScaleDraw
{
public:
    virtual QwtText label(double value) const override
    {
        QwtText temp;
        if (static_cast<int>(value) % 86400 == 0 && value != 0)
        {
            QString data = "24:00";
            temp = data;
        }
        // 将秒数转换为 QTime
        else
        {
            QTime time = QTime(0, 0, 0).addSecs(static_cast<int>(value));
            // 格式化显示为 HH:mm:ss
            temp = time.toString("HH:mm");
        }
        //        temp.setColor(QColor(0,0,145));
        return temp;
    }
};
/*y轴左侧刻度线单位*/
class yleftScaleDraw : public QwtScaleDraw
{
public:
    virtual QwtText label(double value) const override
    {
        const QwtScaleDiv& scaleDiv1 = scaleDiv();

        // 获取最大刻度值（使用qFuzzyCompare处理浮点精度）
        const double maxValue = scaleDiv1.upperBound();
        if (qFuzzyCompare(value, maxValue))
        {
            return QString::number(value) + "km";
        }
        return QString::number(value);
    }
};
/*y轴右侧刻度线单位*/
class yrightScaleDraw : public QwtScaleDraw
{
public:
    yrightScaleDraw(QString data)
    {
        name = data;
    }
    ~yrightScaleDraw()
    {
    }
    virtual QwtText label(double value) const override
    {

        const QwtScaleDiv& scaleDiv1 = scaleDiv();

        // 获取最大刻度值（使用qFuzzyCompare处理浮点精度）
        const double maxValue = scaleDiv1.upperBound();

        // 仅当当前值是最大值时显示带单位的标签
        if (qFuzzyCompare(value, maxValue))
        {

            if (name == "温度")
            {
                QwtText text;
                text.setText("<center>" + QString::number(value) + "<br>℃" + "</center>"); // 分两行显示
                // text.setFont(QFont("Arial", 10));
                return text;
            }
            else if (name == "水汽")
            {
                QwtText text;
                text.setText("<center>" + QString::number(value) + "<br>g/m3" + "</center>"); // 分两行显示
                // text.setFont(QFont("Arial", 10));
                return text;
            }
            else if (name == "湿度")
            {
                QwtText text;
                text.setText("<center>" + QString::number(value) + "<br>%" + "</center>"); // 分两行显示
                // text.setFont(QFont("Arial", 10));
                return text;
            }
            else if (name == "温度_湿度")
            {
                QwtText text;
                text.setText("<center>" + QString::number(value) + "<br>%" + "</center>"); // 分两行显示
                // text.setFont(QFont("Arial", 10));
                return text;
            }
        }

        return QwtText(QString::number(value));
    }
    QString name;
};

/*自定义绘图颜色类*/
class LinearColorMapRGB : public QwtLinearColorMap
{
public:
    LinearColorMapRGB() : QwtLinearColorMap(QColor(0, 0, 145), QColor(136, 0, 0), QwtColorMap::RGB)
    {

        // setNanColor(Qt::gray); // 设置缺失值颜色
        //  增加颜色过渡点
        addColorStop(0.1, QColor(0, 2, 252));     // 深蓝
        addColorStop(0.2, QColor(0, 90, 253));    // 青
        addColorStop(0.3, QColor(0, 188, 253));   // 青
        addColorStop(0.4, QColor(30, 254, 223));  // 青
        addColorStop(0.5, QColor(133, 255, 120)); // 绿
        addColorStop(0.6, QColor(240, 254, 15));  // 绿
        addColorStop(0.7, QColor(255, 172, 2));   // 黄
        addColorStop(0.8, QColor(255, 76, 1));    // 黄
        addColorStop(0.9, QColor(255, 0, 0));     // 橙红
    }

    // 参数化构造函数
    LinearColorMapRGB(const QColor& startColor, const QColor& endColor,
        const QVector<QPair<double, QColor>>& colorStops)
        : QwtLinearColorMap(startColor, endColor, QwtColorMap::RGB)
    {
        // 添加自定义颜色过渡点
        for (const auto& stop : colorStops)
        {
            addColorStop(stop.first, stop.second);
        }
    }
    //     void set_LinearColorMap(QVector<QColor> data)
    //    {
    //        QVector<double> stops = colorStops();
    //            stops.clear();
    //        for(int i=0;i<data.size();i++)
    //        {
    //            addColorStop((1.0/(data.size())*data.size()+2), data[i]);   // 深蓝
    //        }
    //    }
};

class LinearColorMapIndexed : public QwtLinearColorMap
{
public:
    LinearColorMapIndexed() : QwtLinearColorMap(Qt::blue, Qt::red, QwtColorMap::RGB) // 修改起始颜色为蓝色
    {
        addColorStop(0.2, QColor(0, 255, 255)); // 青色
        addColorStop(0.4, Qt::green);           // 绿色
        addColorStop(0.6, Qt::yellow);          // 黄色
        addColorStop(0.8, QColor(255, 165, 0)); // 橙色（可选过渡色）
    }
};

class HueColorMap : public QwtColorMap
{
public:
    // class backported from Qwt 6.2

    HueColorMap() : d_hue1(0),
        d_hue2(359),
        d_saturation(150),
        d_value(200)
    {
        updateTable();
    }

    virtual QRgb rgb(const QwtInterval& interval, double value) const
    {
        if (qIsNaN(value))
            return 0u;

        const double width = interval.width();
        if (width <= 0)
            return 0u;

        if (value <= interval.minValue())
            return d_rgbMin;

        if (value >= interval.maxValue())
            return d_rgbMax;

        const double ratio = (value - interval.minValue()) / width;
        int hue = d_hue1 + qRound(ratio * (d_hue2 - d_hue1));

        if (hue >= 360)
        {
            hue -= 360;

            if (hue >= 360)
                hue = hue % 360;
        }

        return d_rgbTable[hue];
    }

    virtual unsigned char colorIndex(const QwtInterval&, double) const
    {
        // we don't support indexed colors
        return 0;
    }

private:
    void updateTable()
    {
        for (int i = 0; i < 360; i++)
            d_rgbTable[i] = QColor::fromHsv(i, d_saturation, d_value).rgb();

        d_rgbMin = d_rgbTable[d_hue1 % 360];
        d_rgbMax = d_rgbTable[d_hue2 % 360];
    }

    int d_hue1, d_hue2, d_saturation, d_value;
    QRgb d_rgbMin, d_rgbMax, d_rgbTable[360];
};

class AlphaColorMap : public QwtAlphaColorMap
{
public:
    AlphaColorMap()
    {
        // setColor( QColor("DarkSalmon") );
        setColor(QColor("SteelBlue"));
    }
};
