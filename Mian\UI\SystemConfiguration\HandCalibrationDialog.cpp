#include "HandCalibrationDialog.h"
#include "ui_HandCalibrationDialog.h"
#include "tools/Configdata/CSystemConfigData.h"
HandCalibrationDialog::HandCalibrationDialog(QWidget* parent) :
    QDialog(parent),
    ui(new Ui::HandCalibrationDialog)
{
    ui->setupUi(this);
    changesty = new ChangeSysTimer(this);
}

HandCalibrationDialog::~HandCalibrationDialog()
{
    delete ui;
}

void HandCalibrationDialog::setUiParam()
{
    ui->doubleSpinBox_year->setValue(systemconfigfdata->systemdata.year_data);
    ui->doubleSpinBox_moon->setValue(systemconfigfdata->systemdata.moon_data);
    ui->doubleSpinBox_day->setValue(systemconfigfdata->systemdata.day_data);
    ui->doubleSpinBox_hour->setValue(systemconfigfdata->systemdata.hour_data);
    ui->doubleSpinBox_minute->setValue(systemconfigfdata->systemdata.minute_data);
    ui->doubleSpinBox_second->setValue(systemconfigfdata->systemdata.second_data);
}

void HandCalibrationDialog::getUiParam()
{
    systemconfigfdata->systemdata.year_data = ui->doubleSpinBox_year->value();
    systemconfigfdata->systemdata.moon_data = ui->doubleSpinBox_moon->value();
    systemconfigfdata->systemdata.day_data = ui->doubleSpinBox_day->value();
    systemconfigfdata->systemdata.hour_data = ui->doubleSpinBox_hour->value();
    systemconfigfdata->systemdata.minute_data = ui->doubleSpinBox_minute->value();
    systemconfigfdata->systemdata.second_data = ui->doubleSpinBox_second->value();

    systemconfigfdata->saveToFile();
}

void HandCalibrationDialog::slot_hand_data()
{
    this->exec();
}

void HandCalibrationDialog::on_pushButton_clicked()
{
    getUiParam();

    DateTime data;
    data.year = ui->doubleSpinBox_year->value();
    data.month = ui->doubleSpinBox_moon->value();
    data.day = ui->doubleSpinBox_day->value();
    data.hour = ui->doubleSpinBox_hour->value();
    data.minute = ui->doubleSpinBox_minute->value();
    data.second = ui->doubleSpinBox_second->value();

    changesty->setSystemTime(data);

}
