{"configurations": [{"directories": [{"build": ".", "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "EigenDemos", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "demos::@6890427a1f51a3e7e1df", "jsonFile": "target-demos-Debug-2d555229045a9bd8e8fa.json", "name": "demos", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/demos/out/build/x64-Debug", "source": "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/demos"}, "version": {"major": 2, "minor": 2}}