/********************************************************************************
** Form generated from reading UI file 'FMGWidget.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FMGWIDGET_H
#define UI_FMGWIDGET_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QTableView>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include "qcustomplot.h"

QT_BEGIN_NAMESPACE

class Ui_FMGWidget
{
public:
    QHBoxLayout *horizontalLayout_49;
    QWidget *widget_left;
    QVBoxLayout *verticalLayout;
    QFrame *line_6;
    QWidget *widget_swtich_button;
    QHBoxLayout *horizontalLayout_48;
    QFrame *line_5;
    QPushButton *pushButton_weather_six;
    QFrame *line;
    QPushButton *pushButton_k_voltage;
    QPushButton *pushButton_v_voltage;
    QPushButton *pushButton_k_temp;
    QPushButton *pushButton_v_temp;
    QPushButton *pushButton_all_temp_voltage;
    QFrame *line_2;
    QPushButton *pushButton_k_light;
    QPushButton *pushButton_v_light;
    QPushButton *pushButton_rain;
    QPushButton *pushButton_normal_temp;
    QPushButton *pushButton_all_light;
    QFrame *line_4;
    QPushButton *pushButton_k_show_data;
    QPushButton *pushButton_v_show_data;
    QSpacerItem *verticalSpacer;
    QWidget *widget;
    QHBoxLayout *horizontalLayout_12;
    QSplitter *splitter_3;
    QWidget *widget_plot;
    QVBoxLayout *verticalLayout_3;
    QStackedWidget *stackedWidget_FMG;
    QWidget *page_atmosphere_six_element;
    QVBoxLayout *verticalLayout_14;
    QCustomPlot *widget_temp;
    QCustomPlot *widget_humidity;
    QCustomPlot *widget_pressure;
    QCustomPlot *widget_wind_speed;
    QCustomPlot *widget_wind_direction;
    QCustomPlot *widget_rain;
    QWidget *page_init_data_monitor;
    QVBoxLayout *verticalLayout_7;
    QHBoxLayout *horizontalLayout_2;
    QCustomPlot *widget_k_voltage;
    QWidget *widget_k_cebian_hz;
    QGridLayout *gridLayout_8;
    QHBoxLayout *horizontalLayout_8;
    QPushButton *pushButton_k_3000;
    QLabel *label_k_3000;
    QHBoxLayout *horizontalLayout;
    QPushButton *pushButton_k_2224;
    QLabel *label_k_2224;
    QHBoxLayout *horizontalLayout_11;
    QPushButton *pushButton_k_2544;
    QLabel *label_k_2544;
    QHBoxLayout *horizontalLayout_13;
    QPushButton *pushButton_k_2304;
    QLabel *label_k_2304;
    QHBoxLayout *horizontalLayout_50;
    QPushButton *pushButton_k_2384;
    QLabel *label_k_2384;
    QPushButton *pushButton_k_all_show;
    QHBoxLayout *horizontalLayout_9;
    QPushButton *pushButton_k_2784;
    QLabel *label_k_2784;
    QHBoxLayout *horizontalLayout_7;
    QPushButton *pushButton_k_3140;
    QLabel *label_k_3140;
    QPushButton *pushButton_k_clear;
    QPushButton *pushButton_k_stop;
    QHBoxLayout *horizontalLayout_10;
    QPushButton *pushButton_k_2624;
    QLabel *label_k_2624;
    QCustomPlot *widget_v_voltage;
    QWidget *widget_v_cebian_hz;
    QGridLayout *gridLayout_7;
    QHBoxLayout *horizontalLayout_14;
    QPushButton *pushButton_v_5126;
    QLabel *label_v_5126;
    QHBoxLayout *horizontalLayout_6;
    QPushButton *pushButton_v_5228;
    QLabel *label_v_5228;
    QHBoxLayout *horizontalLayout_15;
    QPushButton *pushButton_v_5386;
    QLabel *label_v_5386;
    QHBoxLayout *horizontalLayout_16;
    QPushButton *pushButton_v_5494;
    QLabel *label_v_5494;
    QHBoxLayout *horizontalLayout_17;
    QPushButton *pushButton_v_5550;
    QLabel *label_v_5550;
    QHBoxLayout *horizontalLayout_18;
    QPushButton *pushButton_v_5666;
    QLabel *label_v_5666;
    QHBoxLayout *horizontalLayout_19;
    QPushButton *pushButton_v_5730;
    QLabel *label_v_5730;
    QHBoxLayout *horizontalLayout_20;
    QPushButton *pushButton_v_5800;
    QLabel *label_v_5800;
    QPushButton *pushButton_v_stop;
    QPushButton *pushButton_v_all_show;
    QPushButton *pushButton_v_clear;
    QHBoxLayout *horizontalLayout_3;
    QCustomPlot *widget_k_temp;
    QWidget *widget_k_cebian_temp;
    QGridLayout *gridLayout_6;
    QHBoxLayout *horizontalLayout_40;
    QPushButton *pushButton_k_temp1;
    QLabel *label_k_temp1;
    QHBoxLayout *horizontalLayout_39;
    QPushButton *pushButton_k_temp2;
    QLabel *label_k_temp2;
    QHBoxLayout *horizontalLayout_38;
    QPushButton *pushButton_k_temp3;
    QLabel *label_k_temp3;
    QPushButton *pushButton_k_temp_stop;
    QPushButton *pushButton_k_temp_all_show;
    QPushButton *pushButton_k_temp_clear;
    QSpacerItem *verticalSpacer_4;
    QCustomPlot *widget_v_temp;
    QWidget *widget_v_cebian_temp;
    QGridLayout *gridLayout_5;
    QHBoxLayout *horizontalLayout_41;
    QPushButton *pushButton_v_temp1;
    QLabel *label_v_temp1;
    QHBoxLayout *horizontalLayout_42;
    QPushButton *pushButton_v_temp2;
    QLabel *label_v_temp2;
    QHBoxLayout *horizontalLayout_43;
    QPushButton *pushButton_v_temp3;
    QLabel *label_v_temp3;
    QPushButton *pushButton_v_temp_stop;
    QPushButton *pushButton_v_temp_all_show;
    QPushButton *pushButton_v_temp_clear;
    QSpacerItem *verticalSpacer_5;
    QWidget *page_light_data_monitor;
    QVBoxLayout *verticalLayout_16;
    QHBoxLayout *horizontalLayout_5;
    QCustomPlot *widget_k_light_temp;
    QWidget *widget_light_k_cebian_hz;
    QGridLayout *gridLayout;
    QHBoxLayout *horizontalLayout_28;
    QPushButton *pushButton_k_light_2224;
    QLabel *label_k_light_2224;
    QHBoxLayout *horizontalLayout_27;
    QPushButton *pushButton_k_light_2304;
    QLabel *label_k_light_2304;
    QHBoxLayout *horizontalLayout_26;
    QPushButton *pushButton_k_light_2384;
    QLabel *label_k_light_2384;
    QHBoxLayout *horizontalLayout_25;
    QPushButton *pushButton_k_light_2544;
    QLabel *label_k_light_2544;
    QHBoxLayout *horizontalLayout_24;
    QPushButton *pushButton_k_light_2624;
    QLabel *label_k_light_2624;
    QHBoxLayout *horizontalLayout_23;
    QPushButton *pushButton_k_light_2784;
    QLabel *label_k_light_2784;
    QHBoxLayout *horizontalLayout_22;
    QPushButton *pushButton_k_light_3000;
    QLabel *label_k_light_3000;
    QHBoxLayout *horizontalLayout_21;
    QPushButton *pushButton_k_light_3140;
    QLabel *label_k_light_3140;
    QPushButton *pushButton_k_light_stop;
    QPushButton *pushButton_k_light_all_show;
    QPushButton *pushButton_k_light_clear;
    QCustomPlot *widget_v_light_temp;
    QWidget *widget_light_v_cebian_hz;
    QGridLayout *gridLayout_2;
    QHBoxLayout *horizontalLayout_36;
    QPushButton *pushButton_v_light_5126;
    QLabel *label_v_light_5126;
    QHBoxLayout *horizontalLayout_35;
    QPushButton *pushButton_v_light_5228;
    QLabel *label_v_light_5228;
    QHBoxLayout *horizontalLayout_34;
    QPushButton *pushButton_v_light_5386;
    QLabel *label_v_light_5386;
    QHBoxLayout *horizontalLayout_33;
    QPushButton *pushButton_v_light_5494;
    QLabel *label_v_light_5494;
    QHBoxLayout *horizontalLayout_32;
    QPushButton *pushButton_v_light_5550;
    QLabel *label_v_light_5550;
    QHBoxLayout *horizontalLayout_31;
    QPushButton *pushButton_v_light_5666;
    QLabel *label_v_light_5666;
    QHBoxLayout *horizontalLayout_30;
    QPushButton *pushButton_v_light_5730;
    QLabel *label_v_light_5730;
    QHBoxLayout *horizontalLayout_29;
    QPushButton *pushButton_v_light_5800;
    QLabel *label_v_light_5800;
    QPushButton *pushButton_v_light_stop;
    QPushButton *pushButton_v_light_all_show;
    QPushButton *pushButton_v_light_clear;
    QHBoxLayout *horizontalLayout_4;
    QCustomPlot *widget_light_rain;
    QWidget *widget_light_cebian_rain;
    QGridLayout *gridLayout_3;
    QHBoxLayout *horizontalLayout_44;
    QPushButton *pushButton_light_rain;
    QLabel *label_light_rain;
    QPushButton *pushButton_light_rain_stop;
    QPushButton *pushButton_light_rain_all_show;
    QPushButton *pushButton_light_rain_clear;
    QSpacerItem *verticalSpacer_2;
    QCustomPlot *widget_light_temp;
    QWidget *widget_light_cebian_temp;
    QGridLayout *gridLayout_4;
    QHBoxLayout *horizontalLayout_45;
    QPushButton *pushButton_light_temp;
    QLabel *label_light_temp;
    QPushButton *pushButton_light_temp_stop;
    QPushButton *pushButton_light_temp_all_show;
    QPushButton *pushButton_light_temp_clear;
    QSpacerItem *verticalSpacer_3;
    QWidget *widget_kv_data;
    QHBoxLayout *horizontalLayout_47;
    QWidget *widget_k;
    QVBoxLayout *verticalLayout_11;
    QSplitter *splitter;
    QWidget *widget_6;
    QVBoxLayout *verticalLayout_4;
    QHBoxLayout *horizontalLayout_37;
    QLabel *label_k;
    QComboBox *comboBox_k_data;
    QSpacerItem *horizontalSpacer;
    QTableView *tableView_k;
    QWidget *widget_5;
    QVBoxLayout *verticalLayout_8;
    QLabel *label_k_2;
    QTableView *tableView_k_calibration;
    QWidget *widget_v;
    QVBoxLayout *verticalLayout_12;
    QSplitter *splitter_2;
    QWidget *widget_4;
    QVBoxLayout *verticalLayout_10;
    QHBoxLayout *horizontalLayout_46;
    QLabel *label_v;
    QComboBox *comboBox_v_data;
    QSpacerItem *horizontalSpacer_2;
    QTableView *tableView_v;
    QWidget *widget_3;
    QVBoxLayout *verticalLayout_9;
    QLabel *label_v_2;
    QTableView *tableView_v_calibration;

    void setupUi(QWidget *FMGWidget)
    {
        if (FMGWidget->objectName().isEmpty())
            FMGWidget->setObjectName(QString::fromUtf8("FMGWidget"));
        FMGWidget->resize(1730, 1066);
        FMGWidget->setStyleSheet(QString::fromUtf8("#line\n"
"{\n"
"background-color: rgb(170, 170, 255);\n"
"}\n"
"#line_2\n"
"{\n"
"background-color: rgb(170, 170, 255);\n"
"}\n"
"#line_3\n"
"{\n"
"background-color: rgb(170, 170, 255);\n"
"}\n"
"#line_4\n"
"{\n"
"background-color: rgb(170, 170, 255);\n"
"}\n"
"#line_5\n"
"{\n"
"background-color: rgb(170, 170, 255);\n"
"}\n"
"#line_6\n"
"{\n"
"background-color: rgb(170, 170, 255);\n"
"}\n"
"#FMGWidget\n"
"{\n"
"background-color: qlineargradient(spread:reflect, x1:0.0547264, y1:0.0568182, x2:1, y2:1, stop:0 rgba(36, 18, 88, 255), stop:1 rgba(255, 255, 255, 255));\n"
"}\n"
"#page_device_status\n"
"{\n"
"background-color: rgb(0, 40, 81);\n"
"}\n"
"\n"
"#page_atmosphere_six_element\n"
"{\n"
"background-color: rgb(0, 40, 81);\n"
"}\n"
"#page_init_data_monitor\n"
"{\n"
"background-color: rgb(0, 40, 81);\n"
"}\n"
"\n"
"#page_light_data_monitor\n"
"{\n"
"background-color: rgb(0, 40, 81);\n"
"}\n"
"QSplitter::handle\n"
"{\n"
"	background-color: rgb(160, 160, 160);\n"
"}\n"
"#widget\n"
"{\n"
"	background-color: rgb(0, 4"
                        "0, 81);\n"
"}\n"
"#widget_kv_data\n"
"{\n"
"	background-color: rgb(0, 40, 81);\n"
"}\n"
"#widget_k\n"
"{\n"
"	background-color: rgb(0, 40, 81);\n"
"}\n"
"#widget_v\n"
"{\n"
"	background-color: rgb(0, 40, 81);\n"
"}\n"
"#widget_3\n"
"{\n"
"	background-color: rgb(0, 40, 81);\n"
"}\n"
"#widget_4\n"
"{\n"
"	background-color: rgb(0, 40, 81);\n"
"}\n"
"#widget_plot\n"
"{\n"
"	background-color: rgb(0, 40, 81);\n"
"}\n"
"#widget_status\n"
"{\n"
"	background-color: rgb(0, 40, 81);\n"
"}\n"
"#widget_left\n"
"{\n"
"	background-color: rgb(0, 40, 81);\n"
"}\n"
"\n"
"QLineEdit\n"
"{background-color: rgb(74, 92, 124);color:white;}\n"
"\n"
"\n"
"\n"
"\n"
"#pushButton_init_data_monitor\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(64, 166, 0, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"\n"
"\n"
"\n"
"#pushButton_light_data_monitor\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(64, 166, 0, 255), stop:1 rgba(255, 255, 2"
                        "55, 255));color:white;border:none;}\n"
"\n"
"\n"
"\n"
"#pushButton_weather_six\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_weather_six::checked\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_weather_six::hover\n"
"{border:1px solid white;}\n"
"\n"
"\n"
"\n"
"#pushButton_k_voltage\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_k_voltage::checked\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_k_voltage::hover\n"
"{border:1px solid white;}\n"
"\n"
"#pushButton_v_voltage\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_v_voltage::checked\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255),"
                        " stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_v_voltage::hover\n"
"{border:1px solid white;}\n"
"\n"
"#pushButton_k_temp\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_k_temp::checked\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_k_temp::hover\n"
"{border:1px solid white;}\n"
"\n"
"#pushButton_v_temp\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_v_temp::checked\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_v_temp::hover\n"
"{border:1px solid white;}\n"
"\n"
"\n"
"#pushButton_k_temp_voltage\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_k_temp_voltage::checked\n"
"{background-color: qlineargradient(spread:pa"
                        "d, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_k_temp_voltage::hover\n"
"{border:1px solid white;}\n"
"\n"
"#pushButton_v_temp_voltage\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_v_temp_voltage::checked\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_v_temp_voltage::hover\n"
"{border:1px solid white;}\n"
"\n"
"#pushButton_all_temp_voltage\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_all_temp_voltage::checked\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_all_temp_voltage::hover\n"
"{border:1px solid white;}\n"
"\n"
"\n"
"\n"
"\n"
"#pushButton_k_light\n"
"{background-color: rgb(74, 92, "
                        "124);color:white;border: none;}\n"
"#pushButton_k_light::checked\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_k_light::hover\n"
"{border:1px solid white;}\n"
"\n"
"#pushButton_v_light\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_v_light::checked\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_v_light::hover\n"
"{border:1px solid white;}\n"
"\n"
"#pushButton_k_v_light\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_k_v_light::checked\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_k_v_light::hover\n"
"{border:1px solid white;}\n"
"\n"
"\n"
"\n"
""
                        "#pushButton_rain\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_rain::checked\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_rain::hover\n"
"{border:1px solid white;}\n"
"\n"
"#pushButton_normal_temp\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_normal_temp::checked\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_normal_temp::hover\n"
"{border:1px solid white;}\n"
"\n"
"#pushButton_all_light\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_all_light::checked\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_all_"
                        "light::hover\n"
"{border:1px solid white;}\n"
"\n"
"\n"
"#pushButton_k_show_data\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_k_show_data:pressed\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_k_show_data::hover\n"
"{border:1px solid white;}\n"
"\n"
"\n"
"#pushButton_v_show_data\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_v_show_data:pressed\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_v_show_data::hover\n"
"{border:1px solid white;}\n"
"\n"
"\n"
"#pushButton_sensor_status\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"#pushButton_sensor_status:pressed\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 9"
                        "4, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"#pushButton_sensor_status::hover\n"
"{border:1px solid white;}\n"
"\n"
"\n"
"#widget\n"
"{\n"
"background-color: rgb(62, 62, 62);\n"
"}\n"
"QComboBox\n"
"{color:white;background-color:rgb(74, 92, 124);}\n"
"QComboBox::drop-down\n"
"{color:white;\n"
"image: url(:/VectorIcon/downarrows.png);\n"
"}\n"
"QComboBox QAbstractItemView\n"
"{color:white;background-color:rgb(74, 92, 124);selecttion-background-color:rgb(74, 92, 124);}\n"
"\n"
"	\n"
""));
        horizontalLayout_49 = new QHBoxLayout(FMGWidget);
        horizontalLayout_49->setObjectName(QString::fromUtf8("horizontalLayout_49"));
        widget_left = new QWidget(FMGWidget);
        widget_left->setObjectName(QString::fromUtf8("widget_left"));
        QSizePolicy sizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(widget_left->sizePolicy().hasHeightForWidth());
        widget_left->setSizePolicy(sizePolicy);
        widget_left->setStyleSheet(QString::fromUtf8(""));
        verticalLayout = new QVBoxLayout(widget_left);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        line_6 = new QFrame(widget_left);
        line_6->setObjectName(QString::fromUtf8("line_6"));
        line_6->setStyleSheet(QString::fromUtf8(""));
        line_6->setFrameShape(QFrame::HLine);
        line_6->setFrameShadow(QFrame::Sunken);

        verticalLayout->addWidget(line_6);

        widget_swtich_button = new QWidget(widget_left);
        widget_swtich_button->setObjectName(QString::fromUtf8("widget_swtich_button"));
        widget_swtich_button->setMinimumSize(QSize(170, 45));
        widget_swtich_button->setStyleSheet(QString::fromUtf8(""));
        horizontalLayout_48 = new QHBoxLayout(widget_swtich_button);
        horizontalLayout_48->setObjectName(QString::fromUtf8("horizontalLayout_48"));

        verticalLayout->addWidget(widget_swtich_button);

        line_5 = new QFrame(widget_left);
        line_5->setObjectName(QString::fromUtf8("line_5"));
        line_5->setStyleSheet(QString::fromUtf8(""));
        line_5->setFrameShape(QFrame::HLine);
        line_5->setFrameShadow(QFrame::Sunken);

        verticalLayout->addWidget(line_5);

        pushButton_weather_six = new QPushButton(widget_left);
        pushButton_weather_six->setObjectName(QString::fromUtf8("pushButton_weather_six"));
        QSizePolicy sizePolicy1(QSizePolicy::Minimum, QSizePolicy::Fixed);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(pushButton_weather_six->sizePolicy().hasHeightForWidth());
        pushButton_weather_six->setSizePolicy(sizePolicy1);
        pushButton_weather_six->setMinimumSize(QSize(0, 35));
        QFont font;
        font.setPointSize(12);
        pushButton_weather_six->setFont(font);

        verticalLayout->addWidget(pushButton_weather_six);

        line = new QFrame(widget_left);
        line->setObjectName(QString::fromUtf8("line"));
        line->setStyleSheet(QString::fromUtf8(""));
        line->setFrameShape(QFrame::HLine);
        line->setFrameShadow(QFrame::Sunken);

        verticalLayout->addWidget(line);

        pushButton_k_voltage = new QPushButton(widget_left);
        pushButton_k_voltage->setObjectName(QString::fromUtf8("pushButton_k_voltage"));
        pushButton_k_voltage->setMinimumSize(QSize(0, 35));
        pushButton_k_voltage->setFont(font);

        verticalLayout->addWidget(pushButton_k_voltage);

        pushButton_v_voltage = new QPushButton(widget_left);
        pushButton_v_voltage->setObjectName(QString::fromUtf8("pushButton_v_voltage"));
        pushButton_v_voltage->setMinimumSize(QSize(0, 35));
        pushButton_v_voltage->setFont(font);

        verticalLayout->addWidget(pushButton_v_voltage);

        pushButton_k_temp = new QPushButton(widget_left);
        pushButton_k_temp->setObjectName(QString::fromUtf8("pushButton_k_temp"));
        pushButton_k_temp->setMinimumSize(QSize(0, 35));
        pushButton_k_temp->setFont(font);

        verticalLayout->addWidget(pushButton_k_temp);

        pushButton_v_temp = new QPushButton(widget_left);
        pushButton_v_temp->setObjectName(QString::fromUtf8("pushButton_v_temp"));
        pushButton_v_temp->setMinimumSize(QSize(0, 35));
        pushButton_v_temp->setFont(font);

        verticalLayout->addWidget(pushButton_v_temp);

        pushButton_all_temp_voltage = new QPushButton(widget_left);
        pushButton_all_temp_voltage->setObjectName(QString::fromUtf8("pushButton_all_temp_voltage"));
        pushButton_all_temp_voltage->setMinimumSize(QSize(0, 35));
        pushButton_all_temp_voltage->setFont(font);

        verticalLayout->addWidget(pushButton_all_temp_voltage);

        line_2 = new QFrame(widget_left);
        line_2->setObjectName(QString::fromUtf8("line_2"));
        line_2->setStyleSheet(QString::fromUtf8(""));
        line_2->setFrameShape(QFrame::HLine);
        line_2->setFrameShadow(QFrame::Sunken);

        verticalLayout->addWidget(line_2);

        pushButton_k_light = new QPushButton(widget_left);
        pushButton_k_light->setObjectName(QString::fromUtf8("pushButton_k_light"));
        pushButton_k_light->setMinimumSize(QSize(0, 35));
        pushButton_k_light->setFont(font);

        verticalLayout->addWidget(pushButton_k_light);

        pushButton_v_light = new QPushButton(widget_left);
        pushButton_v_light->setObjectName(QString::fromUtf8("pushButton_v_light"));
        pushButton_v_light->setMinimumSize(QSize(0, 35));
        pushButton_v_light->setFont(font);

        verticalLayout->addWidget(pushButton_v_light);

        pushButton_rain = new QPushButton(widget_left);
        pushButton_rain->setObjectName(QString::fromUtf8("pushButton_rain"));
        pushButton_rain->setMinimumSize(QSize(0, 35));
        pushButton_rain->setFont(font);

        verticalLayout->addWidget(pushButton_rain);

        pushButton_normal_temp = new QPushButton(widget_left);
        pushButton_normal_temp->setObjectName(QString::fromUtf8("pushButton_normal_temp"));
        pushButton_normal_temp->setMinimumSize(QSize(0, 35));
        pushButton_normal_temp->setFont(font);

        verticalLayout->addWidget(pushButton_normal_temp);

        pushButton_all_light = new QPushButton(widget_left);
        pushButton_all_light->setObjectName(QString::fromUtf8("pushButton_all_light"));
        pushButton_all_light->setMinimumSize(QSize(0, 35));
        pushButton_all_light->setFont(font);

        verticalLayout->addWidget(pushButton_all_light);

        line_4 = new QFrame(widget_left);
        line_4->setObjectName(QString::fromUtf8("line_4"));
        line_4->setStyleSheet(QString::fromUtf8(""));
        line_4->setFrameShape(QFrame::HLine);
        line_4->setFrameShadow(QFrame::Sunken);

        verticalLayout->addWidget(line_4);

        pushButton_k_show_data = new QPushButton(widget_left);
        pushButton_k_show_data->setObjectName(QString::fromUtf8("pushButton_k_show_data"));
        pushButton_k_show_data->setMinimumSize(QSize(0, 35));
        pushButton_k_show_data->setFont(font);

        verticalLayout->addWidget(pushButton_k_show_data);

        pushButton_v_show_data = new QPushButton(widget_left);
        pushButton_v_show_data->setObjectName(QString::fromUtf8("pushButton_v_show_data"));
        pushButton_v_show_data->setMinimumSize(QSize(0, 35));
        pushButton_v_show_data->setFont(font);

        verticalLayout->addWidget(pushButton_v_show_data);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout->addItem(verticalSpacer);


        horizontalLayout_49->addWidget(widget_left);

        widget = new QWidget(FMGWidget);
        widget->setObjectName(QString::fromUtf8("widget"));
        horizontalLayout_12 = new QHBoxLayout(widget);
        horizontalLayout_12->setObjectName(QString::fromUtf8("horizontalLayout_12"));
        splitter_3 = new QSplitter(widget);
        splitter_3->setObjectName(QString::fromUtf8("splitter_3"));
        splitter_3->setOrientation(Qt::Horizontal);
        widget_plot = new QWidget(splitter_3);
        widget_plot->setObjectName(QString::fromUtf8("widget_plot"));
        sizePolicy.setHeightForWidth(widget_plot->sizePolicy().hasHeightForWidth());
        widget_plot->setSizePolicy(sizePolicy);
        widget_plot->setMinimumSize(QSize(810, 1022));
        widget_plot->setStyleSheet(QString::fromUtf8(""));
        verticalLayout_3 = new QVBoxLayout(widget_plot);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        stackedWidget_FMG = new QStackedWidget(widget_plot);
        stackedWidget_FMG->setObjectName(QString::fromUtf8("stackedWidget_FMG"));
        stackedWidget_FMG->setStyleSheet(QString::fromUtf8(""));
        page_atmosphere_six_element = new QWidget();
        page_atmosphere_six_element->setObjectName(QString::fromUtf8("page_atmosphere_six_element"));
        verticalLayout_14 = new QVBoxLayout(page_atmosphere_six_element);
        verticalLayout_14->setObjectName(QString::fromUtf8("verticalLayout_14"));
        widget_temp = new QCustomPlot(page_atmosphere_six_element);
        widget_temp->setObjectName(QString::fromUtf8("widget_temp"));

        verticalLayout_14->addWidget(widget_temp);

        widget_humidity = new QCustomPlot(page_atmosphere_six_element);
        widget_humidity->setObjectName(QString::fromUtf8("widget_humidity"));

        verticalLayout_14->addWidget(widget_humidity);

        widget_pressure = new QCustomPlot(page_atmosphere_six_element);
        widget_pressure->setObjectName(QString::fromUtf8("widget_pressure"));

        verticalLayout_14->addWidget(widget_pressure);

        widget_wind_speed = new QCustomPlot(page_atmosphere_six_element);
        widget_wind_speed->setObjectName(QString::fromUtf8("widget_wind_speed"));

        verticalLayout_14->addWidget(widget_wind_speed);

        widget_wind_direction = new QCustomPlot(page_atmosphere_six_element);
        widget_wind_direction->setObjectName(QString::fromUtf8("widget_wind_direction"));

        verticalLayout_14->addWidget(widget_wind_direction);

        widget_rain = new QCustomPlot(page_atmosphere_six_element);
        widget_rain->setObjectName(QString::fromUtf8("widget_rain"));

        verticalLayout_14->addWidget(widget_rain);

        stackedWidget_FMG->addWidget(page_atmosphere_six_element);
        page_init_data_monitor = new QWidget();
        page_init_data_monitor->setObjectName(QString::fromUtf8("page_init_data_monitor"));
        verticalLayout_7 = new QVBoxLayout(page_init_data_monitor);
        verticalLayout_7->setObjectName(QString::fromUtf8("verticalLayout_7"));
        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        widget_k_voltage = new QCustomPlot(page_init_data_monitor);
        widget_k_voltage->setObjectName(QString::fromUtf8("widget_k_voltage"));

        horizontalLayout_2->addWidget(widget_k_voltage);

        widget_k_cebian_hz = new QWidget(page_init_data_monitor);
        widget_k_cebian_hz->setObjectName(QString::fromUtf8("widget_k_cebian_hz"));
        widget_k_cebian_hz->setStyleSheet(QString::fromUtf8("\n"
"#pushButton_k_2224\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"\n"
"#pushButton_k_2304\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_k_2384\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_k_2544\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_k_2624\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_k_2784\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_k_3000\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_k_3140\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}"));
        gridLayout_8 = new QGridLayout(widget_k_cebian_hz);
        gridLayout_8->setObjectName(QString::fromUtf8("gridLayout_8"));
        horizontalLayout_8 = new QHBoxLayout();
        horizontalLayout_8->setObjectName(QString::fromUtf8("horizontalLayout_8"));
        pushButton_k_3000 = new QPushButton(widget_k_cebian_hz);
        pushButton_k_3000->setObjectName(QString::fromUtf8("pushButton_k_3000"));
        pushButton_k_3000->setMinimumSize(QSize(52, 28));
        QFont font1;
        font1.setPointSize(8);
        pushButton_k_3000->setFont(font1);

        horizontalLayout_8->addWidget(pushButton_k_3000);

        label_k_3000 = new QLabel(widget_k_cebian_hz);
        label_k_3000->setObjectName(QString::fromUtf8("label_k_3000"));
        label_k_3000->setMinimumSize(QSize(0, 28));

        horizontalLayout_8->addWidget(label_k_3000);

        horizontalLayout_8->setStretch(1, 1);

        gridLayout_8->addLayout(horizontalLayout_8, 6, 0, 1, 1);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        pushButton_k_2224 = new QPushButton(widget_k_cebian_hz);
        pushButton_k_2224->setObjectName(QString::fromUtf8("pushButton_k_2224"));
        pushButton_k_2224->setMinimumSize(QSize(52, 28));
        pushButton_k_2224->setFont(font1);

        horizontalLayout->addWidget(pushButton_k_2224);

        label_k_2224 = new QLabel(widget_k_cebian_hz);
        label_k_2224->setObjectName(QString::fromUtf8("label_k_2224"));
        label_k_2224->setMinimumSize(QSize(0, 28));

        horizontalLayout->addWidget(label_k_2224);

        horizontalLayout->setStretch(1, 1);

        gridLayout_8->addLayout(horizontalLayout, 0, 0, 1, 1);

        horizontalLayout_11 = new QHBoxLayout();
        horizontalLayout_11->setObjectName(QString::fromUtf8("horizontalLayout_11"));
        pushButton_k_2544 = new QPushButton(widget_k_cebian_hz);
        pushButton_k_2544->setObjectName(QString::fromUtf8("pushButton_k_2544"));
        pushButton_k_2544->setMinimumSize(QSize(52, 28));
        pushButton_k_2544->setFont(font1);

        horizontalLayout_11->addWidget(pushButton_k_2544);

        label_k_2544 = new QLabel(widget_k_cebian_hz);
        label_k_2544->setObjectName(QString::fromUtf8("label_k_2544"));
        label_k_2544->setMinimumSize(QSize(0, 28));

        horizontalLayout_11->addWidget(label_k_2544);

        horizontalLayout_11->setStretch(1, 1);

        gridLayout_8->addLayout(horizontalLayout_11, 3, 0, 1, 1);

        horizontalLayout_13 = new QHBoxLayout();
        horizontalLayout_13->setObjectName(QString::fromUtf8("horizontalLayout_13"));
        pushButton_k_2304 = new QPushButton(widget_k_cebian_hz);
        pushButton_k_2304->setObjectName(QString::fromUtf8("pushButton_k_2304"));
        pushButton_k_2304->setMinimumSize(QSize(52, 28));
        pushButton_k_2304->setFont(font1);

        horizontalLayout_13->addWidget(pushButton_k_2304);

        label_k_2304 = new QLabel(widget_k_cebian_hz);
        label_k_2304->setObjectName(QString::fromUtf8("label_k_2304"));
        label_k_2304->setMinimumSize(QSize(0, 28));

        horizontalLayout_13->addWidget(label_k_2304);

        horizontalLayout_13->setStretch(1, 1);

        gridLayout_8->addLayout(horizontalLayout_13, 1, 0, 1, 1);

        horizontalLayout_50 = new QHBoxLayout();
        horizontalLayout_50->setObjectName(QString::fromUtf8("horizontalLayout_50"));
        pushButton_k_2384 = new QPushButton(widget_k_cebian_hz);
        pushButton_k_2384->setObjectName(QString::fromUtf8("pushButton_k_2384"));
        pushButton_k_2384->setMinimumSize(QSize(52, 28));
        pushButton_k_2384->setFont(font1);

        horizontalLayout_50->addWidget(pushButton_k_2384);

        label_k_2384 = new QLabel(widget_k_cebian_hz);
        label_k_2384->setObjectName(QString::fromUtf8("label_k_2384"));
        label_k_2384->setMinimumSize(QSize(0, 28));

        horizontalLayout_50->addWidget(label_k_2384);

        horizontalLayout_50->setStretch(1, 1);

        gridLayout_8->addLayout(horizontalLayout_50, 2, 0, 1, 1);

        pushButton_k_all_show = new QPushButton(widget_k_cebian_hz);
        pushButton_k_all_show->setObjectName(QString::fromUtf8("pushButton_k_all_show"));
        pushButton_k_all_show->setMinimumSize(QSize(0, 28));
        pushButton_k_all_show->setFont(font1);
        pushButton_k_all_show->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_8->addWidget(pushButton_k_all_show, 9, 0, 1, 1);

        horizontalLayout_9 = new QHBoxLayout();
        horizontalLayout_9->setObjectName(QString::fromUtf8("horizontalLayout_9"));
        pushButton_k_2784 = new QPushButton(widget_k_cebian_hz);
        pushButton_k_2784->setObjectName(QString::fromUtf8("pushButton_k_2784"));
        pushButton_k_2784->setMinimumSize(QSize(52, 28));
        pushButton_k_2784->setFont(font1);

        horizontalLayout_9->addWidget(pushButton_k_2784);

        label_k_2784 = new QLabel(widget_k_cebian_hz);
        label_k_2784->setObjectName(QString::fromUtf8("label_k_2784"));
        label_k_2784->setMinimumSize(QSize(0, 28));

        horizontalLayout_9->addWidget(label_k_2784);

        horizontalLayout_9->setStretch(1, 1);

        gridLayout_8->addLayout(horizontalLayout_9, 5, 0, 1, 1);

        horizontalLayout_7 = new QHBoxLayout();
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        pushButton_k_3140 = new QPushButton(widget_k_cebian_hz);
        pushButton_k_3140->setObjectName(QString::fromUtf8("pushButton_k_3140"));
        pushButton_k_3140->setMinimumSize(QSize(52, 28));
        pushButton_k_3140->setFont(font1);

        horizontalLayout_7->addWidget(pushButton_k_3140);

        label_k_3140 = new QLabel(widget_k_cebian_hz);
        label_k_3140->setObjectName(QString::fromUtf8("label_k_3140"));
        label_k_3140->setMinimumSize(QSize(0, 28));

        horizontalLayout_7->addWidget(label_k_3140);

        horizontalLayout_7->setStretch(1, 1);

        gridLayout_8->addLayout(horizontalLayout_7, 7, 0, 1, 1);

        pushButton_k_clear = new QPushButton(widget_k_cebian_hz);
        pushButton_k_clear->setObjectName(QString::fromUtf8("pushButton_k_clear"));
        pushButton_k_clear->setMinimumSize(QSize(0, 28));
        pushButton_k_clear->setFont(font1);
        pushButton_k_clear->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_8->addWidget(pushButton_k_clear, 10, 0, 1, 1);

        pushButton_k_stop = new QPushButton(widget_k_cebian_hz);
        pushButton_k_stop->setObjectName(QString::fromUtf8("pushButton_k_stop"));
        pushButton_k_stop->setMinimumSize(QSize(0, 28));
        pushButton_k_stop->setFont(font1);
        pushButton_k_stop->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_8->addWidget(pushButton_k_stop, 8, 0, 1, 1);

        horizontalLayout_10 = new QHBoxLayout();
        horizontalLayout_10->setObjectName(QString::fromUtf8("horizontalLayout_10"));
        pushButton_k_2624 = new QPushButton(widget_k_cebian_hz);
        pushButton_k_2624->setObjectName(QString::fromUtf8("pushButton_k_2624"));
        pushButton_k_2624->setMinimumSize(QSize(52, 28));
        pushButton_k_2624->setFont(font1);

        horizontalLayout_10->addWidget(pushButton_k_2624);

        label_k_2624 = new QLabel(widget_k_cebian_hz);
        label_k_2624->setObjectName(QString::fromUtf8("label_k_2624"));
        label_k_2624->setMinimumSize(QSize(0, 28));

        horizontalLayout_10->addWidget(label_k_2624);

        horizontalLayout_10->setStretch(1, 1);

        gridLayout_8->addLayout(horizontalLayout_10, 4, 0, 1, 1);


        horizontalLayout_2->addWidget(widget_k_cebian_hz);

        widget_v_voltage = new QCustomPlot(page_init_data_monitor);
        widget_v_voltage->setObjectName(QString::fromUtf8("widget_v_voltage"));

        horizontalLayout_2->addWidget(widget_v_voltage);

        widget_v_cebian_hz = new QWidget(page_init_data_monitor);
        widget_v_cebian_hz->setObjectName(QString::fromUtf8("widget_v_cebian_hz"));
        widget_v_cebian_hz->setStyleSheet(QString::fromUtf8("\n"
"\n"
"#pushButton_v_5126\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"\n"
"#pushButton_v_5228\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_v_5386\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_v_5494\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_v_5550\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_v_5666\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_v_5730\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_v_5800\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}"));
        gridLayout_7 = new QGridLayout(widget_v_cebian_hz);
        gridLayout_7->setObjectName(QString::fromUtf8("gridLayout_7"));
        horizontalLayout_14 = new QHBoxLayout();
        horizontalLayout_14->setObjectName(QString::fromUtf8("horizontalLayout_14"));
        pushButton_v_5126 = new QPushButton(widget_v_cebian_hz);
        pushButton_v_5126->setObjectName(QString::fromUtf8("pushButton_v_5126"));
        pushButton_v_5126->setMinimumSize(QSize(52, 28));
        pushButton_v_5126->setFont(font1);

        horizontalLayout_14->addWidget(pushButton_v_5126);

        label_v_5126 = new QLabel(widget_v_cebian_hz);
        label_v_5126->setObjectName(QString::fromUtf8("label_v_5126"));
        label_v_5126->setMinimumSize(QSize(0, 28));

        horizontalLayout_14->addWidget(label_v_5126);

        horizontalLayout_14->setStretch(1, 1);

        gridLayout_7->addLayout(horizontalLayout_14, 0, 0, 1, 1);

        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        pushButton_v_5228 = new QPushButton(widget_v_cebian_hz);
        pushButton_v_5228->setObjectName(QString::fromUtf8("pushButton_v_5228"));
        pushButton_v_5228->setMinimumSize(QSize(52, 28));
        pushButton_v_5228->setFont(font1);

        horizontalLayout_6->addWidget(pushButton_v_5228);

        label_v_5228 = new QLabel(widget_v_cebian_hz);
        label_v_5228->setObjectName(QString::fromUtf8("label_v_5228"));
        label_v_5228->setMinimumSize(QSize(0, 28));

        horizontalLayout_6->addWidget(label_v_5228);

        horizontalLayout_6->setStretch(1, 1);

        gridLayout_7->addLayout(horizontalLayout_6, 1, 0, 1, 1);

        horizontalLayout_15 = new QHBoxLayout();
        horizontalLayout_15->setObjectName(QString::fromUtf8("horizontalLayout_15"));
        pushButton_v_5386 = new QPushButton(widget_v_cebian_hz);
        pushButton_v_5386->setObjectName(QString::fromUtf8("pushButton_v_5386"));
        pushButton_v_5386->setMinimumSize(QSize(52, 28));
        pushButton_v_5386->setFont(font1);

        horizontalLayout_15->addWidget(pushButton_v_5386);

        label_v_5386 = new QLabel(widget_v_cebian_hz);
        label_v_5386->setObjectName(QString::fromUtf8("label_v_5386"));
        label_v_5386->setMinimumSize(QSize(0, 28));

        horizontalLayout_15->addWidget(label_v_5386);

        horizontalLayout_15->setStretch(1, 1);

        gridLayout_7->addLayout(horizontalLayout_15, 2, 0, 1, 1);

        horizontalLayout_16 = new QHBoxLayout();
        horizontalLayout_16->setObjectName(QString::fromUtf8("horizontalLayout_16"));
        pushButton_v_5494 = new QPushButton(widget_v_cebian_hz);
        pushButton_v_5494->setObjectName(QString::fromUtf8("pushButton_v_5494"));
        pushButton_v_5494->setMinimumSize(QSize(52, 28));
        pushButton_v_5494->setFont(font1);

        horizontalLayout_16->addWidget(pushButton_v_5494);

        label_v_5494 = new QLabel(widget_v_cebian_hz);
        label_v_5494->setObjectName(QString::fromUtf8("label_v_5494"));
        label_v_5494->setMinimumSize(QSize(0, 28));

        horizontalLayout_16->addWidget(label_v_5494);

        horizontalLayout_16->setStretch(1, 1);

        gridLayout_7->addLayout(horizontalLayout_16, 3, 0, 1, 1);

        horizontalLayout_17 = new QHBoxLayout();
        horizontalLayout_17->setObjectName(QString::fromUtf8("horizontalLayout_17"));
        pushButton_v_5550 = new QPushButton(widget_v_cebian_hz);
        pushButton_v_5550->setObjectName(QString::fromUtf8("pushButton_v_5550"));
        pushButton_v_5550->setMinimumSize(QSize(52, 28));
        pushButton_v_5550->setFont(font1);

        horizontalLayout_17->addWidget(pushButton_v_5550);

        label_v_5550 = new QLabel(widget_v_cebian_hz);
        label_v_5550->setObjectName(QString::fromUtf8("label_v_5550"));
        label_v_5550->setMinimumSize(QSize(0, 28));

        horizontalLayout_17->addWidget(label_v_5550);

        horizontalLayout_17->setStretch(1, 1);

        gridLayout_7->addLayout(horizontalLayout_17, 4, 0, 1, 1);

        horizontalLayout_18 = new QHBoxLayout();
        horizontalLayout_18->setObjectName(QString::fromUtf8("horizontalLayout_18"));
        pushButton_v_5666 = new QPushButton(widget_v_cebian_hz);
        pushButton_v_5666->setObjectName(QString::fromUtf8("pushButton_v_5666"));
        pushButton_v_5666->setMinimumSize(QSize(52, 28));
        pushButton_v_5666->setFont(font1);

        horizontalLayout_18->addWidget(pushButton_v_5666);

        label_v_5666 = new QLabel(widget_v_cebian_hz);
        label_v_5666->setObjectName(QString::fromUtf8("label_v_5666"));
        label_v_5666->setMinimumSize(QSize(0, 28));

        horizontalLayout_18->addWidget(label_v_5666);

        horizontalLayout_18->setStretch(1, 1);

        gridLayout_7->addLayout(horizontalLayout_18, 5, 0, 1, 1);

        horizontalLayout_19 = new QHBoxLayout();
        horizontalLayout_19->setObjectName(QString::fromUtf8("horizontalLayout_19"));
        pushButton_v_5730 = new QPushButton(widget_v_cebian_hz);
        pushButton_v_5730->setObjectName(QString::fromUtf8("pushButton_v_5730"));
        pushButton_v_5730->setMinimumSize(QSize(52, 28));
        pushButton_v_5730->setFont(font1);

        horizontalLayout_19->addWidget(pushButton_v_5730);

        label_v_5730 = new QLabel(widget_v_cebian_hz);
        label_v_5730->setObjectName(QString::fromUtf8("label_v_5730"));
        label_v_5730->setMinimumSize(QSize(0, 28));

        horizontalLayout_19->addWidget(label_v_5730);

        horizontalLayout_19->setStretch(1, 1);

        gridLayout_7->addLayout(horizontalLayout_19, 6, 0, 1, 1);

        horizontalLayout_20 = new QHBoxLayout();
        horizontalLayout_20->setObjectName(QString::fromUtf8("horizontalLayout_20"));
        pushButton_v_5800 = new QPushButton(widget_v_cebian_hz);
        pushButton_v_5800->setObjectName(QString::fromUtf8("pushButton_v_5800"));
        pushButton_v_5800->setMinimumSize(QSize(52, 28));
        pushButton_v_5800->setFont(font1);

        horizontalLayout_20->addWidget(pushButton_v_5800);

        label_v_5800 = new QLabel(widget_v_cebian_hz);
        label_v_5800->setObjectName(QString::fromUtf8("label_v_5800"));
        label_v_5800->setMinimumSize(QSize(0, 28));

        horizontalLayout_20->addWidget(label_v_5800);

        horizontalLayout_20->setStretch(1, 1);

        gridLayout_7->addLayout(horizontalLayout_20, 7, 0, 1, 1);

        pushButton_v_stop = new QPushButton(widget_v_cebian_hz);
        pushButton_v_stop->setObjectName(QString::fromUtf8("pushButton_v_stop"));
        pushButton_v_stop->setMinimumSize(QSize(0, 28));
        pushButton_v_stop->setFont(font1);
        pushButton_v_stop->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_7->addWidget(pushButton_v_stop, 8, 0, 1, 1);

        pushButton_v_all_show = new QPushButton(widget_v_cebian_hz);
        pushButton_v_all_show->setObjectName(QString::fromUtf8("pushButton_v_all_show"));
        pushButton_v_all_show->setMinimumSize(QSize(0, 28));
        pushButton_v_all_show->setFont(font1);
        pushButton_v_all_show->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_7->addWidget(pushButton_v_all_show, 9, 0, 1, 1);

        pushButton_v_clear = new QPushButton(widget_v_cebian_hz);
        pushButton_v_clear->setObjectName(QString::fromUtf8("pushButton_v_clear"));
        pushButton_v_clear->setMinimumSize(QSize(0, 28));
        pushButton_v_clear->setFont(font1);
        pushButton_v_clear->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_7->addWidget(pushButton_v_clear, 10, 0, 1, 1);


        horizontalLayout_2->addWidget(widget_v_cebian_hz);

        horizontalLayout_2->setStretch(0, 14);
        horizontalLayout_2->setStretch(1, 4);
        horizontalLayout_2->setStretch(2, 14);
        horizontalLayout_2->setStretch(3, 4);

        verticalLayout_7->addLayout(horizontalLayout_2);

        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        widget_k_temp = new QCustomPlot(page_init_data_monitor);
        widget_k_temp->setObjectName(QString::fromUtf8("widget_k_temp"));
        widget_k_temp->setMinimumSize(QSize(0, 0));

        horizontalLayout_3->addWidget(widget_k_temp);

        widget_k_cebian_temp = new QWidget(page_init_data_monitor);
        widget_k_cebian_temp->setObjectName(QString::fromUtf8("widget_k_cebian_temp"));
        widget_k_cebian_temp->setStyleSheet(QString::fromUtf8("\n"
"#pushButton_k_temp1\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"\n"
"#pushButton_k_temp2\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_k_temp3\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}"));
        gridLayout_6 = new QGridLayout(widget_k_cebian_temp);
        gridLayout_6->setObjectName(QString::fromUtf8("gridLayout_6"));
        horizontalLayout_40 = new QHBoxLayout();
        horizontalLayout_40->setObjectName(QString::fromUtf8("horizontalLayout_40"));
        pushButton_k_temp1 = new QPushButton(widget_k_cebian_temp);
        pushButton_k_temp1->setObjectName(QString::fromUtf8("pushButton_k_temp1"));
        pushButton_k_temp1->setMinimumSize(QSize(52, 28));
        pushButton_k_temp1->setFont(font1);
        pushButton_k_temp1->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_40->addWidget(pushButton_k_temp1);

        label_k_temp1 = new QLabel(widget_k_cebian_temp);
        label_k_temp1->setObjectName(QString::fromUtf8("label_k_temp1"));
        label_k_temp1->setMinimumSize(QSize(64, 28));
        label_k_temp1->setAlignment(Qt::AlignCenter);

        horizontalLayout_40->addWidget(label_k_temp1);

        horizontalLayout_40->setStretch(1, 1);

        gridLayout_6->addLayout(horizontalLayout_40, 0, 0, 1, 1);

        horizontalLayout_39 = new QHBoxLayout();
        horizontalLayout_39->setObjectName(QString::fromUtf8("horizontalLayout_39"));
        pushButton_k_temp2 = new QPushButton(widget_k_cebian_temp);
        pushButton_k_temp2->setObjectName(QString::fromUtf8("pushButton_k_temp2"));
        pushButton_k_temp2->setMinimumSize(QSize(52, 28));
        pushButton_k_temp2->setFont(font1);

        horizontalLayout_39->addWidget(pushButton_k_temp2);

        label_k_temp2 = new QLabel(widget_k_cebian_temp);
        label_k_temp2->setObjectName(QString::fromUtf8("label_k_temp2"));
        label_k_temp2->setMinimumSize(QSize(64, 28));
        label_k_temp2->setAlignment(Qt::AlignCenter);

        horizontalLayout_39->addWidget(label_k_temp2);

        horizontalLayout_39->setStretch(1, 1);

        gridLayout_6->addLayout(horizontalLayout_39, 1, 0, 1, 1);

        horizontalLayout_38 = new QHBoxLayout();
        horizontalLayout_38->setObjectName(QString::fromUtf8("horizontalLayout_38"));
        pushButton_k_temp3 = new QPushButton(widget_k_cebian_temp);
        pushButton_k_temp3->setObjectName(QString::fromUtf8("pushButton_k_temp3"));
        pushButton_k_temp3->setMinimumSize(QSize(52, 28));
        pushButton_k_temp3->setFont(font1);

        horizontalLayout_38->addWidget(pushButton_k_temp3);

        label_k_temp3 = new QLabel(widget_k_cebian_temp);
        label_k_temp3->setObjectName(QString::fromUtf8("label_k_temp3"));
        label_k_temp3->setMinimumSize(QSize(64, 28));
        label_k_temp3->setAlignment(Qt::AlignCenter);

        horizontalLayout_38->addWidget(label_k_temp3);

        horizontalLayout_38->setStretch(1, 1);

        gridLayout_6->addLayout(horizontalLayout_38, 2, 0, 1, 1);

        pushButton_k_temp_stop = new QPushButton(widget_k_cebian_temp);
        pushButton_k_temp_stop->setObjectName(QString::fromUtf8("pushButton_k_temp_stop"));
        pushButton_k_temp_stop->setMinimumSize(QSize(0, 28));
        pushButton_k_temp_stop->setFont(font1);
        pushButton_k_temp_stop->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_6->addWidget(pushButton_k_temp_stop, 3, 0, 1, 1);

        pushButton_k_temp_all_show = new QPushButton(widget_k_cebian_temp);
        pushButton_k_temp_all_show->setObjectName(QString::fromUtf8("pushButton_k_temp_all_show"));
        pushButton_k_temp_all_show->setMinimumSize(QSize(0, 28));
        pushButton_k_temp_all_show->setFont(font1);
        pushButton_k_temp_all_show->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_6->addWidget(pushButton_k_temp_all_show, 4, 0, 1, 1);

        pushButton_k_temp_clear = new QPushButton(widget_k_cebian_temp);
        pushButton_k_temp_clear->setObjectName(QString::fromUtf8("pushButton_k_temp_clear"));
        pushButton_k_temp_clear->setMinimumSize(QSize(0, 28));
        pushButton_k_temp_clear->setFont(font1);
        pushButton_k_temp_clear->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_6->addWidget(pushButton_k_temp_clear, 5, 0, 1, 1);

        verticalSpacer_4 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_6->addItem(verticalSpacer_4, 6, 0, 1, 1);


        horizontalLayout_3->addWidget(widget_k_cebian_temp);

        widget_v_temp = new QCustomPlot(page_init_data_monitor);
        widget_v_temp->setObjectName(QString::fromUtf8("widget_v_temp"));

        horizontalLayout_3->addWidget(widget_v_temp);

        widget_v_cebian_temp = new QWidget(page_init_data_monitor);
        widget_v_cebian_temp->setObjectName(QString::fromUtf8("widget_v_cebian_temp"));
        widget_v_cebian_temp->setStyleSheet(QString::fromUtf8("\n"
"\n"
"#pushButton_v_temp1\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"\n"
"#pushButton_v_temp2\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_v_temp3\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}"));
        gridLayout_5 = new QGridLayout(widget_v_cebian_temp);
        gridLayout_5->setObjectName(QString::fromUtf8("gridLayout_5"));
        horizontalLayout_41 = new QHBoxLayout();
        horizontalLayout_41->setObjectName(QString::fromUtf8("horizontalLayout_41"));
        pushButton_v_temp1 = new QPushButton(widget_v_cebian_temp);
        pushButton_v_temp1->setObjectName(QString::fromUtf8("pushButton_v_temp1"));
        pushButton_v_temp1->setMinimumSize(QSize(52, 28));
        pushButton_v_temp1->setFont(font1);

        horizontalLayout_41->addWidget(pushButton_v_temp1);

        label_v_temp1 = new QLabel(widget_v_cebian_temp);
        label_v_temp1->setObjectName(QString::fromUtf8("label_v_temp1"));
        label_v_temp1->setMinimumSize(QSize(64, 28));
        label_v_temp1->setAlignment(Qt::AlignCenter);

        horizontalLayout_41->addWidget(label_v_temp1);

        horizontalLayout_41->setStretch(1, 1);

        gridLayout_5->addLayout(horizontalLayout_41, 0, 0, 1, 1);

        horizontalLayout_42 = new QHBoxLayout();
        horizontalLayout_42->setObjectName(QString::fromUtf8("horizontalLayout_42"));
        pushButton_v_temp2 = new QPushButton(widget_v_cebian_temp);
        pushButton_v_temp2->setObjectName(QString::fromUtf8("pushButton_v_temp2"));
        pushButton_v_temp2->setMinimumSize(QSize(52, 28));
        pushButton_v_temp2->setFont(font1);

        horizontalLayout_42->addWidget(pushButton_v_temp2);

        label_v_temp2 = new QLabel(widget_v_cebian_temp);
        label_v_temp2->setObjectName(QString::fromUtf8("label_v_temp2"));
        label_v_temp2->setMinimumSize(QSize(64, 28));
        label_v_temp2->setAlignment(Qt::AlignCenter);

        horizontalLayout_42->addWidget(label_v_temp2);

        horizontalLayout_42->setStretch(1, 1);

        gridLayout_5->addLayout(horizontalLayout_42, 1, 0, 1, 1);

        horizontalLayout_43 = new QHBoxLayout();
        horizontalLayout_43->setObjectName(QString::fromUtf8("horizontalLayout_43"));
        pushButton_v_temp3 = new QPushButton(widget_v_cebian_temp);
        pushButton_v_temp3->setObjectName(QString::fromUtf8("pushButton_v_temp3"));
        pushButton_v_temp3->setMinimumSize(QSize(52, 28));
        pushButton_v_temp3->setFont(font1);

        horizontalLayout_43->addWidget(pushButton_v_temp3);

        label_v_temp3 = new QLabel(widget_v_cebian_temp);
        label_v_temp3->setObjectName(QString::fromUtf8("label_v_temp3"));
        label_v_temp3->setMinimumSize(QSize(64, 28));
        label_v_temp3->setAlignment(Qt::AlignCenter);

        horizontalLayout_43->addWidget(label_v_temp3);

        horizontalLayout_43->setStretch(1, 1);

        gridLayout_5->addLayout(horizontalLayout_43, 2, 0, 1, 1);

        pushButton_v_temp_stop = new QPushButton(widget_v_cebian_temp);
        pushButton_v_temp_stop->setObjectName(QString::fromUtf8("pushButton_v_temp_stop"));
        pushButton_v_temp_stop->setMinimumSize(QSize(0, 28));
        pushButton_v_temp_stop->setFont(font1);
        pushButton_v_temp_stop->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_5->addWidget(pushButton_v_temp_stop, 3, 0, 1, 1);

        pushButton_v_temp_all_show = new QPushButton(widget_v_cebian_temp);
        pushButton_v_temp_all_show->setObjectName(QString::fromUtf8("pushButton_v_temp_all_show"));
        pushButton_v_temp_all_show->setMinimumSize(QSize(0, 28));
        pushButton_v_temp_all_show->setFont(font1);
        pushButton_v_temp_all_show->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_5->addWidget(pushButton_v_temp_all_show, 4, 0, 1, 1);

        pushButton_v_temp_clear = new QPushButton(widget_v_cebian_temp);
        pushButton_v_temp_clear->setObjectName(QString::fromUtf8("pushButton_v_temp_clear"));
        pushButton_v_temp_clear->setMinimumSize(QSize(0, 28));
        pushButton_v_temp_clear->setFont(font1);
        pushButton_v_temp_clear->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_5->addWidget(pushButton_v_temp_clear, 5, 0, 1, 1);

        verticalSpacer_5 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_5->addItem(verticalSpacer_5, 6, 0, 1, 1);


        horizontalLayout_3->addWidget(widget_v_cebian_temp);

        horizontalLayout_3->setStretch(0, 14);
        horizontalLayout_3->setStretch(1, 4);
        horizontalLayout_3->setStretch(2, 14);
        horizontalLayout_3->setStretch(3, 4);

        verticalLayout_7->addLayout(horizontalLayout_3);

        stackedWidget_FMG->addWidget(page_init_data_monitor);
        page_light_data_monitor = new QWidget();
        page_light_data_monitor->setObjectName(QString::fromUtf8("page_light_data_monitor"));
        verticalLayout_16 = new QVBoxLayout(page_light_data_monitor);
        verticalLayout_16->setObjectName(QString::fromUtf8("verticalLayout_16"));
        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        widget_k_light_temp = new QCustomPlot(page_light_data_monitor);
        widget_k_light_temp->setObjectName(QString::fromUtf8("widget_k_light_temp"));

        horizontalLayout_5->addWidget(widget_k_light_temp);

        widget_light_k_cebian_hz = new QWidget(page_light_data_monitor);
        widget_light_k_cebian_hz->setObjectName(QString::fromUtf8("widget_light_k_cebian_hz"));
        widget_light_k_cebian_hz->setStyleSheet(QString::fromUtf8("\n"
"#pushButton_k_light_2224\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"\n"
"#pushButton_k_light_2304\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_k_light_2384\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_k_light_2544\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_k_light_2624\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_k_light_2784\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_k_light_3000\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_k_light_3140\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}"));
        gridLayout = new QGridLayout(widget_light_k_cebian_hz);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        horizontalLayout_28 = new QHBoxLayout();
        horizontalLayout_28->setObjectName(QString::fromUtf8("horizontalLayout_28"));
        pushButton_k_light_2224 = new QPushButton(widget_light_k_cebian_hz);
        pushButton_k_light_2224->setObjectName(QString::fromUtf8("pushButton_k_light_2224"));
        pushButton_k_light_2224->setMinimumSize(QSize(52, 28));
        pushButton_k_light_2224->setFont(font1);

        horizontalLayout_28->addWidget(pushButton_k_light_2224);

        label_k_light_2224 = new QLabel(widget_light_k_cebian_hz);
        label_k_light_2224->setObjectName(QString::fromUtf8("label_k_light_2224"));
        label_k_light_2224->setMinimumSize(QSize(64, 28));

        horizontalLayout_28->addWidget(label_k_light_2224);

        horizontalLayout_28->setStretch(1, 1);

        gridLayout->addLayout(horizontalLayout_28, 0, 0, 1, 1);

        horizontalLayout_27 = new QHBoxLayout();
        horizontalLayout_27->setObjectName(QString::fromUtf8("horizontalLayout_27"));
        pushButton_k_light_2304 = new QPushButton(widget_light_k_cebian_hz);
        pushButton_k_light_2304->setObjectName(QString::fromUtf8("pushButton_k_light_2304"));
        pushButton_k_light_2304->setMinimumSize(QSize(52, 28));
        pushButton_k_light_2304->setFont(font1);

        horizontalLayout_27->addWidget(pushButton_k_light_2304);

        label_k_light_2304 = new QLabel(widget_light_k_cebian_hz);
        label_k_light_2304->setObjectName(QString::fromUtf8("label_k_light_2304"));
        label_k_light_2304->setMinimumSize(QSize(64, 28));

        horizontalLayout_27->addWidget(label_k_light_2304);

        horizontalLayout_27->setStretch(1, 1);

        gridLayout->addLayout(horizontalLayout_27, 1, 0, 1, 1);

        horizontalLayout_26 = new QHBoxLayout();
        horizontalLayout_26->setObjectName(QString::fromUtf8("horizontalLayout_26"));
        pushButton_k_light_2384 = new QPushButton(widget_light_k_cebian_hz);
        pushButton_k_light_2384->setObjectName(QString::fromUtf8("pushButton_k_light_2384"));
        pushButton_k_light_2384->setMinimumSize(QSize(52, 28));
        pushButton_k_light_2384->setFont(font1);

        horizontalLayout_26->addWidget(pushButton_k_light_2384);

        label_k_light_2384 = new QLabel(widget_light_k_cebian_hz);
        label_k_light_2384->setObjectName(QString::fromUtf8("label_k_light_2384"));
        label_k_light_2384->setMinimumSize(QSize(64, 28));

        horizontalLayout_26->addWidget(label_k_light_2384);

        horizontalLayout_26->setStretch(1, 1);

        gridLayout->addLayout(horizontalLayout_26, 2, 0, 1, 1);

        horizontalLayout_25 = new QHBoxLayout();
        horizontalLayout_25->setObjectName(QString::fromUtf8("horizontalLayout_25"));
        pushButton_k_light_2544 = new QPushButton(widget_light_k_cebian_hz);
        pushButton_k_light_2544->setObjectName(QString::fromUtf8("pushButton_k_light_2544"));
        pushButton_k_light_2544->setMinimumSize(QSize(52, 28));
        pushButton_k_light_2544->setFont(font1);

        horizontalLayout_25->addWidget(pushButton_k_light_2544);

        label_k_light_2544 = new QLabel(widget_light_k_cebian_hz);
        label_k_light_2544->setObjectName(QString::fromUtf8("label_k_light_2544"));
        label_k_light_2544->setMinimumSize(QSize(64, 28));

        horizontalLayout_25->addWidget(label_k_light_2544);

        horizontalLayout_25->setStretch(1, 1);

        gridLayout->addLayout(horizontalLayout_25, 3, 0, 1, 1);

        horizontalLayout_24 = new QHBoxLayout();
        horizontalLayout_24->setObjectName(QString::fromUtf8("horizontalLayout_24"));
        pushButton_k_light_2624 = new QPushButton(widget_light_k_cebian_hz);
        pushButton_k_light_2624->setObjectName(QString::fromUtf8("pushButton_k_light_2624"));
        pushButton_k_light_2624->setMinimumSize(QSize(52, 28));
        pushButton_k_light_2624->setFont(font1);

        horizontalLayout_24->addWidget(pushButton_k_light_2624);

        label_k_light_2624 = new QLabel(widget_light_k_cebian_hz);
        label_k_light_2624->setObjectName(QString::fromUtf8("label_k_light_2624"));
        label_k_light_2624->setMinimumSize(QSize(64, 28));

        horizontalLayout_24->addWidget(label_k_light_2624);

        horizontalLayout_24->setStretch(1, 1);

        gridLayout->addLayout(horizontalLayout_24, 4, 0, 1, 1);

        horizontalLayout_23 = new QHBoxLayout();
        horizontalLayout_23->setObjectName(QString::fromUtf8("horizontalLayout_23"));
        pushButton_k_light_2784 = new QPushButton(widget_light_k_cebian_hz);
        pushButton_k_light_2784->setObjectName(QString::fromUtf8("pushButton_k_light_2784"));
        pushButton_k_light_2784->setMinimumSize(QSize(52, 28));
        pushButton_k_light_2784->setFont(font1);

        horizontalLayout_23->addWidget(pushButton_k_light_2784);

        label_k_light_2784 = new QLabel(widget_light_k_cebian_hz);
        label_k_light_2784->setObjectName(QString::fromUtf8("label_k_light_2784"));
        label_k_light_2784->setMinimumSize(QSize(64, 28));

        horizontalLayout_23->addWidget(label_k_light_2784);

        horizontalLayout_23->setStretch(1, 1);

        gridLayout->addLayout(horizontalLayout_23, 5, 0, 1, 1);

        horizontalLayout_22 = new QHBoxLayout();
        horizontalLayout_22->setObjectName(QString::fromUtf8("horizontalLayout_22"));
        pushButton_k_light_3000 = new QPushButton(widget_light_k_cebian_hz);
        pushButton_k_light_3000->setObjectName(QString::fromUtf8("pushButton_k_light_3000"));
        pushButton_k_light_3000->setMinimumSize(QSize(52, 28));
        pushButton_k_light_3000->setFont(font1);

        horizontalLayout_22->addWidget(pushButton_k_light_3000);

        label_k_light_3000 = new QLabel(widget_light_k_cebian_hz);
        label_k_light_3000->setObjectName(QString::fromUtf8("label_k_light_3000"));
        label_k_light_3000->setMinimumSize(QSize(64, 28));

        horizontalLayout_22->addWidget(label_k_light_3000);

        horizontalLayout_22->setStretch(1, 1);

        gridLayout->addLayout(horizontalLayout_22, 6, 0, 1, 1);

        horizontalLayout_21 = new QHBoxLayout();
        horizontalLayout_21->setObjectName(QString::fromUtf8("horizontalLayout_21"));
        pushButton_k_light_3140 = new QPushButton(widget_light_k_cebian_hz);
        pushButton_k_light_3140->setObjectName(QString::fromUtf8("pushButton_k_light_3140"));
        pushButton_k_light_3140->setMinimumSize(QSize(52, 28));
        pushButton_k_light_3140->setFont(font1);

        horizontalLayout_21->addWidget(pushButton_k_light_3140);

        label_k_light_3140 = new QLabel(widget_light_k_cebian_hz);
        label_k_light_3140->setObjectName(QString::fromUtf8("label_k_light_3140"));
        label_k_light_3140->setMinimumSize(QSize(64, 28));

        horizontalLayout_21->addWidget(label_k_light_3140);

        horizontalLayout_21->setStretch(1, 1);

        gridLayout->addLayout(horizontalLayout_21, 7, 0, 1, 1);

        pushButton_k_light_stop = new QPushButton(widget_light_k_cebian_hz);
        pushButton_k_light_stop->setObjectName(QString::fromUtf8("pushButton_k_light_stop"));
        pushButton_k_light_stop->setMinimumSize(QSize(0, 28));
        pushButton_k_light_stop->setFont(font1);
        pushButton_k_light_stop->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout->addWidget(pushButton_k_light_stop, 8, 0, 1, 1);

        pushButton_k_light_all_show = new QPushButton(widget_light_k_cebian_hz);
        pushButton_k_light_all_show->setObjectName(QString::fromUtf8("pushButton_k_light_all_show"));
        pushButton_k_light_all_show->setMinimumSize(QSize(0, 28));
        pushButton_k_light_all_show->setFont(font1);
        pushButton_k_light_all_show->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout->addWidget(pushButton_k_light_all_show, 9, 0, 1, 1);

        pushButton_k_light_clear = new QPushButton(widget_light_k_cebian_hz);
        pushButton_k_light_clear->setObjectName(QString::fromUtf8("pushButton_k_light_clear"));
        pushButton_k_light_clear->setMinimumSize(QSize(0, 28));
        pushButton_k_light_clear->setFont(font1);
        pushButton_k_light_clear->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout->addWidget(pushButton_k_light_clear, 10, 0, 1, 1);


        horizontalLayout_5->addWidget(widget_light_k_cebian_hz);

        widget_v_light_temp = new QCustomPlot(page_light_data_monitor);
        widget_v_light_temp->setObjectName(QString::fromUtf8("widget_v_light_temp"));

        horizontalLayout_5->addWidget(widget_v_light_temp);

        widget_light_v_cebian_hz = new QWidget(page_light_data_monitor);
        widget_light_v_cebian_hz->setObjectName(QString::fromUtf8("widget_light_v_cebian_hz"));
        widget_light_v_cebian_hz->setStyleSheet(QString::fromUtf8("\n"
"#pushButton_v_light_5126\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"\n"
"#pushButton_v_light_5228\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_v_light_5386\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_v_light_5494\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_v_light_5550\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_v_light_5666\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_v_light_5730\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"#pushButton_v_light_5800\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}"));
        gridLayout_2 = new QGridLayout(widget_light_v_cebian_hz);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        horizontalLayout_36 = new QHBoxLayout();
        horizontalLayout_36->setObjectName(QString::fromUtf8("horizontalLayout_36"));
        pushButton_v_light_5126 = new QPushButton(widget_light_v_cebian_hz);
        pushButton_v_light_5126->setObjectName(QString::fromUtf8("pushButton_v_light_5126"));
        pushButton_v_light_5126->setMinimumSize(QSize(52, 28));
        pushButton_v_light_5126->setFont(font1);

        horizontalLayout_36->addWidget(pushButton_v_light_5126);

        label_v_light_5126 = new QLabel(widget_light_v_cebian_hz);
        label_v_light_5126->setObjectName(QString::fromUtf8("label_v_light_5126"));
        label_v_light_5126->setMinimumSize(QSize(64, 28));

        horizontalLayout_36->addWidget(label_v_light_5126);

        horizontalLayout_36->setStretch(1, 1);

        gridLayout_2->addLayout(horizontalLayout_36, 0, 0, 1, 1);

        horizontalLayout_35 = new QHBoxLayout();
        horizontalLayout_35->setObjectName(QString::fromUtf8("horizontalLayout_35"));
        pushButton_v_light_5228 = new QPushButton(widget_light_v_cebian_hz);
        pushButton_v_light_5228->setObjectName(QString::fromUtf8("pushButton_v_light_5228"));
        pushButton_v_light_5228->setMinimumSize(QSize(52, 28));
        pushButton_v_light_5228->setFont(font1);

        horizontalLayout_35->addWidget(pushButton_v_light_5228);

        label_v_light_5228 = new QLabel(widget_light_v_cebian_hz);
        label_v_light_5228->setObjectName(QString::fromUtf8("label_v_light_5228"));
        label_v_light_5228->setMinimumSize(QSize(64, 28));

        horizontalLayout_35->addWidget(label_v_light_5228);

        horizontalLayout_35->setStretch(1, 1);

        gridLayout_2->addLayout(horizontalLayout_35, 1, 0, 1, 1);

        horizontalLayout_34 = new QHBoxLayout();
        horizontalLayout_34->setObjectName(QString::fromUtf8("horizontalLayout_34"));
        pushButton_v_light_5386 = new QPushButton(widget_light_v_cebian_hz);
        pushButton_v_light_5386->setObjectName(QString::fromUtf8("pushButton_v_light_5386"));
        pushButton_v_light_5386->setMinimumSize(QSize(52, 28));
        pushButton_v_light_5386->setFont(font1);

        horizontalLayout_34->addWidget(pushButton_v_light_5386);

        label_v_light_5386 = new QLabel(widget_light_v_cebian_hz);
        label_v_light_5386->setObjectName(QString::fromUtf8("label_v_light_5386"));
        label_v_light_5386->setMinimumSize(QSize(64, 28));

        horizontalLayout_34->addWidget(label_v_light_5386);

        horizontalLayout_34->setStretch(1, 1);

        gridLayout_2->addLayout(horizontalLayout_34, 2, 0, 1, 1);

        horizontalLayout_33 = new QHBoxLayout();
        horizontalLayout_33->setObjectName(QString::fromUtf8("horizontalLayout_33"));
        pushButton_v_light_5494 = new QPushButton(widget_light_v_cebian_hz);
        pushButton_v_light_5494->setObjectName(QString::fromUtf8("pushButton_v_light_5494"));
        pushButton_v_light_5494->setMinimumSize(QSize(52, 28));
        pushButton_v_light_5494->setFont(font1);

        horizontalLayout_33->addWidget(pushButton_v_light_5494);

        label_v_light_5494 = new QLabel(widget_light_v_cebian_hz);
        label_v_light_5494->setObjectName(QString::fromUtf8("label_v_light_5494"));
        label_v_light_5494->setMinimumSize(QSize(64, 28));

        horizontalLayout_33->addWidget(label_v_light_5494);

        horizontalLayout_33->setStretch(1, 1);

        gridLayout_2->addLayout(horizontalLayout_33, 3, 0, 1, 1);

        horizontalLayout_32 = new QHBoxLayout();
        horizontalLayout_32->setObjectName(QString::fromUtf8("horizontalLayout_32"));
        pushButton_v_light_5550 = new QPushButton(widget_light_v_cebian_hz);
        pushButton_v_light_5550->setObjectName(QString::fromUtf8("pushButton_v_light_5550"));
        pushButton_v_light_5550->setMinimumSize(QSize(52, 28));
        pushButton_v_light_5550->setFont(font1);

        horizontalLayout_32->addWidget(pushButton_v_light_5550);

        label_v_light_5550 = new QLabel(widget_light_v_cebian_hz);
        label_v_light_5550->setObjectName(QString::fromUtf8("label_v_light_5550"));
        label_v_light_5550->setMinimumSize(QSize(64, 28));

        horizontalLayout_32->addWidget(label_v_light_5550);

        horizontalLayout_32->setStretch(1, 1);

        gridLayout_2->addLayout(horizontalLayout_32, 4, 0, 1, 1);

        horizontalLayout_31 = new QHBoxLayout();
        horizontalLayout_31->setObjectName(QString::fromUtf8("horizontalLayout_31"));
        pushButton_v_light_5666 = new QPushButton(widget_light_v_cebian_hz);
        pushButton_v_light_5666->setObjectName(QString::fromUtf8("pushButton_v_light_5666"));
        pushButton_v_light_5666->setMinimumSize(QSize(52, 28));
        pushButton_v_light_5666->setFont(font1);

        horizontalLayout_31->addWidget(pushButton_v_light_5666);

        label_v_light_5666 = new QLabel(widget_light_v_cebian_hz);
        label_v_light_5666->setObjectName(QString::fromUtf8("label_v_light_5666"));
        label_v_light_5666->setMinimumSize(QSize(64, 28));

        horizontalLayout_31->addWidget(label_v_light_5666);

        horizontalLayout_31->setStretch(1, 1);

        gridLayout_2->addLayout(horizontalLayout_31, 5, 0, 1, 1);

        horizontalLayout_30 = new QHBoxLayout();
        horizontalLayout_30->setObjectName(QString::fromUtf8("horizontalLayout_30"));
        pushButton_v_light_5730 = new QPushButton(widget_light_v_cebian_hz);
        pushButton_v_light_5730->setObjectName(QString::fromUtf8("pushButton_v_light_5730"));
        pushButton_v_light_5730->setMinimumSize(QSize(52, 28));
        pushButton_v_light_5730->setFont(font1);

        horizontalLayout_30->addWidget(pushButton_v_light_5730);

        label_v_light_5730 = new QLabel(widget_light_v_cebian_hz);
        label_v_light_5730->setObjectName(QString::fromUtf8("label_v_light_5730"));
        label_v_light_5730->setMinimumSize(QSize(64, 28));

        horizontalLayout_30->addWidget(label_v_light_5730);

        horizontalLayout_30->setStretch(1, 1);

        gridLayout_2->addLayout(horizontalLayout_30, 6, 0, 1, 1);

        horizontalLayout_29 = new QHBoxLayout();
        horizontalLayout_29->setObjectName(QString::fromUtf8("horizontalLayout_29"));
        pushButton_v_light_5800 = new QPushButton(widget_light_v_cebian_hz);
        pushButton_v_light_5800->setObjectName(QString::fromUtf8("pushButton_v_light_5800"));
        pushButton_v_light_5800->setMinimumSize(QSize(52, 28));
        pushButton_v_light_5800->setFont(font1);

        horizontalLayout_29->addWidget(pushButton_v_light_5800);

        label_v_light_5800 = new QLabel(widget_light_v_cebian_hz);
        label_v_light_5800->setObjectName(QString::fromUtf8("label_v_light_5800"));
        label_v_light_5800->setMinimumSize(QSize(64, 28));

        horizontalLayout_29->addWidget(label_v_light_5800);

        horizontalLayout_29->setStretch(1, 1);

        gridLayout_2->addLayout(horizontalLayout_29, 7, 0, 1, 1);

        pushButton_v_light_stop = new QPushButton(widget_light_v_cebian_hz);
        pushButton_v_light_stop->setObjectName(QString::fromUtf8("pushButton_v_light_stop"));
        pushButton_v_light_stop->setMinimumSize(QSize(0, 28));
        pushButton_v_light_stop->setFont(font1);
        pushButton_v_light_stop->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_2->addWidget(pushButton_v_light_stop, 8, 0, 1, 1);

        pushButton_v_light_all_show = new QPushButton(widget_light_v_cebian_hz);
        pushButton_v_light_all_show->setObjectName(QString::fromUtf8("pushButton_v_light_all_show"));
        pushButton_v_light_all_show->setMinimumSize(QSize(0, 28));
        pushButton_v_light_all_show->setFont(font1);
        pushButton_v_light_all_show->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_2->addWidget(pushButton_v_light_all_show, 9, 0, 1, 1);

        pushButton_v_light_clear = new QPushButton(widget_light_v_cebian_hz);
        pushButton_v_light_clear->setObjectName(QString::fromUtf8("pushButton_v_light_clear"));
        pushButton_v_light_clear->setMinimumSize(QSize(0, 28));
        pushButton_v_light_clear->setFont(font1);
        pushButton_v_light_clear->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_2->addWidget(pushButton_v_light_clear, 10, 0, 1, 1);


        horizontalLayout_5->addWidget(widget_light_v_cebian_hz);

        horizontalLayout_5->setStretch(0, 14);
        horizontalLayout_5->setStretch(1, 4);
        horizontalLayout_5->setStretch(2, 14);
        horizontalLayout_5->setStretch(3, 4);

        verticalLayout_16->addLayout(horizontalLayout_5);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        widget_light_rain = new QCustomPlot(page_light_data_monitor);
        widget_light_rain->setObjectName(QString::fromUtf8("widget_light_rain"));

        horizontalLayout_4->addWidget(widget_light_rain);

        widget_light_cebian_rain = new QWidget(page_light_data_monitor);
        widget_light_cebian_rain->setObjectName(QString::fromUtf8("widget_light_cebian_rain"));
        widget_light_cebian_rain->setStyleSheet(QString::fromUtf8("\n"
"\n"
"#pushButton_light_rain\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"\n"
""));
        gridLayout_3 = new QGridLayout(widget_light_cebian_rain);
        gridLayout_3->setObjectName(QString::fromUtf8("gridLayout_3"));
        horizontalLayout_44 = new QHBoxLayout();
        horizontalLayout_44->setObjectName(QString::fromUtf8("horizontalLayout_44"));
        pushButton_light_rain = new QPushButton(widget_light_cebian_rain);
        pushButton_light_rain->setObjectName(QString::fromUtf8("pushButton_light_rain"));
        pushButton_light_rain->setMinimumSize(QSize(52, 28));
        pushButton_light_rain->setFont(font1);

        horizontalLayout_44->addWidget(pushButton_light_rain);

        label_light_rain = new QLabel(widget_light_cebian_rain);
        label_light_rain->setObjectName(QString::fromUtf8("label_light_rain"));
        label_light_rain->setMinimumSize(QSize(64, 28));
        label_light_rain->setAlignment(Qt::AlignCenter);

        horizontalLayout_44->addWidget(label_light_rain);

        horizontalLayout_44->setStretch(1, 1);

        gridLayout_3->addLayout(horizontalLayout_44, 0, 0, 1, 1);

        pushButton_light_rain_stop = new QPushButton(widget_light_cebian_rain);
        pushButton_light_rain_stop->setObjectName(QString::fromUtf8("pushButton_light_rain_stop"));
        pushButton_light_rain_stop->setMinimumSize(QSize(0, 28));
        pushButton_light_rain_stop->setFont(font1);
        pushButton_light_rain_stop->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_3->addWidget(pushButton_light_rain_stop, 1, 0, 1, 1);

        pushButton_light_rain_all_show = new QPushButton(widget_light_cebian_rain);
        pushButton_light_rain_all_show->setObjectName(QString::fromUtf8("pushButton_light_rain_all_show"));
        pushButton_light_rain_all_show->setMinimumSize(QSize(0, 28));
        pushButton_light_rain_all_show->setFont(font1);
        pushButton_light_rain_all_show->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_3->addWidget(pushButton_light_rain_all_show, 2, 0, 1, 1);

        pushButton_light_rain_clear = new QPushButton(widget_light_cebian_rain);
        pushButton_light_rain_clear->setObjectName(QString::fromUtf8("pushButton_light_rain_clear"));
        pushButton_light_rain_clear->setMinimumSize(QSize(0, 28));
        pushButton_light_rain_clear->setFont(font1);
        pushButton_light_rain_clear->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_3->addWidget(pushButton_light_rain_clear, 3, 0, 1, 1);

        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_3->addItem(verticalSpacer_2, 4, 0, 1, 1);


        horizontalLayout_4->addWidget(widget_light_cebian_rain);

        widget_light_temp = new QCustomPlot(page_light_data_monitor);
        widget_light_temp->setObjectName(QString::fromUtf8("widget_light_temp"));

        horizontalLayout_4->addWidget(widget_light_temp);

        widget_light_cebian_temp = new QWidget(page_light_data_monitor);
        widget_light_cebian_temp->setObjectName(QString::fromUtf8("widget_light_cebian_temp"));
        widget_light_cebian_temp->setStyleSheet(QString::fromUtf8("\n"
"\n"
"#pushButton_light_temp\n"
"{\n"
"	border-image: url(:/VectorIcon/eyesclose.png);\n"
"}\n"
"\n"
""));
        gridLayout_4 = new QGridLayout(widget_light_cebian_temp);
        gridLayout_4->setObjectName(QString::fromUtf8("gridLayout_4"));
        horizontalLayout_45 = new QHBoxLayout();
        horizontalLayout_45->setObjectName(QString::fromUtf8("horizontalLayout_45"));
        pushButton_light_temp = new QPushButton(widget_light_cebian_temp);
        pushButton_light_temp->setObjectName(QString::fromUtf8("pushButton_light_temp"));
        pushButton_light_temp->setMinimumSize(QSize(52, 28));
        pushButton_light_temp->setFont(font1);

        horizontalLayout_45->addWidget(pushButton_light_temp);

        label_light_temp = new QLabel(widget_light_cebian_temp);
        label_light_temp->setObjectName(QString::fromUtf8("label_light_temp"));
        label_light_temp->setMinimumSize(QSize(64, 28));
        label_light_temp->setAlignment(Qt::AlignCenter);

        horizontalLayout_45->addWidget(label_light_temp);

        horizontalLayout_45->setStretch(1, 1);

        gridLayout_4->addLayout(horizontalLayout_45, 0, 0, 1, 1);

        pushButton_light_temp_stop = new QPushButton(widget_light_cebian_temp);
        pushButton_light_temp_stop->setObjectName(QString::fromUtf8("pushButton_light_temp_stop"));
        pushButton_light_temp_stop->setMinimumSize(QSize(0, 28));
        pushButton_light_temp_stop->setFont(font1);
        pushButton_light_temp_stop->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_4->addWidget(pushButton_light_temp_stop, 1, 0, 1, 1);

        pushButton_light_temp_all_show = new QPushButton(widget_light_cebian_temp);
        pushButton_light_temp_all_show->setObjectName(QString::fromUtf8("pushButton_light_temp_all_show"));
        pushButton_light_temp_all_show->setMinimumSize(QSize(0, 28));
        pushButton_light_temp_all_show->setFont(font1);
        pushButton_light_temp_all_show->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_4->addWidget(pushButton_light_temp_all_show, 2, 0, 1, 1);

        pushButton_light_temp_clear = new QPushButton(widget_light_cebian_temp);
        pushButton_light_temp_clear->setObjectName(QString::fromUtf8("pushButton_light_temp_clear"));
        pushButton_light_temp_clear->setMinimumSize(QSize(0, 28));
        pushButton_light_temp_clear->setFont(font1);
        pushButton_light_temp_clear->setStyleSheet(QString::fromUtf8("QPushButton\n"
"{background-color: rgb(74, 92, 124);color:white;border: none;}\n"
"\n"
"QPushButton:focus\n"
"{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}\n"
"\n"
"QPushButton:hover\n"
"{border:1px solid white;}"));

        gridLayout_4->addWidget(pushButton_light_temp_clear, 3, 0, 1, 1);

        verticalSpacer_3 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_4->addItem(verticalSpacer_3, 4, 0, 1, 1);


        horizontalLayout_4->addWidget(widget_light_cebian_temp);

        horizontalLayout_4->setStretch(0, 14);
        horizontalLayout_4->setStretch(1, 4);
        horizontalLayout_4->setStretch(2, 14);
        horizontalLayout_4->setStretch(3, 4);

        verticalLayout_16->addLayout(horizontalLayout_4);

        stackedWidget_FMG->addWidget(page_light_data_monitor);

        verticalLayout_3->addWidget(stackedWidget_FMG);

        splitter_3->addWidget(widget_plot);
        widget_kv_data = new QWidget(splitter_3);
        widget_kv_data->setObjectName(QString::fromUtf8("widget_kv_data"));
        QSizePolicy sizePolicy2(QSizePolicy::Minimum, QSizePolicy::Maximum);
        sizePolicy2.setHorizontalStretch(0);
        sizePolicy2.setVerticalStretch(0);
        sizePolicy2.setHeightForWidth(widget_kv_data->sizePolicy().hasHeightForWidth());
        widget_kv_data->setSizePolicy(sizePolicy2);
        widget_kv_data->setStyleSheet(QString::fromUtf8("QTableView\n"
"{background-color:rgb(74, 92, 124);color:black;}\n"
"QTableView:item\n"
"{background-:rgb(74, 92, 124);color:white;}\n"
"QTableView:item:selected\n"
"{background-color: rgb(0, 170, 255);color:white;}\n"
"QHeaderView::section\n"
"{background-color:rgb(74, 92, 124);color:rgb(255,255,255);}\n"
"QHeaderView::section:vertical\n"
"{background-color:rgb(74, 92, 124);color:rgb(255,255,255);}\n"
"QTableCornerButton::section\n"
"{background-color:rgb(74, 92, 124);border: 2px solid;}\n"
"\n"
"\n"
"#label_v\n"
"{\n"
"color:white;border:none;\n"
"}\n"
"#label_v_2\n"
"{\n"
"color:white;border:none;\n"
"}\n"
"\n"
"#label_k\n"
"{\n"
"color:white;border:none;\n"
"}\n"
"#label_k_2\n"
"{\n"
"color:white;border:none;\n"
"}\n"
"\n"
""));
        horizontalLayout_47 = new QHBoxLayout(widget_kv_data);
        horizontalLayout_47->setObjectName(QString::fromUtf8("horizontalLayout_47"));
        widget_k = new QWidget(widget_kv_data);
        widget_k->setObjectName(QString::fromUtf8("widget_k"));
        verticalLayout_11 = new QVBoxLayout(widget_k);
        verticalLayout_11->setObjectName(QString::fromUtf8("verticalLayout_11"));
        splitter = new QSplitter(widget_k);
        splitter->setObjectName(QString::fromUtf8("splitter"));
        splitter->setOrientation(Qt::Vertical);
        widget_6 = new QWidget(splitter);
        widget_6->setObjectName(QString::fromUtf8("widget_6"));
        verticalLayout_4 = new QVBoxLayout(widget_6);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        horizontalLayout_37 = new QHBoxLayout();
        horizontalLayout_37->setObjectName(QString::fromUtf8("horizontalLayout_37"));
        label_k = new QLabel(widget_6);
        label_k->setObjectName(QString::fromUtf8("label_k"));
        label_k->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter);

        horizontalLayout_37->addWidget(label_k);

        comboBox_k_data = new QComboBox(widget_6);
        comboBox_k_data->addItem(QString());
        comboBox_k_data->addItem(QString());
        comboBox_k_data->addItem(QString());
        comboBox_k_data->addItem(QString());
        comboBox_k_data->setObjectName(QString::fromUtf8("comboBox_k_data"));
        comboBox_k_data->setStyleSheet(QString::fromUtf8(""));

        horizontalLayout_37->addWidget(comboBox_k_data);

        horizontalSpacer = new QSpacerItem(100, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_37->addItem(horizontalSpacer);

        horizontalLayout_37->setStretch(0, 1);
        horizontalLayout_37->setStretch(1, 2);

        verticalLayout_4->addLayout(horizontalLayout_37);

        tableView_k = new QTableView(widget_6);
        tableView_k->setObjectName(QString::fromUtf8("tableView_k"));

        verticalLayout_4->addWidget(tableView_k);

        splitter->addWidget(widget_6);
        widget_5 = new QWidget(splitter);
        widget_5->setObjectName(QString::fromUtf8("widget_5"));
        verticalLayout_8 = new QVBoxLayout(widget_5);
        verticalLayout_8->setObjectName(QString::fromUtf8("verticalLayout_8"));
        label_k_2 = new QLabel(widget_5);
        label_k_2->setObjectName(QString::fromUtf8("label_k_2"));
        label_k_2->setAlignment(Qt::AlignCenter);

        verticalLayout_8->addWidget(label_k_2);

        tableView_k_calibration = new QTableView(widget_5);
        tableView_k_calibration->setObjectName(QString::fromUtf8("tableView_k_calibration"));

        verticalLayout_8->addWidget(tableView_k_calibration);

        splitter->addWidget(widget_5);

        verticalLayout_11->addWidget(splitter);


        horizontalLayout_47->addWidget(widget_k);

        widget_v = new QWidget(widget_kv_data);
        widget_v->setObjectName(QString::fromUtf8("widget_v"));
        verticalLayout_12 = new QVBoxLayout(widget_v);
        verticalLayout_12->setObjectName(QString::fromUtf8("verticalLayout_12"));
        splitter_2 = new QSplitter(widget_v);
        splitter_2->setObjectName(QString::fromUtf8("splitter_2"));
        splitter_2->setOrientation(Qt::Vertical);
        widget_4 = new QWidget(splitter_2);
        widget_4->setObjectName(QString::fromUtf8("widget_4"));
        verticalLayout_10 = new QVBoxLayout(widget_4);
        verticalLayout_10->setObjectName(QString::fromUtf8("verticalLayout_10"));
        horizontalLayout_46 = new QHBoxLayout();
        horizontalLayout_46->setObjectName(QString::fromUtf8("horizontalLayout_46"));
        label_v = new QLabel(widget_4);
        label_v->setObjectName(QString::fromUtf8("label_v"));
        label_v->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter);

        horizontalLayout_46->addWidget(label_v);

        comboBox_v_data = new QComboBox(widget_4);
        comboBox_v_data->addItem(QString());
        comboBox_v_data->addItem(QString());
        comboBox_v_data->addItem(QString());
        comboBox_v_data->addItem(QString());
        comboBox_v_data->setObjectName(QString::fromUtf8("comboBox_v_data"));

        horizontalLayout_46->addWidget(comboBox_v_data);

        horizontalSpacer_2 = new QSpacerItem(100, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_46->addItem(horizontalSpacer_2);

        horizontalLayout_46->setStretch(0, 1);
        horizontalLayout_46->setStretch(1, 2);

        verticalLayout_10->addLayout(horizontalLayout_46);

        tableView_v = new QTableView(widget_4);
        tableView_v->setObjectName(QString::fromUtf8("tableView_v"));

        verticalLayout_10->addWidget(tableView_v);

        splitter_2->addWidget(widget_4);
        widget_3 = new QWidget(splitter_2);
        widget_3->setObjectName(QString::fromUtf8("widget_3"));
        verticalLayout_9 = new QVBoxLayout(widget_3);
        verticalLayout_9->setObjectName(QString::fromUtf8("verticalLayout_9"));
        label_v_2 = new QLabel(widget_3);
        label_v_2->setObjectName(QString::fromUtf8("label_v_2"));
        label_v_2->setAlignment(Qt::AlignCenter);

        verticalLayout_9->addWidget(label_v_2);

        tableView_v_calibration = new QTableView(widget_3);
        tableView_v_calibration->setObjectName(QString::fromUtf8("tableView_v_calibration"));

        verticalLayout_9->addWidget(tableView_v_calibration);

        splitter_2->addWidget(widget_3);

        verticalLayout_12->addWidget(splitter_2);


        horizontalLayout_47->addWidget(widget_v);

        splitter_3->addWidget(widget_kv_data);

        horizontalLayout_12->addWidget(splitter_3);


        horizontalLayout_49->addWidget(widget);

        horizontalLayout_49->setStretch(0, 1);
        horizontalLayout_49->setStretch(1, 10);

        retranslateUi(FMGWidget);

        stackedWidget_FMG->setCurrentIndex(1);


        QMetaObject::connectSlotsByName(FMGWidget);
    } // setupUi

    void retranslateUi(QWidget *FMGWidget)
    {
        FMGWidget->setWindowTitle(QCoreApplication::translate("FMGWidget", "Form", nullptr));
        pushButton_weather_six->setText(QCoreApplication::translate("FMGWidget", "\346\260\224\350\261\241\345\205\255\350\246\201\347\264\240", nullptr));
        pushButton_k_voltage->setText(QCoreApplication::translate("FMGWidget", "K\351\200\232\351\201\223\347\224\265\345\216\213", nullptr));
        pushButton_v_voltage->setText(QCoreApplication::translate("FMGWidget", "V\351\200\232\351\201\223\347\224\265\345\216\213", nullptr));
        pushButton_k_temp->setText(QCoreApplication::translate("FMGWidget", "K\351\200\232\351\201\223\346\270\251\345\272\246", nullptr));
        pushButton_v_temp->setText(QCoreApplication::translate("FMGWidget", "V\351\200\232\351\201\223\346\270\251\345\272\246", nullptr));
        pushButton_all_temp_voltage->setText(QCoreApplication::translate("FMGWidget", "\345\216\237\345\247\213\346\225\260\346\215\256\345\205\250\351\203\250\346\230\276\347\244\272", nullptr));
        pushButton_k_light->setText(QCoreApplication::translate("FMGWidget", "K\351\200\232\351\201\223\344\272\256\346\270\251", nullptr));
        pushButton_v_light->setText(QCoreApplication::translate("FMGWidget", "V\351\200\232\351\201\223\344\272\256\346\270\251", nullptr));
        pushButton_rain->setText(QCoreApplication::translate("FMGWidget", "\351\231\215\351\233\250\351\207\217", nullptr));
        pushButton_normal_temp->setText(QCoreApplication::translate("FMGWidget", "\345\270\270\346\270\251\346\272\220", nullptr));
        pushButton_all_light->setText(QCoreApplication::translate("FMGWidget", "\344\272\256\346\270\251\346\225\260\346\215\256\345\205\250\351\203\250\346\230\276\347\244\272", nullptr));
        pushButton_k_show_data->setText(QCoreApplication::translate("FMGWidget", "K\351\200\232\351\201\223\346\225\260\346\215\256\346\230\276\347\244\272", nullptr));
        pushButton_v_show_data->setText(QCoreApplication::translate("FMGWidget", "V\351\200\232\351\201\223\346\225\260\346\215\256\346\230\276\347\244\272", nullptr));
        pushButton_k_3000->setText(QString());
        label_k_3000->setText(QCoreApplication::translate("FMGWidget", "30.00GHZ", nullptr));
        pushButton_k_2224->setText(QString());
        label_k_2224->setText(QCoreApplication::translate("FMGWidget", "22.24GHZ", nullptr));
        pushButton_k_2544->setText(QString());
        label_k_2544->setText(QCoreApplication::translate("FMGWidget", "25.44GHZ", nullptr));
        pushButton_k_2304->setText(QString());
        label_k_2304->setText(QCoreApplication::translate("FMGWidget", "23.04GHZ", nullptr));
        pushButton_k_2384->setText(QString());
        label_k_2384->setText(QCoreApplication::translate("FMGWidget", "23.04GHZ", nullptr));
        pushButton_k_all_show->setText(QCoreApplication::translate("FMGWidget", "\346\273\241\345\261\217", nullptr));
        pushButton_k_2784->setText(QString());
        label_k_2784->setText(QCoreApplication::translate("FMGWidget", "27.84GHZ", nullptr));
        pushButton_k_3140->setText(QString());
        label_k_3140->setText(QCoreApplication::translate("FMGWidget", "31.40GHZ", nullptr));
        pushButton_k_clear->setText(QCoreApplication::translate("FMGWidget", "\346\270\205\351\231\244", nullptr));
        pushButton_k_stop->setText(QCoreApplication::translate("FMGWidget", "\346\232\202\345\201\234", nullptr));
        pushButton_k_2624->setText(QString());
        label_k_2624->setText(QCoreApplication::translate("FMGWidget", "26.24GHZ", nullptr));
        pushButton_v_5126->setText(QString());
        label_v_5126->setText(QCoreApplication::translate("FMGWidget", "51.26GHZ", nullptr));
        pushButton_v_5228->setText(QString());
        label_v_5228->setText(QCoreApplication::translate("FMGWidget", "52.28GHZ", nullptr));
        pushButton_v_5386->setText(QString());
        label_v_5386->setText(QCoreApplication::translate("FMGWidget", "53.86GHZ", nullptr));
        pushButton_v_5494->setText(QString());
        label_v_5494->setText(QCoreApplication::translate("FMGWidget", "54.94GHZ", nullptr));
        pushButton_v_5550->setText(QString());
        label_v_5550->setText(QCoreApplication::translate("FMGWidget", "55.50GHZ", nullptr));
        pushButton_v_5666->setText(QString());
        label_v_5666->setText(QCoreApplication::translate("FMGWidget", "56.66GHZ", nullptr));
        pushButton_v_5730->setText(QString());
        label_v_5730->setText(QCoreApplication::translate("FMGWidget", "57.30GHZ", nullptr));
        pushButton_v_5800->setText(QString());
        label_v_5800->setText(QCoreApplication::translate("FMGWidget", "58.00GHZ", nullptr));
        pushButton_v_stop->setText(QCoreApplication::translate("FMGWidget", "\346\232\202\345\201\234", nullptr));
        pushButton_v_all_show->setText(QCoreApplication::translate("FMGWidget", "\346\273\241\345\261\217", nullptr));
        pushButton_v_clear->setText(QCoreApplication::translate("FMGWidget", "\346\270\205\351\231\244", nullptr));
        pushButton_k_temp1->setText(QString());
        label_k_temp1->setText(QCoreApplication::translate("FMGWidget", "\346\270\251\345\272\2461", nullptr));
        pushButton_k_temp2->setText(QString());
        label_k_temp2->setText(QCoreApplication::translate("FMGWidget", "\346\270\251\345\272\2462", nullptr));
        pushButton_k_temp3->setText(QString());
        label_k_temp3->setText(QCoreApplication::translate("FMGWidget", "\346\270\251\345\272\2463", nullptr));
        pushButton_k_temp_stop->setText(QCoreApplication::translate("FMGWidget", "\346\232\202\345\201\234", nullptr));
        pushButton_k_temp_all_show->setText(QCoreApplication::translate("FMGWidget", "\346\273\241\345\261\217", nullptr));
        pushButton_k_temp_clear->setText(QCoreApplication::translate("FMGWidget", "\346\270\205\351\231\244", nullptr));
        pushButton_v_temp1->setText(QString());
        label_v_temp1->setText(QCoreApplication::translate("FMGWidget", "\346\270\251\345\272\2461", nullptr));
        pushButton_v_temp2->setText(QString());
        label_v_temp2->setText(QCoreApplication::translate("FMGWidget", "\346\270\251\345\272\2461", nullptr));
        pushButton_v_temp3->setText(QString());
        label_v_temp3->setText(QCoreApplication::translate("FMGWidget", "\346\270\251\345\272\2461", nullptr));
        pushButton_v_temp_stop->setText(QCoreApplication::translate("FMGWidget", "\346\232\202\345\201\234", nullptr));
        pushButton_v_temp_all_show->setText(QCoreApplication::translate("FMGWidget", "\346\273\241\345\261\217", nullptr));
        pushButton_v_temp_clear->setText(QCoreApplication::translate("FMGWidget", "\346\270\205\351\231\244", nullptr));
        pushButton_k_light_2224->setText(QString());
        label_k_light_2224->setText(QCoreApplication::translate("FMGWidget", "22.24GHZ", nullptr));
        pushButton_k_light_2304->setText(QString());
        label_k_light_2304->setText(QCoreApplication::translate("FMGWidget", "23.04GHZ", nullptr));
        pushButton_k_light_2384->setText(QString());
        label_k_light_2384->setText(QCoreApplication::translate("FMGWidget", "23.84GHZ", nullptr));
        pushButton_k_light_2544->setText(QString());
        label_k_light_2544->setText(QCoreApplication::translate("FMGWidget", "25.44GHZ", nullptr));
        pushButton_k_light_2624->setText(QString());
        label_k_light_2624->setText(QCoreApplication::translate("FMGWidget", "26.24GHZ", nullptr));
        pushButton_k_light_2784->setText(QString());
        label_k_light_2784->setText(QCoreApplication::translate("FMGWidget", "27.84GHZ", nullptr));
        pushButton_k_light_3000->setText(QString());
        label_k_light_3000->setText(QCoreApplication::translate("FMGWidget", "30.00GHZ", nullptr));
        pushButton_k_light_3140->setText(QString());
        label_k_light_3140->setText(QCoreApplication::translate("FMGWidget", "31.40GHZ", nullptr));
        pushButton_k_light_stop->setText(QCoreApplication::translate("FMGWidget", "\346\232\202\345\201\234", nullptr));
        pushButton_k_light_all_show->setText(QCoreApplication::translate("FMGWidget", "\346\273\241\345\261\217", nullptr));
        pushButton_k_light_clear->setText(QCoreApplication::translate("FMGWidget", "\346\270\205\351\231\244", nullptr));
        pushButton_v_light_5126->setText(QString());
        label_v_light_5126->setText(QCoreApplication::translate("FMGWidget", "51.26GHZ", nullptr));
        pushButton_v_light_5228->setText(QString());
        label_v_light_5228->setText(QCoreApplication::translate("FMGWidget", "52.28GHZ", nullptr));
        pushButton_v_light_5386->setText(QString());
        label_v_light_5386->setText(QCoreApplication::translate("FMGWidget", "53.86GHZ", nullptr));
        pushButton_v_light_5494->setText(QString());
        label_v_light_5494->setText(QCoreApplication::translate("FMGWidget", "54.94GHZ", nullptr));
        pushButton_v_light_5550->setText(QString());
        label_v_light_5550->setText(QCoreApplication::translate("FMGWidget", "55.50GHZ", nullptr));
        pushButton_v_light_5666->setText(QString());
        label_v_light_5666->setText(QCoreApplication::translate("FMGWidget", "56.66GHZ", nullptr));
        pushButton_v_light_5730->setText(QString());
        label_v_light_5730->setText(QCoreApplication::translate("FMGWidget", "57.30GHZ", nullptr));
        pushButton_v_light_5800->setText(QString());
        label_v_light_5800->setText(QCoreApplication::translate("FMGWidget", "58.00GHZ", nullptr));
        pushButton_v_light_stop->setText(QCoreApplication::translate("FMGWidget", "\346\232\202\345\201\234", nullptr));
        pushButton_v_light_all_show->setText(QCoreApplication::translate("FMGWidget", "\346\273\241\345\261\217", nullptr));
        pushButton_v_light_clear->setText(QCoreApplication::translate("FMGWidget", "\346\270\205\351\231\244", nullptr));
        pushButton_light_rain->setText(QString());
        label_light_rain->setText(QCoreApplication::translate("FMGWidget", "\351\233\250\351\207\217", nullptr));
        pushButton_light_rain_stop->setText(QCoreApplication::translate("FMGWidget", "\346\232\202\345\201\234", nullptr));
        pushButton_light_rain_all_show->setText(QCoreApplication::translate("FMGWidget", "\346\273\241\345\261\217", nullptr));
        pushButton_light_rain_clear->setText(QCoreApplication::translate("FMGWidget", "\346\270\205\351\231\244", nullptr));
        pushButton_light_temp->setText(QString());
        label_light_temp->setText(QCoreApplication::translate("FMGWidget", "\346\270\251\345\272\246", nullptr));
        pushButton_light_temp_stop->setText(QCoreApplication::translate("FMGWidget", "\346\232\202\345\201\234", nullptr));
        pushButton_light_temp_all_show->setText(QCoreApplication::translate("FMGWidget", "\346\273\241\345\261\217", nullptr));
        pushButton_light_temp_clear->setText(QCoreApplication::translate("FMGWidget", "\346\270\205\351\231\244", nullptr));
        label_k->setText(QCoreApplication::translate("FMGWidget", "k\351\200\232\351\201\223\347\224\265\345\216\213\346\225\260\346\215\256", nullptr));
        comboBox_k_data->setItemText(0, QCoreApplication::translate("FMGWidget", "5\345\210\206\351\222\237", nullptr));
        comboBox_k_data->setItemText(1, QCoreApplication::translate("FMGWidget", "10\345\210\206\351\222\237", nullptr));
        comboBox_k_data->setItemText(2, QCoreApplication::translate("FMGWidget", "30\345\210\206\351\222\237", nullptr));
        comboBox_k_data->setItemText(3, QCoreApplication::translate("FMGWidget", "1\345\260\217\346\227\266", nullptr));

        label_k_2->setText(QCoreApplication::translate("FMGWidget", "\345\256\232\346\240\207\346\225\260\346\215\256", nullptr));
        label_v->setText(QCoreApplication::translate("FMGWidget", "v\351\200\232\351\201\223\347\224\265\345\216\213\346\225\260\346\215\256", nullptr));
        comboBox_v_data->setItemText(0, QCoreApplication::translate("FMGWidget", "5\345\210\206\351\222\237", nullptr));
        comboBox_v_data->setItemText(1, QCoreApplication::translate("FMGWidget", "10\345\210\206\351\222\237", nullptr));
        comboBox_v_data->setItemText(2, QCoreApplication::translate("FMGWidget", "30\345\210\206\351\222\237", nullptr));
        comboBox_v_data->setItemText(3, QCoreApplication::translate("FMGWidget", "1\345\260\217\346\227\266", nullptr));

        label_v_2->setText(QCoreApplication::translate("FMGWidget", "\345\256\232\346\240\207\346\225\260\346\215\256", nullptr));
    } // retranslateUi

};

namespace Ui {
    class FMGWidget: public Ui_FMGWidget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FMGWIDGET_H
