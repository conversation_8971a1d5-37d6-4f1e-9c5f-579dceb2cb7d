﻿#include "SystemRegister.h"
#include "ui_SystemRegister.h"
#include "qmessagebox.h"
#include "tools/Global.h"
#include "qstatusbar.h"
SystemRegister::SystemRegister(QWidget *parent) : QWidget(parent),
                                                  ui(new Ui::SystemRegister)
{
    ui->setupUi(this);
    this->setWindowTitle("微波辐射计登录界面");
    ui->lineEdit_user_name->setPlaceholderText("请输入6~20位用户名");
    ui->lineEdit_user_password->setPlaceholderText("请输入6~20位密码");
    ui->lineEdit_user_password->setEchoMode(QLineEdit::Password);
    ui->lineEdit_add_user_name->setPlaceholderText("请输入6~20位用户名");
    ui->lineEdit_add_user_password->setPlaceholderText("请输入6~20位密码");
    ui->lineEdit_delect_user_name->setPlaceholderText("请输入6~20位用户名");
    ui->lineEdit_delect_user_password->setPlaceholderText("请输入6~20位密码");
    ui->lineEdit_alter_user_name->setPlaceholderText("请输入6~20位用户名");
    ui->lineEdit_alter_user_oldpassword->setPlaceholderText("请输入6~20位旧密码");
    ui->lineEdit_alter_user_newpassword->setPlaceholderText("请输入6~20位新密码");

    USER_data = new sqlite_USER_data("SqliteData/");
    ui->pushButton_return->hide();
    ui->stackedWidget->setCurrentIndex(0);

    display1 = new QLabel("0/6", ui->lineEdit_user_name);
    display2 = new QLabel("0/6", ui->lineEdit_user_password);
    QString labelStyle = "border:none;color:#cccccc;font:9pt '微软雅黑';";
    display1->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    display1->setStyleSheet(labelStyle);
    display1->setFixedSize(50, 25);
    display2->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    display2->setStyleSheet(labelStyle);
    display2->setFixedSize(50, 25);
    // 初始位置
    display1->move(ui->lineEdit_user_name->width() - 50, 2);
    display2->move(ui->lineEdit_user_password->width() - 50, 2);

    // USER_data->use_CustomSqlTableModel(ui->tableView);
}

SystemRegister::~SystemRegister()
{
    delete ui;
    delete display1;
    delete display2;
    delete USER_data;
}
void SystemRegister::set_textChange_status(const QString &text)
{
    QLineEdit *edit = qobject_cast<QLineEdit *>(sender());
    if (!edit)
        return;
    QLabel *currentLabel = nullptr;
    if (edit == ui->lineEdit_user_name)
    {
        currentLabel = display1;
    }
    else if (edit == ui->lineEdit_user_password)
    {
        currentLabel = display2;
    }
    else
    {
        return;
    }

    const int len = text.length();
    const int minLength = 6;
    const int maxLength = 20;
    if (len > maxLength)
    {
        edit->blockSignals(true);
        edit->setText(text.left(maxLength));
        edit->blockSignals(false);
        QMessageBox::warning(this, "警告", QString("长度不能不能超过%1位").arg(maxLength));
        currentLabel->setText(QString("%1/%2").arg(maxLength).arg(maxLength));
        currentLabel->setStyleSheet("color:#FF5050;font:9pt '微软雅黑';");
        return;
    }
    const QString lengthText = QString("%1/%2").arg(len).arg(maxLength);
    QString color = (len <= minLength) ? "#FF5050" : "#ffaa00";
    currentLabel->setText(lengthText);
    currentLabel->setStyleSheet(QString("color:%1;font:9pt '微软雅黑';").arg(color));
}
void SystemRegister::open_qmessage(QString title, QString message, QString sure)
{
    QMessageBox msgbox;
    msgbox.setWindowTitle(title);
    msgbox.setText(message);
    if (title == "警告")
    {
        msgbox.setIcon(QMessageBox::Warning);
    }
    else
    {
        msgbox.setIcon(QMessageBox::Question);
    }
    msgbox.addButton(sure, QMessageBox::ActionRole);
    msgbox.exec();
}

void SystemRegister::on_pushButton_Register_clicked()
{
    struct USER_SQ_GROUP temp = {ui->lineEdit_user_name->text(), ui->lineEdit_user_password->text()};
    int re = USER_data->log_in(temp);
    qDebug() << re;

    if (re == 0)
    {
        open_qmessage("提示", "登录成功!", "确定");
        Login_validation = true;
        emit signal_CalibrationParameterdata();
    }
    if (re == 1)
    {
        open_qmessage("提示", "用户不存在，请从新输入!", "确定");
    }
    else if (re == 2)
    {
        open_qmessage("提示", "密码不符合要求!", "确定");
    }
    else if (re == 3)
    {
        open_qmessage("提示", "用户名不符合要求!", "确定");
    }
    else if (re == 4)
    {
        open_qmessage("提示", "密码错误!", "确定");
    }
}
void SystemRegister::on_pushButton_delect_user_clicked()
{
    struct USER_SQ_GROUP temp = {ui->lineEdit_delect_user_name->text(), ui->lineEdit_delect_user_password->text()};
    int re = USER_data->log_in(temp);
    qDebug() << re;

    if (re != 0)
    {
        open_qmessage("提示", "用户不存在，删除失败！", "确定");
    }
    else
    {

        struct USER_SQ_GROUP temp = {ui->lineEdit_user_name->text(), ui->lineEdit_user_password->text(), 1};
        int re = USER_data->delete_user(temp, ui->lineEdit_delect_user_name->text());

        open_qmessage("提示", "该用户存在，用户删除成功", "确定");
    }
}

void SystemRegister::on_pushButton_add_user_clicked()
{
    struct USER_SQ_GROUP temp = {ui->lineEdit_add_user_name->text(), ui->lineEdit_add_user_password->text(), ui->spinBox_add->value(), ui->lineEdit_text_add->text(), "beijing"};
    int re = USER_data->new_user(temp);
    qDebug() << re;

    if (re == 0)
    {
        open_qmessage("提示", "用户添加成功!", "确定");
    }
    else if (re == 1)
    {
        open_qmessage("提示", "该用户已存在!", "确定");
    }
    else if (re == 2)
    {
        open_qmessage("提示", "密码不符合要求(必须是数字加字母组合)!", "确定");
    }
    else if (re == 3)
    {
        open_qmessage("提示", "用户名不符合要求(必须是数字加字母组合)!", "确定");
    }
    else if (re == 4)
    {
        open_qmessage("提示", "权限不符合要求!", "确定");
    }
}
void SystemRegister::on_pushButton_alter_make_clicked()
{
    struct USER_SQ_GROUP temp = {ui->lineEdit_alter_user_name->text(), ui->lineEdit_alter_user_oldpassword->text(), ui->spinBox_alter->value()};
    int re = USER_data->log_in(temp);
    qDebug() << re;
    if (re == 0)
    {
        struct USER_SQ_GROUP temp = {ui->lineEdit_alter_user_name->text(), ui->lineEdit_alter_user_newpassword->text(), 1};

        USER_data->update_user(temp);

        open_qmessage("提示", "密码修改成功！", "确定");
    }
    else if (re == 1)
    {
        open_qmessage("提示", "用户不存在，请从新输入!", "确定");
    }
    else if (re == 2)
    {
        open_qmessage("提示", "密码不符合要求!", "确定");
    }
    else if (re == 3)
    {
        open_qmessage("提示", "用户名不符合要求!", "确定");
    }
    else if (re == 4)
    {
        open_qmessage("提示", "密码错误!", "确定");
    }
}
void SystemRegister::on_pushButton_register_clicked()
{
    ui->stackedWidget->setCurrentIndex(1);
    ui->pushButton_return->show();
}

void SystemRegister::on_pushButton_remove_clicked()
{
    ui->stackedWidget->setCurrentIndex(2);
    ui->pushButton_return->show();
}

void SystemRegister::on_pushButton_return_clicked()
{
    ui->stackedWidget->setCurrentIndex(0);
    ui->pushButton_return->hide();
}

void SystemRegister::on_pushButton_alter_clicked()
{
    ui->stackedWidget->setCurrentIndex(3);
    ui->pushButton_return->show();
}

void SystemRegister::on_lineEdit_user_name_textEdited(const QString &arg1)
{
    set_textChange_status(arg1);
}

void SystemRegister::on_lineEdit_user_password_textEdited(const QString &arg1)
{
    set_textChange_status(arg1);
}
