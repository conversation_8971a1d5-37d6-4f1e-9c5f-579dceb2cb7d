﻿#ifndef TCPCLIENTINTERFACE_H
#define TCPCLIENTINTERFACE_H

#include <QObject>
#include <QTcpSocket>
#include <QString>

/**
 * @brief Qt TCP客户端接口类
 * 提供TCP客户端连接、断开、数据收发等操作
 */
class TcpClientInterface : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit TcpClientInterface(QObject *parent = nullptr);

    /**
     * @brief 析构函数，自动断开连接
     */
    ~TcpClientInterface();

    /**
     * @brief 连接到服务器
     * @param host 服务器地址
     * @param port 服务器端口
     * @return 成功返回true，失败返回false
     */
    bool connectToHost(const QString &host, quint16 port);

    /**
     * @brief 断开连接
     */
    void disconnectFromHost();

    /**
     * @brief 检查是否已连接
     * @return 已连接返回true，否则false
     */
    bool isConnected() const;

    /**
     * @brief 发送数据
     * @param data 要发送的数据
     * @return 成功发送的字节数，-1表示失败
     */
    qint64 sendData(const QByteArray &data);

    /**
     * @brief 读取数据
     * @param timeout 超时时间(毫秒)，-1表示无限等待
     * @return 读取到的数据
     */
    QByteArray readData(int timeout = -1);

signals:
    /**
     * @brief 连接成功信号
     */
    void connected();

    /**
     * @brief 断开连接信号
     */
    void disconnected();

    /**
     * @brief 数据接收信号
     * @param data 接收到的数据
     */
    void dataReceived(const QByteArray &data);

    /**
     * @brief 错误发生信号
     * @param error 错误信息
     */
    void errorOccurred(const QString &error);

private slots:
    void handleConnected();
    void handleDisconnected();
    void handleReadyRead();
    void handleError(QAbstractSocket::SocketError error);

private:
    QTcpSocket *m_tcpSocket; // TCP套接字对象
};

#endif // TCPCLIENTINTERFACE_H