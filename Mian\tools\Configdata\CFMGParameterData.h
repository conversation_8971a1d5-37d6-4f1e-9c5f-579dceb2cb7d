﻿#ifndef CFMGPARAMETERDATA_H
#define CFMGPARAMETERDATA_H

#include <QObject>
#include <QString>
#include <QFile>
#include <QSettings>
struct FMGParatemerData
{
    // 参数设置
    QString level1;
    QString level2;
    QString mast_angle;
    QString mast_ratio;
    QString mast_speed;
    QString mast_angle_compensate;
    double mast_target_angle;
    QString wind_speed;
    double wind_pw;

    int agreement_choice;

    QString fanye_path;
    QString look_path;

    double data_filter;
    double calculate_filter;
    double normal_temp_angle;
    double noise_angle;
    double sky_angle;
    double stop_time;
    double interval_time;
    int calibration_Mode;

    double var_data;
    double num_limit;
    double time_limit;

    // 采集温控
    double parameter_p1;
    double parameter_I1;
    double parameter_D1;
    double target_temp1;
    QString current_temp1;
    QString current_pw1;

    double parameter_p2;
    double parameter_I2;
    double parameter_D2;
    double target_temp2;
    QString current_temp2;
    QString current_pw2;
    // 设备数据
};
class CFMGParameterData : public QObject
{
    Q_OBJECT
public:
    explicit CFMGParameterData(QObject *parent = nullptr);
    ~CFMGParameterData();
    void saveToFile();   // 保存数据到文件中
    void loadFromFile(); // 加载文件中数据
    FMGParatemerData fmgdata;
    void creat_configfile();

signals:

public slots:
};

#endif // CFMGPARAMETERDATA_H
