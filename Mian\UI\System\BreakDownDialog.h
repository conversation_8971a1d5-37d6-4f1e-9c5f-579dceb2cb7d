﻿#ifndef BREAKDOWNDIALOG_H
#define BREAKDOWNDIALOG_H

#include <QDialog>
#include "qcustomplot.h"
#include <QtCharts>
namespace Ui
{
    class BreakDownDialog;
}

class BreakDownDialog : public QDialog
{
    Q_OBJECT

public:
    explicit BreakDownDialog(QWidget* parent = nullptr);
    ~BreakDownDialog();
    void CreatpieSetView();
    void Init_widget_graph_view();
public slots:
    void OnSliceClicked();
private slots:

    void on_pushButton_query_clicked();

    void on_pushButton_delect_clicked();

    void on_pushButton_Fault_add_clicked();

private:
    Ui::BreakDownDialog* ui;
    QTimer* auto_delect_data;
};

#endif // BREAKDOWNDIALOG_H
