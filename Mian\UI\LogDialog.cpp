﻿#include "LogDialog.h"
#include "ui_LogDialog.h"
#include "tools/Global.h"
#include <QMessageBox>
#include <QDateTime>
#include <QRegularExpression>
LogDialog::LogDialog(QWidget* parent) : QDialog(parent),
ui(new Ui::LogDialog)
{
    ui->setupUi(this);
    this->setWindowTitle("日志记录");
    this->setWindowFlags(this->windowFlags() | Qt::WindowMaximizeButtonHint);
    this->setWindowFlags(Qt::Window);

    StateRecord_data_txt = new txt_staterecord_data();
    StateRecord_data_txt->use_QStandardItemModel(ui->tableView_log, "日志文件/");



    auto_delect_data = new QTimer();
    connect(auto_delect_data, SIGNAL(timeout()), this, SLOT(slot_auto_delect_data())); //数据自动清理
    auto_delect_data->start(1000);

    time_table_data = new QTimer();

    connect(time_table_data, SIGNAL(timeout()), this, SLOT(set_time_table_data()));

    //time_table_data->start(5000);



    ui->pushButton_clear->hide();
    ui->pushButton_status_add->hide();
    ui->pushButton_status_delete->hide();
    ui->pushButton_status_save->hide();
    ui->lineEdit_status_delete->hide();
    ui->lineEdit_status->hide();
    ui->lineEdit_status_save_time->hide();

}

LogDialog::~LogDialog()
{
    delete ui;
    delete StateRecord_data_txt;
    delete time_table_data;
}

void LogDialog::open_qmessage(QString title, QString message, QString sure)
{
    QMessageBox msgbox;
    msgbox.setWindowTitle(title);
    msgbox.setText(message);
    if (title == "警告")
    {
        msgbox.setIcon(QMessageBox::Warning);
    }
    else
    {
        msgbox.setIcon(QMessageBox::Question);
    }
    msgbox.addButton(sure, QMessageBox::ActionRole);
    msgbox.exec();
}

QColor LogDialog::rgbStringToColor(const QString& rgbString)
{
    //    QString clean = rgbString.toLower().remove(" ");

    //    if(!clean.startsWith("rgb(") || !clean.endsWith(")"))
    //    {
    //        return QColor();
    //    }
    //    QString content = clean.mid(4,clean.length()-5);
    //    //分割逗号分割的值
    //    QStringList parts = content.split(",");
    //    if(parts.size()!=3)
    //    {
    //        return QColor();
    //    }
    //    //转换为整数
    //    bool ok;
    //    int r = parts[0].toInt(&ok);
    //    if(!ok || r<0 || r>255)
    //    return QColor();

    //    int g = parts[1].toInt(&ok);
    //    if(!ok || g<0 || g>255)
    //    return QColor();

    //    int b = parts[1].toInt(&ok);
    //    if(!ok || b<0 || b>255)
    //    return QColor();

    //    return QColor(r,g,b);

        //\d{1,3}匹配1~3位数字  \s*允许有空格
    QRegularExpression regex(R"(rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\))", QRegularExpression::CaseInsensitiveOption);//匹配不区分大小写

    QRegularExpressionMatch match = regex.match(rgbString);
    if (!match.hasMatch())
    {
        return QColor();
    }

    bool ok;
    int r = match.captured(1).toInt(&ok);
    if (!ok || r < 0 || r>255)
        return QColor();

    int g = match.captured(2).toInt(&ok);
    if (!ok || g < 0 || g>255)
        return QColor();

    int b = match.captured(3).toInt(&ok);
    if (!ok || b < 0 || b>255)
        return QColor();

    return QColor(r, g, b);

}

void LogDialog::on_pushButton_status_add_clicked()
{

}

void LogDialog::on_pushButton_status_delete_clicked()
{

    //删除所有早于当前时间之前的子目录
    QDateTime month_file = QDateTime::currentDateTime().addDays(-5);
    QString  strTime = month_file.toString("yyyy-MM-dd");
    StateRecord_data_txt->deleteOldFiles(systemconfigfdata.systemdata.save_log_path + "/日志文件", strTime);

}

void LogDialog::on_pushButton_clear_clicked()
{
    StateRecord_data_txt->clear_StandardItemModel_data();
}

void LogDialog::on_pushButton_status_save_clicked()
{

}

void LogDialog::slot_auto_delect_data()
{

    //    QDateTime current = QDateTime::currentDateTime();
    //    QTime now_time = current.time();
    //    QDate today_date = current.date();
    //    QDate last_trigger_today;
    //    if (now_time >= QTime(23, 59, 59) && now_time <= QTime(00, 00, 00) && today_date != last_trigger_today)
    //    {
    //        StateRecord_data_txt->save_StandardItemModelTotxt("beijing");
    //        StateRecord_data_txt->clear_StandardItemModel_data();
    //        last_trigger_today = today_date;
    //        qDebug() << current.toString();
    //    }

        //删除所有早于当前时间之前的子目录
    QDateTime month_file = QDateTime::currentDateTime().addDays(-(systemconfigfdata.systemdata.log_day));

    StateRecord_data_txt->deleteOldFiles(systemconfigfdata.systemdata.save_log_path + "/日志文件", month_file.toString("yyyy-MM-dd"));
}

void LogDialog::set_time_table_data()
{
    QDateTime current = QDateTime::currentDateTime();
    QString time_data = current.toString("yyyy-MM-dd HH:mm:ss");
    txt_staterecord_data::StateRecord_SQ_GROUP data;
    data.content = "一般错误:辐射计读取数据为0";
    data.time = time_data;
    StateRecord_data_txt->insertdata(data);
    StateRecord_data_txt->save_StandardItemModelTotxt("beijing");

    if (data.content.left(4) == "一般错误")
    {
        QVector<QColor> c;
        QColor color = rgbStringToColor(systemconfigfdata.systemdata.general_error);
        c.append(color);
        c.append(color);
        StateRecord_data_txt->set_column_color(c);
    }
    else if (data.content.left(4) == "致命错误")
    {
        QVector<QColor> c;
        QColor color = rgbStringToColor(systemconfigfdata.systemdata.dead_error);
        c.append(color);
        c.append(color);
        StateRecord_data_txt->set_column_color(c);
    }
    else if (data.content.left(4) == "一般信息")
    {
        QVector<QColor> c;
        QColor color = rgbStringToColor(systemconfigfdata.systemdata.general_information);
        c.append(color);
        c.append(color);
        StateRecord_data_txt->set_column_color(c);
    }
    else if (data.content.left(4) == "调试信息")
    {
        QVector<QColor> c;
        QColor color = rgbStringToColor(systemconfigfdata.systemdata.debug_information);
        c.append(color);
        c.append(color);
        StateRecord_data_txt->set_column_color(c);
    }
    else if (data.content.left(4) == "警告信息")
    {
        QVector<QColor> c;
        QColor color = rgbStringToColor(systemconfigfdata.systemdata.warn_information_error);
        c.append(color);
        c.append(color);
        StateRecord_data_txt->set_column_color(c);
    }



}

void LogDialog::set_logRecord_color(QColor color)
{
    QVector<QColor> c;
    c.append(color);
    c.append(color);

    StateRecord_data_txt->set_column_color(c);
}
void LogDialog::add_Operational_information(QString strdata)
{
    QDateTime current = QDateTime::currentDateTime();
    QString time_data = current.toString("yyyy-MM-dd HH:mm:ss");
    txt_staterecord_data::StateRecord_SQ_GROUP data;
    data.time = time_data;
    data.content = strdata;
    StateRecord_data_txt->insertdata(data);
    StateRecord_data_txt->save_StandardItemModelTotxt("beijing");

    if (data.content.left(4) == "一般错误")
    {
        QVector<QColor> c;
        QColor color = rgbStringToColor(systemconfigfdata.systemdata.general_error);
        c.append(color);
        c.append(color);
        StateRecord_data_txt->set_column_color(c);
    }
    else if (data.content.left(4) == "致命错误")
    {
        QVector<QColor> c;
        QColor color = rgbStringToColor(systemconfigfdata.systemdata.dead_error);
        c.append(color);
        c.append(color);
        StateRecord_data_txt->set_column_color(c);
    }
    else if (data.content.left(4) == "一般信息")
    {
        QVector<QColor> c;
        QColor color = rgbStringToColor(systemconfigfdata.systemdata.general_information);
        c.append(color);
        c.append(color);
        StateRecord_data_txt->set_column_color(c);
    }
    else if (data.content.left(4) == "调试信息")
    {
        QVector<QColor> c;
        QColor color = rgbStringToColor(systemconfigfdata.systemdata.debug_information);
        c.append(color);
        c.append(color);
        StateRecord_data_txt->set_column_color(c);
    }
    else if (data.content.left(4) == "警告信息")
    {
        QVector<QColor> c;
        QColor color = rgbStringToColor(systemconfigfdata.systemdata.warn_information_error);
        c.append(color);
        c.append(color);
        StateRecord_data_txt->set_column_color(c);
    }
}
