﻿#pragma once

#include <QtWidgets/QWidget>
#include "ui_FMGParameterDialog.h"
#include <qtimer.h>

class FMGParameterDialog : public QWidget
{
    Q_OBJECT

public:
    
    FMGParameterDialog(QWidget* parent = nullptr);
    ~FMGParameterDialog();



private slots:
    // 读取设置槽函数
    void slot_read_param();

    // 写入设置槽函数
    void slot_write_param();


    // 读取观测参数槽
    void slot_read_obsPara();

    // 读取温控参数槽
    void slot_read_tempCtrlPara();

    void on_comboBox_auto_real_time(int index);

private:
    Ui::FMGParameterDialogClass ui;
    uint UiID;
};
