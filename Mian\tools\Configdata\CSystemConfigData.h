﻿#ifndef CSYSTEMCONFIGDATA_H
#define CSYSTEMCONFIGDATA_H

#include <QObject>
#include <QString>
#include <QFile>
#include <QSettings>
#include <QColor>
struct SystemConfigData
{
    // 数据存储路径设置
    QString save_log_path;
    QString dingbiao_path;
    QString status_path;
    QString LV1_path;
    QString LV2_path;
    QString ceyun_path;

    int lv1_file_format;

    int lv2_file_format;

    int status_data_file_format;
    int calibrate_data_file_format;

    int status_data_file_format_xml;
    int calibrate_data_file_format_xml;
    // 皮肤设置
    int set_skin;
    QString dead_error;//致命错误
    QString general_information;//一般信息
    QString general_error;//一般错误
    QString debug_information;//调试信息
    QString warn_information_error;//警告信息


    // 站点设置
    QString shengming;
    QString station_name;
    QString station_number;
    QString weather_number;
    QString business_number;
    double ground_hieght;
    double mast_height;
    double point;
    QString longitude_angle;
    QString longitude_minute;
    QString longitude_second;
    QString latitude_angle;
    QString latitude_minute;
    QString latitude_second;

    // 数据管理
    double data_save_time;
    double space_limit;
    double jiao_hour;
    double jiao_minute;
    double jiao_second;
    QString database_ip;
    QString database_name;
    QString database_user;
    QString database_password;

    bool auto_time_calibration;
    // 网络通信设置
    QString local_network_ip;
    double local_network_port;
    QString long_network_ip;
    double long_network_port;

    // 视图管理设置
    int view_num;
    QString one_view;
    QString two_view;
    QString three_view;
    QString four_view;
    int time_rang;
    int dis_rang;
    double data_interval;
    // 亮温质量控制设置
    QString yuzhi1;
    QString yuzhi2;
    QString min_value;
    QString max_value;
    QString difference_value;

    QString rainfall_time;

    bool logic;//逻辑检查
    bool min_bianlv;//最小变率
    bool water;//降水量
    bool gender;//一致性判别
    bool peak;//极值判别

    // FTP文件设置
    QString ftp_ip;
    QString ftp_user;
    QString ftp_port;
    QString ftp_password;
    QString ftp_local_upload_catalogue;
    QString ftp_long_upload_catalogue;
    QString ftp_upload_interval;
    QString ftp_local_download_catalogue;
    QString ftp_long_download_catalogue;

    bool lv1_data;
    bool lv2_data;
    bool sensor_status_data;
    bool daily_maintenance_data;
    bool fault_management_data;
    bool system_maintenance_data;
    bool upload_monitor_data;
    bool log_data;
    bool calibration_data;


    double lv1_day;
    double lv2_day;
    double sensor_status_day;
    double daily_maintenance_day;
    double fault_management_day;
    double system_maintenance_day;
    double upload_monitor_day;
    double log_day;
    double calibration_day;

    // 设备关键参数设置
    QString jifen_time;
    QString black_period;
    QString noise_period;
    QString tianxian_period;
    QString other_time;


    double year_data;
    double moon_data;
    double day_data;
    double hour_data;
    double minute_data;
    double second_data;
};
class CSystemConfigData : public QObject
{
    Q_OBJECT
public:
    explicit CSystemConfigData(QObject* parent = nullptr);
    ~CSystemConfigData();
    void saveToFile();   // 保存数据到文件中
    void loadFromFile(); // 加载文件中数据
    SystemConfigData systemdata;
    QString filePath;
    void creat_configfile();

signals:

public slots:
};

#endif // CSYSTEMCONFIGDATA_H
