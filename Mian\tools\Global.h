﻿#ifndef GLOBAL_H
#define GLOBAL_H
#include <iostream>
#include "Configdata/CControlSetParameterData.h"
#include "Configdata/CSystemConfigData.h"
#include "Configdata/CFMGParameterData.h"
#include <QVector>
#include "Data_storage/sqlite_user_data.h"
#include <QColor>
#include "tools/Data_storage/sqlite_faultrecord_data.h"
#include "tools/Data_storage/sqlite_ftpuploadrecord_data.h"
#include "tools/Data_storage/sqlite_systemmaintenance_data.h"
#include <tools/Data_storage/txt_staterecord_data.h>
#include "Data_storage/txt_routinemaintenance_data.h"
#include <QTableWidget>
#include <tools/Data_storage/sensor_status_record.h>
#include <tools/data_treating/lv1_data_storage.h>
#include <tools/data_treating/lv2_data_storage.h>
#include "UI/comm/classwidgetcomm.h"
#include "UI/LogDialog.h"
#include "tools/data_treating/device_status_data_storage.h"
#include "tools/data_treating/Calibrate_data_storage.h"


//extern CControlSetParameterData controlParameterdata;
extern CSystemConfigData* systemconfigfdata;
//extern CFMGParameterData fmgparameterdata;

extern QVector<double> timedataContainer_;          // 时间数据（秒）
extern QVector<double> heightdataContainer_;        // 高度数据
extern QVector<QVector<double>> tempdataContainer_; // 温度数据（二维数组）

// 数据库相关对象
extern sqlite_USER_data *USER_data;                           // 创建用户管理表
extern sqlite_FaultRecord_data *FaultRecord_data;             // 创建故障记录表
extern sqlite_FtpUploadRecord_data *FtpUploadRecord_data;     // 创建FTP上传记录表
extern sqlite_SystemMaintenance_data *SystemMaintenance_data; // 创建系统维护日志记录表

extern txt_staterecord_data *StateRecord_data_txt;               // 日志记录
extern txt_routinemaintenance_data *RoutineMaintenance_data_txt; // 日常维护记录
extern sensor_status_record* SensorStatusRecord_data;

extern LogDialog* m_log;


extern Lv1_data_storage* lv1_data_storage;
extern Lv2_data_storage* lv2_data_storage;
extern Device_status_data_storage* device_status_data_storage;
extern Calibrate_data_storage* calibrate_data_storage;

extern QColor color_dead_error;
extern QColor color_general_information;
extern QColor color_general_error;
extern QColor color_debug_information;
extern QColor color_warn_information;

extern QString str_dead_error;
extern QString str_general_information;
extern QString str_general_error;
extern QString str_debug_information;
extern QString str_warn_information;

extern classWidgetComm *widgetComm; // 通讯连接对象

extern QString log_currentFilePath;

extern int index_seconds;//时间自动刷新
extern bool  Login_validation;

extern QString  g_ftp_name;
extern QString  g_ftp_ip;
extern QString  g_ftp_port;
extern QString  g_download_catalogue;

#define ONESEC 1000


// 参数设置中亮温设置结构体
struct BTempParmConf_Struct
{
   float brightTempFilCoef; // 亮温滤波系数
   short constTempSrcAng;   // 观测时常温源角度
   short noiseSrcAngle;     // 观测时噪声源角度
   short skyAngle;          // 观测时对天空角度
   ushort stayDuration;      // 观测停留时长
   ushort intervalDur;       // 观测间隔时长
   uchar innerCalibrMode;    // 内定标模式
};

// 定标设置中结构体
struct calibSettings_Struct
{
    float LNsrcAng;//液氮源角度
    float ATsrcAng;//常温源角度
    float noiSrcAng;//噪声源角度
    float nitroSrcTemp;//液氮亮温
    float skyTilt1;//天空倾斜角度1
    float skyTilt2;//天空倾斜角度2
    float skyTilt3;//天空倾斜角度3
    float skyTilt4;//天空倾斜角度4
    float skyTilt5;//天空倾斜角度5
    ushort stayDuration;// 观测停留时长
};
//定标参数结构体
struct calibrationValue_Struct
{
    float gainCoef_C[16];//增益系数
    float sysBtNoi_Tsys[16];//系统噪声亮温
    float Alpha_param[16];
};

// 参数设置中亮温设置结构体
extern struct BTempParmConf_Struct BTempParmConf_value;

// 定标设置中结构体
extern struct calibSettings_Struct calibSettings_value;

//定标参数结构体值
extern struct calibrationValue_Struct calibrationValue_value;

//亮温参数
extern float BrightTemp_value[];
class Global
{
public:
    Global();
    ~Global();
    static void open_qmessage(QString title, QString message, QString sure);
    static void Container_removal(QVector<ushort> * vect);
    static void set_alendarWidget_time(QLineEdit* lineedit);
    void set_FMG_tableWidget(QTableWidget *tab);

    static void saveToFile();
    static void loadFromFile();

    static void creat_configfile();
};



#endif // GLOBAL_H