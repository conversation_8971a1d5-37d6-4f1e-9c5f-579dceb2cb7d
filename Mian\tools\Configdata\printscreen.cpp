﻿#include "printscreen.h"
#include <QScreen>
#include <QFileDialog>
#include <QMouseEvent>
#include <QApplication>
#include <QDesktopWidget>
#include <QPrinter>

QList<QPixmap> pixmaps;

PrintScreen::PrintScreen(QWidget *parent)
    : QWidget{parent}
{
    /*****画图初始化****/
    InitWindow();
    /*****创建右键菜单****/
    CreateMenu();
}

PrintScreen::~PrintScreen()
{
    delete menu;
}

void PrintScreen::InitWindow(void)
{
    // 启用鼠标跟踪
    this->setMouseTracking(true);
    // 设置无边框窗口
    this->setWindowFlags(Qt::FramelessWindowHint);
    // 设置窗口为激活状态和全屏模式
    setWindowState(Qt::WindowActive | Qt::WindowFullScreen);
    // 确保关闭时自动删除
    setAttribute(Qt::WA_DeleteOnClose);

    // 获取主屏幕
    QScreen *screen = QApplication::primaryScreen();
    // 抓取整个屏幕内容
    m_loadPixmap = screen->grabWindow(QApplication::desktop()->winId());
    // 设置屏幕宽度、高度
    m_screenWidth = m_loadPixmap.width();
    m_screenHeight = m_loadPixmap.height();
}

void PrintScreen::CreateMenu(void)
{
    menu = new QMenu(this); // 创建右键菜单
    menu->addAction("复制到粘贴板", this, [=]()
                    { copytoPasteboard(true); });
    menu->addAction("保存到图片", this, [=]()
                    { savetoImage(); });
    //    menu->addAction("复制到文档", this, [=](){copytoPDF();});
    //    menu->addAction("文档打印", this, [=](){printtoPDF();});
    menu->addAction("退出截图", this, [=]()
                    { this->close(); });
    menu->setStyleSheet("background-color: rgb(30, 30, 30, 100);color: rgb(255, 255, 255);"
                        "border-right:2px solid #aaaaaa; "
                        "border-bottom:2px solid #aaaaaa;"
                        "border-left:2px solid #aaaaaa;"
                        "border-top:2px solid #aaaaaa; "
                        "border-radius:5px;"
                        "font: 10pt "
                        "Agency FB"
                        ";"
                        "selection-background-color: rgb(100, 40, 40);");
}

// 将截图复制到粘贴板
QPixmap PrintScreen::copytoPasteboard(bool hideflag)
{
    QPixmap pixmap;
    // 截屏
    this->setWindowOpacity(0); // 设置窗口透明度
    // 抓取整个屏幕内容
    QScreen *mainscreen = QApplication::primaryScreen();
    // 计算截屏区域位置及大小
    int x = std::min(m_beginPoint.x(), m_endPoint.x());
    int y = std::min(m_beginPoint.y(), m_endPoint.y());
    int width = std::abs(m_beginPoint.x() - m_endPoint.x());
    int height = std::abs(m_beginPoint.y() - m_endPoint.y());
    pixmap = mainscreen->grabWindow(QApplication::desktop()->winId(), x, y, width, height);
    this->setWindowOpacity(0.4); // 设置窗口透明度
    // 保存到粘贴板
    QClipboard *pic = QApplication::clipboard();
    pic->setPixmap(pixmap);
    if (hideflag)
        this->close();
    update();
    return pixmap;
}
// 截图另存为图片
void PrintScreen::savetoImage(void)
{
    QString savename = QStandardPaths::writableLocation(QStandardPaths::DesktopLocation) + "/截图.png"; // 默认路径：桌面 默认格式：png
    QString fileName = QFileDialog::getSaveFileName(this, "截图另存为", savename, "PNG (*.png);;BMP (*.bmp);;JPEG (*.jpg *.jpeg)");
    QPixmap pixmap = copytoPasteboard(true); // 将截图复制到粘贴板
    if (!fileName.isEmpty())
    {
        pixmap.save(fileName);
    }
    pixmap.save(fileName);
    this->close();
}

// 绘制
void PrintScreen::saveImage(void)
{
    QScreen *screen = QApplication::primaryScreen();
    QPixmap pix = screen->grabWindow(0, m_beginPoint.rx(), m_beginPoint.ry(), m_screenWidth, m_screenHeight);
    QImage image = pix.toImage().convertToFormat(QImage::Format_RGBA8888);
}

QRect PrintScreen::GetRect(const QPoint &beginPoint, const QPoint &endPoint)
{
    int x = std::min(beginPoint.x(), endPoint.x());
    int y = std::min(beginPoint.y(), endPoint.y());
    int width = std::abs(beginPoint.x() - endPoint.x());
    int height = std::abs(beginPoint.y() - endPoint.y());

    if (width == 0)
        width = 1; // 确保宽度至少为1像素
    if (height == 0)
        height = 1; // 确保高度至少为1像素

    return QRect(x, y, width, height);
}

void PrintScreen::mousePressEvent(QMouseEvent *event)
{
    // 按下右键 显示菜单
    if (event->button() == Qt::RightButton)
        menu->exec(cursor().pos()); // 菜单显示的位置跟随鼠标
    // 按下左键
    else if (event->button() == Qt::LeftButton)
    {
        if (m_captureComplete && QRect(m_beginPoint, m_endPoint).contains(event->pos()))
        {
            m_isDragging = true;                          // 开始拖动
            m_dragPosition = event->pos() - m_beginPoint; // 计算开始拖动位置
        }
        else
        {
            m_isMousePress = true; // 鼠标被按下
            m_isDragging = false;
            m_beginPoint = event->pos(); // 记录开始点  获取点击的坐标
            // qInfo()<< m_beginPoint;
        }
    }
}

void PrintScreen::mouseMoveEvent(QMouseEvent *event)
{
    // 获取屏幕尺寸
    QRect screenRect = QGuiApplication::primaryScreen()->geometry();

    // 鼠标按下且截图未完成
    if (m_isMousePress && !m_captureComplete)
    {
        // 确保终点坐标不超过屏幕范围
        int x = qBound(screenRect.left(), event->pos().x(), screenRect.right());
        int y = qBound(screenRect.top(), event->pos().y(), screenRect.bottom());
        m_endPoint = QPoint(x, y);
    }
    // 正在拖动
    else if (m_isDragging)
    {
        QPoint newTopLeft = event->pos() - m_dragPosition;
        // 确保新的顶点坐标不超过屏幕范围
        int x = qBound(screenRect.left(), newTopLeft.x(), screenRect.right() - m_dragPosition.x());
        int y = qBound(screenRect.top(), newTopLeft.y(), screenRect.bottom() - m_dragPosition.y());
        newTopLeft = QPoint(x, y);

        QPoint offset = newTopLeft - m_beginPoint;
        m_beginPoint += offset;
        m_endPoint += offset;
    }

    update();
    return QWidget::mouseMoveEvent(event);
}

void PrintScreen::mouseReleaseEvent(QMouseEvent *event)
{
    // 鼠标释放且截图未完成
    if (m_isMousePress && !m_captureComplete)
    {
        m_endPoint = event->pos(); // 设置结束点
        m_isMousePress = false;    // 重置鼠标按下状态
        m_captureComplete = true;  // 标记截图完成
        update();
    }
    // 释放时正在拖动
    else if (m_isDragging)
    {
        m_isDragging = false;
    }
    update();
}
// 键盘按下事件
void PrintScreen::keyPressEvent(QKeyEvent *event)
{
    if (event->key() == Qt::Key_Enter || event->key() == Qt::Key_Return) // 按下回车键
    {
        // 保存图片
        QString filePath = QFileDialog::getSaveFileName(nullptr, "保存图片", QString(), "Images (*.png *.jpg)");
        if (!filePath.isEmpty())
        {
            m_capturePixmap.save(filePath); // 保存截图到文件
        }
        // this->close(); //没必要退出
    }
    else if (event->key() == Qt::Key_Escape) // 按下Esc键
        this->close();
    else
        QWidget::keyPressEvent(event);
}

void PrintScreen::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)

    m_painter.begin(this);                                           // 开始绘制
    QColor shadowColor(0, 0, 0, 100);                                // 半透明遮罩颜色
    m_painter.setPen(QPen(Qt::blue, 1, Qt::SolidLine, Qt::FlatCap)); // 设置画笔
    m_painter.drawPixmap(0, 0, m_loadPixmap);                        // 绘制加载的屏幕截图
    m_painter.fillRect(m_loadPixmap.rect(), shadowColor);            // 绘制半透明遮罩

    QRect selectedRect = GetRect(m_beginPoint, m_endPoint);        // 获取选择区域
    m_capturePixmap = m_loadPixmap.copy(selectedRect);             // 截取选择区域的屏幕截图
    m_painter.drawPixmap(selectedRect.topLeft(), m_capturePixmap); // 绘制截取的区域
    m_painter.drawRect(selectedRect);                              // 绘制选择区域的边框
    m_painter.end();                                               // 结束绘制
}
