{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "EigenLapack", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "eigen_lapack_static::@6890427a1f51a3e7e1df", "jsonFile": "target-eigen_lapack_static-Debug-288caa8cb4e9b50b2c55.json", "name": "eigen_lapack_static", "projectIndex": 0}, {"directoryIndex": 0, "id": "lapack::@6890427a1f51a3e7e1df", "jsonFile": "target-lapack-Debug-2463c0f8370256caaf59.json", "name": "lapack", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/lapack/out/build/x64-Debug", "source": "E:/VS2019/FSJ_V1.0/Mian/include/eigen-3.4.0/lapack"}, "version": {"major": 2, "minor": 2}}