﻿#include "FTPDialog.h"
#include "ui_FTPDialog.h"
#include <QMessageBox>
#include <QVector>
#include "tools/Global.h"
#include <QtConcurrent/QtConcurrent>
/**
 * @brief FTPDialog 构造函数
 *
 * 初始化FTPDialog窗口，设置窗口标题为"FTP配置"，创建并配置表格视图，加载FTP配置数据，并连接信号与槽。
 *
 * @param parent 父窗口指针
 */
FTPDialog::FTPDialog( LogDialog * log, QWidget* parent) : QDialog(parent),
                                        ui(new Ui::FTPDialog),
                                        log(log)
{
    ui->setupUi(this);
    this->setWindowTitle("FTP配置");

    ftpdata = new FTPDataDialog();
    //ftpClient = new FTPClientInterface();
    timer = new QTimer();

    // 创建模型和视图
    QVector<QString> data;
    data.append("状态");
    data.append("站点名称");
    data.append("IP");
    data.append("端口");
    data.append("远程目录");

    model_ftp = new LargeTableModel(data);

    ui->tableView->setModel(model_ftp);

    // 性能优化设置
    ui->tableView->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    ui->tableView->verticalHeader()->setDefaultSectionSize(25);              // 固定行高
    ui->tableView->setVerticalScrollMode(QAbstractItemView::ScrollPerPixel); // 像素级滚动
    ui->tableView->setEditTriggers(QAbstractItemView::NoEditTriggers);       // 禁用编辑

    ui->tableView->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableView->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->tableView->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    connect(ui->tableView->horizontalHeader(), &QHeaderView::geometriesChanged, [&]()
            {
        if (ui->tableView == nullptr)
            return;
        int totalwidth = ui->tableView->viewport()->width();
        ui->tableView->setColumnWidth(0, (totalwidth / 9));
        ui->tableView->setColumnWidth(1, (totalwidth / 9));
        ui->tableView->setColumnWidth(2, (totalwidth / 9));
        ui->tableView->setColumnWidth(3, (totalwidth / 9));
        ui->tableView->setColumnWidth(4, (totalwidth / 9)); });

    configList = new FTPConfig;
    if (!configList->loadFromFile())
    {
        add_log(Log_Sort_Info_Erro+"加载配置文件失败");
    }
    //directories.append("E:/辐射计资料/辐射计/样例数据/LV1/");
    //directories.append("E:/辐射计资料/辐射计/样例数据/LV2/");
    //directories.append("E:/辐射计资料/辐射计/样例数据/数据Lv2/");

    QVector<QString> buff;
    QVector<FTPSiteConfig> sitelist = configList->getAllSites();
    for (const auto &site : sitelist)
    {
        buff.clear();
        buff.append((site.isEnabled() ? "启用" : "禁用"));
        buff.append(site.siteName);
        buff.append(site.host);
        buff.append(QString::number(site.port));
        buff.append(site.remoteDir);
        addSingleRow(buff);
    }
    connect(ui->pushButton_add, &QPushButton::clicked, this, &FTPDialog::slot_pushButton_add_clicked);
    connect(ui->pushButton_delect, &QPushButton::clicked, this, &FTPDialog::slot_pushButton_delect_clicked);
    connect(ui->pushButton_editor, &QPushButton::clicked, this, &FTPDialog::slot_pushButton_edit_clicked);
    connect(ui->pushButton_start, &QPushButton::clicked, this, &FTPDialog::slot_pushButton_enable_clicked);
    connect(ui->pushButton_stop, &QPushButton::clicked, this, &FTPDialog::slot_pushButton_disable_clicked);
    connect(ui->pushButton_inspect, &QPushButton::clicked, this, &FTPDialog::slot_pushButton_check_clicked);
    connect(timer, &QTimer::timeout, this, &FTPDialog::slot_timer_timeout);

    timer->start(FTP_TIMEOUT_INTERVAL);
}

FTPDialog::~FTPDialog()
{
    delete ui;
}

/**
 * @brief 当“添加”按钮被点击时调用的槽函数
 *
 * 当用户点击“添加”按钮时，此函数将被调用。
 * 它首先将 FTPDataDialog 类中的 signal_data_send_show 信号与 FTPDialog 类中的 slot_data_send_show 槽函数连接，
 * 以便在数据发送时显示相应的界面。
 * 然后，它会断开 FTPDataDialog 类中的 signal_data_send_show 信号与 FTPDialog 类中的 slot_data_modify_show 槽函数的连接，
 * 以防止在数据发送时显示错误的界面。
 * 接着，它将 FTPDataDialog 对象设置为添加状态，并执行 FTPDataDialog 以显示相应的对话框。
 */
void FTPDialog::slot_pushButton_add_clicked()
{
    connect(ftpdata, &FTPDataDialog::signal_data_send_show, this, &FTPDialog::slot_data_send_show);
    disconnect(ftpdata, &FTPDataDialog::signal_data_send_show, this, &FTPDialog::slot_data_modify_show);
    ftpdata->set_add_state();
    ftpdata->exec();
}

/**
 * @brief slot_data_modify_show 更新FTP站点配置
 *
 * 该函数用于更新FTP站点配置，并刷新显示。
 *
 * @param siteConfig FTP站点配置信息
 */
void FTPDialog::slot_data_modify_show(FTPSiteConfig siteConfig)
{
    int rows = model_ftp->rowCount();
    configList->updateSite(siteConfig.siteName, siteConfig);
    if (rows>0)
    {
        for (int i = 0; i < rows; i++)
        {
            QVector<QString> buff = model_ftp->getRow(i);

            if (buff[1] == siteConfig.siteName)
            {
                buff.clear();
                buff.append((siteConfig.isEnabled() ? "启用" : "禁用"));
                buff.append(siteConfig.siteName);
                buff.append(siteConfig.host);
                buff.append(QString::number(siteConfig.port));
                buff.append(siteConfig.remoteDir);
                model_ftp->replaceRow(i, buff);
            }
        }
    }

    if (configList->saveToFile())
    {
        add_log(Log_Sort_Info + "站点保存成功");
    }
    else
    {
        add_log(Log_Sort_Info_Erro + "站点保存失败");
    }

}

/**
 * @brief slot_pushButton_delect_clicked 删除FTP站点按钮点击槽函数
 *
 * 当点击删除FTP站点按钮时，调用此槽函数进行站点删除操作。
 *
 * 首先，获取当前选中的行，如果模型未初始化，则显示错误消息并返回。
 * 如果未选中任何行，则显示提示消息并返回。
 * 获取选中行的数据，从配置列表中移除站点，并尝试将配置保存到文件。
 * 如果保存成功，则输出成功消息；否则，输出失败消息。
 * 从模型中移除选中的行，并更新表格视图。
 */
void FTPDialog::slot_pushButton_delect_clicked()
{
    QItemSelectionModel *selectionModel = ui->tableView->selectionModel();
    if (!model_ftp)
    {
        QMessageBox::critical(this, "错误", "模型未初始化");
        return;
    }

    if (!selectionModel->hasSelection())
    {
        QMessageBox::information(this, "提示", "请选择删除的行");
        return;
    }

    int row = model_ftp->getSelectedRow(selectionModel->selection());
    QVector<QString> buff = model_ftp->getRow(row);

    configList->removeSite(buff[1]);
    if (configList->saveToFile())
    {
        add_log(Log_Sort_Info+"站点移除成功");
    }
    else
    {
        add_log(Log_Sort_Error+"站点移除失败");
    }
    model_ftp->removeSelectedRows(selectionModel->selection());
    ui->tableView->viewport()->update();
}

/**
 * @brief FTPDialog类的slot_pushButton_edit_clicked成员函数
 *
 * 当点击编辑按钮时，此函数将被触发。
 *
 * 首先，获取tableView的选择模型。
 * 如果没有选择任何行，则显示提示信息“请选择修改的行”并返回。
 *
 * 然后，断开ftpdata的signal_data_send_show信号与FTPDialog的slot_data_send_show槽函数的连接，
 * 并连接ftpdata的signal_data_send_show信号与FTPDialog的slot_data_modify_show槽函数。
 *
 * 接着，获取所选行的索引，并从模型中获取该行的数据。
 *
 * 如果configList中存在所选站点（通过站点名称匹配），则获取该站点的信息，
 * 设置ftpdata为修改状态，加载该站点的数据，并显示ftpdata对话框。
 */
void FTPDialog::slot_pushButton_edit_clicked()
{
    QItemSelectionModel* selectionModel = ui->tableView->selectionModel();

    if (!selectionModel->hasSelection())
    {
        QMessageBox::information(this, "提示", "请选择修改的行");
        return;
    }
    disconnect(ftpdata, &FTPDataDialog::signal_data_send_show, this, &FTPDialog::slot_data_send_show);
    connect(ftpdata, &FTPDataDialog::signal_data_send_show, this, &FTPDialog::slot_data_modify_show);

    int row = model_ftp->getSelectedRow(selectionModel->selection());
    QVector<QString> buff =  model_ftp->getRow(row);

    if (configList->findSiteIndex(buff[1])>=0)
    {
        auto site = configList->getSite(buff[1]);
        ftpdata->set_modify_state();
        ftpdata->load_data_fun(*site);
        ftpdata->exec();
    }
}

/**
 * @brief FTPDialog::slot_pushButton_enable_clicked 启用FTP站点
 *
 * 当用户点击启用按钮时，此槽函数会被调用。
 * 函数会检查当前选中的行，如果没有选中任何行，则弹出提示框提示用户选择启用的行。
 * 如果已选中行，函数会获取选中的行号，并从FTP模型中获取该行的数据。
 * 然后，根据选中的站点名称从配置列表中获取对应的FTP站点配置，将其启用状态设置为true，
 * 并更新配置列表和保存到文件中。
 * 最后，将选中的行的状态更新为“启用”。
 */
void FTPDialog::slot_pushButton_enable_clicked()
{
    QItemSelectionModel* selectionModel = ui->tableView->selectionModel();

    if (!selectionModel->hasSelection())
    {
        QMessageBox::information(this, "提示", "请选择启用的行");
        return;
    }
    int row = model_ftp->getSelectedRow(selectionModel->selection());
    QVector<QString> buff = model_ftp->getRow(row);
    auto site = configList->getSite(buff[1]);
    FTPSiteConfig tempsite = *site;
    tempsite.setEnabled(true);
    configList->updateSite(buff[1], tempsite);
    configList->saveToFile();
    buff[0] = "启用";
    add_log(Log_Sort_Info + "启用站点" + buff[1]);
    model_ftp->replaceRow(row, buff);
}

/**
 * @brief 禁用按钮点击槽函数
 *
 * 当点击禁用按钮时，该函数会被调用。该函数首先检查表格中是否有选中的行，
 * 如果没有选中任何行，则弹出提示信息框，提示用户选择禁用的行。
 * 如果有选中的行，则获取选中的行号，并从FTP模型中获取对应行的数据。
 * 然后，根据选中的行号从配置列表中获取对应的FTP站点配置，将其禁用状态设置为false，
 * 并更新配置列表和保存配置文件。最后，将表格中对应行的状态更新为"停用"。
 */
void FTPDialog::slot_pushButton_disable_clicked()
{
    QItemSelectionModel* selectionModel = ui->tableView->selectionModel();

    if (!selectionModel->hasSelection())
    {
        QMessageBox::information(this, "提示", "请选择禁用的行");
        return;
    }
    int row = model_ftp->getSelectedRow(selectionModel->selection());
    QVector<QString> buff = model_ftp->getRow(row);
    auto site = configList->getSite(buff[1]);
    FTPSiteConfig tempsite = *site;
    tempsite.setEnabled(false);
    configList->updateSite(buff[1], tempsite);
    configList->saveToFile();
    buff[0] = "停用";
    add_log(Log_Sort_Info + "停用站点" + buff[1]);
    model_ftp->replaceRow(row, buff);
}

/**
 * @brief slot_pushButton_check_clicked 槽函数，用于处理FTP连接检测按钮的点击事件
 *
 * 当用户点击“检测”按钮时，该函数会被调用。函数会检查用户是否选择了要检测的FTP站点行，
 * 然后尝试连接到所选的FTP站点，并显示连接状态。
 */
void FTPDialog::slot_pushButton_check_clicked()
{
    QItemSelectionModel* selectionModel = ui->tableView->selectionModel();

    if (!selectionModel->hasSelection())
    {
        QMessageBox::information(this, "提示", "请选择检测的行");
        return;
    }
    int row = model_ftp->getSelectedRow(selectionModel->selection());
    QVector<QString> buff = model_ftp->getRow(row);
    auto site = configList->getSite(buff[1]);
    FTPClientInterface tempftpClient;
    tempftpClient.initConfig(*site);
    if (tempftpClient.testConnection())
    {
        QMessageBox::information(nullptr, "FTP连接状态", "连接成功");
    }
    else
    {
        QMessageBox::information(nullptr, "FTP连接状态", "连接失败");
    }
}

/**
 * @brief slot_timer_timeout
 *
 * 处理FTP对话框中的定时器超时槽函数。
 *
 * 当定时器超时时，该函数将被调用。函数的主要功能是停止所有当前正在上传的FTP客户端，
 * 清除当前FTP客户端列表，然后根据配置列表中的站点信息重新创建FTP客户端，并初始化每个客户端的配置和本地目录。
 * 如果站点被启用，则启动上传；否则，停止上传。
 */
void FTPDialog::slot_timer_timeout()
{
    for (int i = 0; i < ftpClient.size(); i++)
    {
        ftpClient[i]->stopUpload();
        disconnect(ftpClient[i].data(), &FTPClientInterface::uploadLog, this, &FTPDialog::slot_log_record);
    }
    ftpClient.clear();
    for (int i = 0; i < configList->getSiteCount(); i++)
    {
        auto client = QSharedPointer<FTPClientInterface>::create();
        ftpClient.append(client);
        connect(ftpClient[i].data(), &FTPClientInterface::uploadLog, this, &FTPDialog::slot_log_record);
        ftpClient[i]->initConfig(*configList->getSite(i));
        ftpClient[i]->setLocalDirectories(directories);
        if (configList->getSite(i)->isEnabled())
        {
            ftpClient[i]->startUpload();
        }
        else
        {
            ftpClient[i]->stopUpload();
            disconnect(ftpClient[i].data(), &FTPClientInterface::uploadLog, this, &FTPDialog::slot_log_record);
        }
    }
}

void FTPDialog::slot_log_record(const QString& log)
{
    add_log(Log_Sort_Info+log);
}

void FTPDialog::add_log(QString info)
{

    log->add_Operational_information(info);
}

/**
 * @brief 添加单行数据到FTP对话框的数据模型中
 *
 * 将给定的数据行添加到FTP对话框的数据模型中，并根据需要清除旧数据。
 *
 * @param data 要添加的数据行，类型为QVector<QString>，包含多个QString元素。
 */
void FTPDialog::addSingleRow(QVector<QString> data)
{
    int rowCount = model_ftp->rowCount();

    model_ftp->addRow(data);
    if (model_ftp->rowCount() > 86400)
    {
        model_ftp->clear1rowData();
        rowCount = model_ftp->rowCount();
        rowCount--;
    }
    rowCount++;
}



/**
 * @brief slot_file_upload 上传文件槽函数
 *
 * 该函数用于上传指定的文件到FTP服务器。
 *
 * @param path 要上传的文件路径
 */
void FTPDialog::slot_file_upload(QString path)
{
    for (int i = 0; i < ftpClient.size(); i++)
    {
        ftpClient[i]->stopUpload();
    }
    ftpClient.clear();
    for (int i = 0; i < configList->getSiteCount(); i++)
    {
        auto client = QSharedPointer<FTPClientInterface>::create();
        ftpClient.append(client);
        ftpClient[i]->initConfig(*configList->getSite(i));
        ftpClient[i]->uploadSpecifiedFile(path);
    }
}


/**
 * @brief slot_data_send_show 将FTP站点配置添加到列表中并展示
 *
 * 将传入的FTP站点配置添加到配置列表中，并展示在界面中。
 *
 * @param siteConfig FTP站点配置信息
 */
void FTPDialog::slot_data_send_show(FTPSiteConfig siteConfig)
{
    QVector<QString> data;
    siteConfig.enabled = false;
    if (configList->addSite(siteConfig))
    {

        data.append(FTP_DISABLE);
        data.append(siteConfig.siteName);
        data.append(siteConfig.host);
        data.append(QString::number(siteConfig.port));
        data.append(siteConfig.remoteDir);

        addSingleRow(data);

        add_log(Log_Sort_Info + "站点添加成功");
        if (configList->saveToFile())
        {
            add_log(Log_Sort_Info + "站点保存成功");
        }
        else
        {
            add_log(Log_Sort_Info_Erro + "站点保存失败");
        }
    }
    else
    {
        add_log(Log_Sort_Error + "站点添加失败");
    }
}


