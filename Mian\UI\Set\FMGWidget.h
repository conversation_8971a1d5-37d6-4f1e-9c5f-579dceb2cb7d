﻿#ifndef FMGWIDGET_H
#define FMGWIDGET_H

#include <QWidget>
#include "qcustomplot.h"
#include "UI/comm/classwidgetcomm.h"
#include <QTimer>
#include <QThread>
#include "tools/header/DockManager.h"
#include "tools/header/DockAreaWidget.h"
#include "tools/header/DockWidget.h"
#include "tools/header/DockSplitter.h"
#include "tools/header/DockAreaWidget.h"
#include "tools/header/DockAreaTitleBar.h"
#include "tools/header/DockAreaTabBar.h"
#include "tools/header/FloatingDockContainer.h"
#include "tools/header/DockComponentsFactory.h"
#include "tools/Data_storage/largetablemodel.h"
#include "UI/flowlayout.h"
#include "tools/QCustomButton.h"


namespace Ui
{
    class FMGWidget;
}
using namespace ads;
class FMGWidget : public QWidget
{
    Q_OBJECT

public:
    explicit FMGWidget(QWidget* parent = nullptr);
    ~FMGWidget();

    classWidgetComm* FMG_network;
    void init_graph();
    void set_graph_background(QCustomPlot* plot);
    void setupPlot(QCustomPlot* plot, QColor);
    void set_Graph_index(QCustomPlot* plot, int i);
    void set_date_change(QSharedPointer<QCPAxisTickerTime> timeTicker);


    void stop_plot();
    void set_LV1_data_update(bool value);

    double second_data = 60;

    CxcDataExchange* data_k;
    CxcDataExchange* data_weather_six;
    CxcDataExchange* original_data;
    CxcDataExchange* light_data;

    QTimer* time_show_status;

    void addSingleRow(LargeTableModel* model, QVector<QString> data);
    void set_tracer(QCustomPlot* plot, QCPItemTracer* tracer, QCPItemText* tracerLabel);//游标显示


    void set_pushButton_style(QPushButton *btn, bool style);


public slots:
    // 定时器槽函数
    void set_status_show_time_weather_six(); // 温度

    void set_k_status_show_time_status();      // 原始数据监测
    void set_v_status_show_time_status();      // 原始数据监测
    void set_k_temp_status_show_time_status(); // k温度
    void set_v_temp_status_show_time_status(); // v温度

    void set_k_show_time_light(); // k亮温数据监测
    void set_v_show_time_light(); // v亮温数据监测
    void set_temp_show_time_light();
    void set_rain_show_time_light();

    void set_status_show_time_status(); // 设备状态 定时器

    void set_calibration_data();

    void on_comboBox_k_real_data(int);
    void on_comboBox_v_real_data(int);

protected:
    void closeEvent(QCloseEvent* event) override;

    bool eventFilter(QObject* watched, QEvent* event) override;

private slots:
    void handleButtonClick();
private:
    Ui::FMGWidget* ui;

    QVector<double> xData;
    QVector<double> yData;
    QVector<double> yData1;
    //k通道电压 温度
    QCPItemTracer* tracer_k_voltage;
    QCPItemText* tracerLabe_k_voltage;
    QCPItemTracer* tracer_k_temp;
    QCPItemText* tracerLabe_k_temp;
    //v通道电压 温度
    QCPItemTracer* tracer_v_voltage;
    QCPItemText* tracerLabe_v_voltage;
    QCPItemTracer* tracer_v_temp;
    QCPItemText* tracerLabe_v_temp;

    // 气象六要素
    QCPItemTracer* tracer_humidity;
    QCPItemText* tracerLabe_humidity;
    QCPItemTracer* tracer_pressure;
    QCPItemText* tracerLabe_pressure;
    QCPItemTracer* tracer_rain;
    QCPItemText* tracerLabe_rain;
    QCPItemTracer* tracer_temp;
    QCPItemText* tracerLabe_temp;
    QCPItemTracer* tracer_wind_direction;
    QCPItemText* tracerLabe_wind_direction;
    QCPItemTracer* tracer_wind_speed;
    QCPItemText* tracerLabe_wind_speed;

    //k通道亮温
    QCPItemTracer* tracer_k_light_temp;
    QCPItemText* tracerLabe_k_light_temp;

    //v通道亮温
    QCPItemTracer* tracer_v_light_temp;
    QCPItemText* tracerLabe_v_light_temp;

    // 降雨量
    QCPItemTracer* tracer_light_rain;
    QCPItemText* tracerLabe_light_rain;

    // 常温源
    QCPItemTracer* tracer_light_temp;
    QCPItemText* tracerLabe_light_temp;

    LargeTableModel* model_k;

    LargeTableModel* model_v;


    LargeTableModel* model_ca1;

    LargeTableModel* model_ca2;

    bool open_observe = false;

    double totalSeconds;            // 系统实时时间

    int lastDay = -1;
    QDateTime baseDateTime;

    double count;
    bool time_status = false;

    int row_num;


    LargeTableModel* model;

    LargeTableModel* model1;

    int plot_data_K_v = 0;
    int plot_data_K_t = 0;
    int plot_data_V_v = 0;
    int plot_data_V_t = 0;

    int plot_data_K_l = 0;
    int plot_data_V_l = 0;
    int plot_data_rainfall = 0;
    int plot_data_T = 0;

    bool time_K_v_flag = true;
    bool time_V_v_flag = true;
    bool time_K_t_flag = true;
    bool time_V_t_flag = true;
    bool time_K_l_flag = true;
    bool time_V_l_flag = true;
    bool time_TTemp_flag = true;
    bool time_Rain_flag = true;

    bool LV1_data_update_flag = false;


    QButtonGroup* buttonGroup;

    CDockManager* DockManager;
    CDockWidget* CentralDockWidget;
    CDockWidget* leftPlace;
    CDockWidget* leftPlace_button;
    CDockWidget* leftPlace_sensor;

    CDockWidget* rightUpPlaceholder;
    CDockWidget* rightDownPlaceholder;


    bool sensor_status_widget = false;
    bool k_data_widget = false;
    bool v_data_widget = false;


    int second_time;

    QSharedPointer<QCPAxisTickerTime> k_voltage_timeTicker;
    QSharedPointer<QCPAxisTickerTime> k_temp_timeTicker;
    QSharedPointer<QCPAxisTickerTime> v_voltage_timeTicker;
    QSharedPointer<QCPAxisTickerTime> v_temp_timeTicker;

    QSharedPointer<QCPAxisTickerTime> k_light_timeTicker;
    QSharedPointer<QCPAxisTickerTime> v_light_timeTicker;
    QSharedPointer<QCPAxisTickerTime> light_rain_timeTicker;
    QSharedPointer<QCPAxisTickerTime> light_temp_timeTicker;

    QSharedPointer<QCPAxisTickerTime> temp_timeTicker;
    QSharedPointer<QCPAxisTickerTime> rain_timeTicker;
    QSharedPointer<QCPAxisTickerTime> wind_direction_timeTicker;
    QSharedPointer<QCPAxisTickerTime> wind_speed_timeTicker;
    QSharedPointer<QCPAxisTickerTime> humidity_timeTicker;
    QSharedPointer<QCPAxisTickerTime> pressure_timeTicker;


    QCustomButton* btn_swtich;
};

#endif // FMGWIDGET_H
