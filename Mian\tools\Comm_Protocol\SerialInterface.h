﻿#ifndef SERIALINTERFACE_H
#define SERIALINTERFACE_H

#include <QObject>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QString>

/**
 * @brief Qt串口通信接口类
 * 提供串口打开、关闭、读写等基本操作
 */
class SerialInterface : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit SerialInterface(QObject *parent = nullptr);

    /**
     * @brief 析构函数，自动关闭串口
     */
    ~SerialInterface();

    /**
     * @brief 打开串口
     * @param portName 串口名称(如COM3)
     * @param baudRate 波特率
     * @param dataBits 数据位(默认8)
     * @param parity 校验位(默认无校验)
     * @param stopBits 停止位(默认1)
     * @param flowControl 流控(默认无流控)
     * @return 成功返回true，失败返回false
     */
    bool openPort(const QString &portName,
                  QSerialPort::BaudRate baudRate,
                  QSerialPort::DataBits dataBits = QSerialPort::Data8,
                  QSerialPort::Parity parity = QSerialPort::NoParity,
                  QSerialPort::StopBits stopBits = QSerialPort::OneStop,
                  QSerialPort::FlowControl flowControl = QSerialPort::NoFlowControl);

    /**
     * @brief 关闭串口
     */
    void closePort();

    /**
     * @brief 检查串口是否打开
     * @return 打开返回true，否则false
     */
    bool isOpen() const;

    /**
     * @brief 写入数据到串口
     * @param data 要写入的数据
     * @return 成功写入的字节数，-1表示失败
     */
    qint64 writeData(const QByteArray &data);

    /**
     * @brief 读取串口数据
     * @param timeout 超时时间(毫秒)，-1表示无限等待
     * @return 读取到的数据
     */
    QByteArray readData(int timeout = -1);

    /**
     * @brief 获取可用串口列表
     * @return 可用串口名称列表
     */
    static QStringList availablePorts();

signals:
    /**
     * @brief 数据接收信号
     * @param data 接收到的数据
     */
    void dataReceived(const QByteArray &data);

    /**
     * @brief 错误发生信号
     * @param error 错误信息
     */
    void errorOccurred(const QString &error);

private slots:
    void handleReadyRead();
    void handleError(QSerialPort::SerialPortError error);

private:
    QSerialPort *m_serialPort; // Qt串口对象
};

#endif // SERIALINTERFACE_H