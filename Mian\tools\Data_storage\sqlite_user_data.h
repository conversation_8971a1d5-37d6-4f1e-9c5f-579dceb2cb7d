﻿#ifndef SQLITE_USER_DATA_H
#define SQLITE_USER_DATA_H

#include "tools/Data_storage/qtsqlitedb.h"
#include "tools/Data_storage/CustomSqlTableModel.h"
#include <QTableView>
#include <QDateTime>
struct USER_SQ_GROUP
{

    QString name;     // 用户名
    QString password; // 密码
    int permissions;  // 权限1， 普通用户：使用软件的基本功能； 2， 高级用户：有权限使用软件显示的所有功能 3， 超级用户：可对软件底层参数进行修改
    QString describe; // 描述
    QString time;     // 时间
};
class sqlite_USER_data : public QtSqliteDB
{
    Q_OBJECT
public:
    explicit sqlite_USER_data(QString path);

    ~sqlite_USER_data();
    QTableView *tableview; // 存储使用模型的view
    int cur_row;           // 记录最后一行
    // 使用QStandardItemModel模型
    QVector<int> ratios = {1, 2, 1}; // 设置模型各列显示比例
    CustomSqlTableModel *myCustomSqlTableModel;
    void use_CustomSqlTableModel(QTableView *view); // 数据库模型初始化
    bool myCustomSqlTableModel_status = false;

    QVector<struct USER_SQ_GROUP> data; // 存储查询数据库的结果

    bool select_sqlitedata(QString insertSQL) override; // 查询用户数据
    bool select_sqlitedata_user(QString username);//根据用户名查询信息

    int new_user(struct USER_SQ_GROUP data); // 新建用户

    QVector<struct USER_SQ_GROUP> select_user_group(); // 查询所有用户信息 不显示权限及密码

    int delete_user(struct USER_SQ_GROUP data, QString name); // 超级用户删除高级用户和普通用户

    int update_user(struct USER_SQ_GROUP data); // 更新用户数据  注：用户名不可修改

    int log_in(struct USER_SQ_GROUP data); // 登录。用户需输入用户名及密码

    void set_column_edit(int column, bool en); // 设置指定列编辑状态

    bool set_column_color(QVector<QColor> c);                                            // 设置最后一行的颜色
    bool set_column_color(int row, int column, const QColor &c = QColor(255, 255, 255)); // 设置指定项的颜色
    QString xorEncrypt(QString data);
private:
    
    QMetaObject::Connection coon;
};

#endif // SQLITE_USER_DATA_H
