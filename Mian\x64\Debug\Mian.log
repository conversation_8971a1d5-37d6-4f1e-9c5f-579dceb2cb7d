﻿   Qt/MSBuild: 3.4.0.1
   Qt: 5.14.2
  txt_staterecord_data.cpp
  plot.cpp
  HistoryDialog.cpp
  LogDialog.cpp
  SensorStatusDialog.cpp
  HandCalibrationDialog.cpp
  SystemConfigurationDialog.cpp
  BreakDownDialog.cpp
  DailyMaintenanceDialog.cpp
  SensorStatusRecodeDialog.cpp
  SystemMaintenanceDialog.cpp
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1b8 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1b9 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1bc 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1bd 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1c1 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1c2 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1c3 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1c4 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1ca 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1cc 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1cd 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1cf 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1d0 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1d1 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1d3 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1d6 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1d7 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1da 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1db 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1de 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1df 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1fe 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x1ff 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x204 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x205 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x208 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x209 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x20b 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x20d 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x210 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x213 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x216 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x218 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x219 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x21b 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x21d 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x21e 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x21f 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x223 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\HandCalibrationDialog.h(1,1): warning C4828: 文件包含在偏移 0x225 处开始的字符，该字符在当前源字符集中无效(代码页 65001)。 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\tools\XC_DataHanding\xcPeripheralDataBoard.h(63,1): warning C4305: “=”: 从“double”到“float”截断 (编译源文件 UI\System\DailyMaintenanceDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\tools\XC_DataHanding\xcPeripheralDataBoard.h(63,1): warning C4305: “=”: 从“double”到“float”截断 (编译源文件 tools\Data_storage\txt_staterecord_data.cpp)
E:\VS2019\FSJ_V1.0\Mian\tools\XC_DataHanding\xcPeripheralDataBoard.h(63,1): warning C4305: “=”: 从“double”到“float”截断 (编译源文件 UI\HistoryDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\tools\Data_storage\txt_staterecord_data.cpp(130,42): error C2228: “.systemdata”的左边必须有类/结构/联合
E:\VS2019\FSJ_V1.0\Mian\tools\Data_storage\txt_staterecord_data.cpp(130,42): message : 类型是“CSystemConfigData *”
E:\VS2019\FSJ_V1.0\Mian\tools\Data_storage\txt_staterecord_data.cpp(130,42): message : 是否改用“->”?
E:\VS2019\FSJ_V1.0\Mian\tools\XC_DataHanding\xcPeripheralDataBoard.h(63,1): warning C4305: “=”: 从“double”到“float”截断 (编译源文件 UI\SensorStatusDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\tools\XC_DataHanding\xcPeripheralDataBoard.h(63,1): warning C4305: “=”: 从“double”到“float”截断 (编译源文件 UI\System\SystemMaintenanceDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\System\DailyMaintenanceDialog.cpp(75,85): error C2228: “.systemdata”的左边必须有类/结构/联合
E:\VS2019\FSJ_V1.0\Mian\UI\System\DailyMaintenanceDialog.cpp(75,85): message : 类型是“CSystemConfigData *”
E:\VS2019\FSJ_V1.0\Mian\UI\System\DailyMaintenanceDialog.cpp(75,85): message : 是否改用“->”?
E:\VS2019\FSJ_V1.0\Mian\tools\XC_DataHanding\xcPeripheralDataBoard.h(63,1): warning C4305: “=”: 从“double”到“float”截断 (编译源文件 UI\System\SensorStatusRecodeDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\tools\XC_DataHanding\xcPeripheralDataBoard.h(63,1): warning C4305: “=”: 从“double”到“float”截断 (编译源文件 UI\LogDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\tools\XC_DataHanding\xcPeripheralDataBoard.h(63,1): warning C4305: “=”: 从“double”到“float”截断 (编译源文件 UI\SystemConfiguration\SystemConfigurationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\tools\XC_DataHanding\xcPeripheralDataBoard.h(63,1): warning C4305: “=”: 从“double”到“float”截断 (编译源文件 UI\SystemConfiguration\HandCalibrationDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\LogDialog.cpp(164,85): error C2228: “.systemdata”的左边必须有类/结构/联合
E:\VS2019\FSJ_V1.0\Mian\UI\LogDialog.cpp(164,85): message : 类型是“CSystemConfigData *”
E:\VS2019\FSJ_V1.0\Mian\UI\LogDialog.cpp(164,85): message : 是否改用“->”?
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\SystemConfigurationDialog.cpp(317,59): error C2228: “.systemdata”的左边必须有类/结构/联合
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\SystemConfigurationDialog.cpp(317,59): message : 类型是“CSystemConfigData *”
E:\VS2019\FSJ_V1.0\Mian\UI\SystemConfiguration\SystemConfigurationDialog.cpp(317,59): message : 是否改用“->”?
E:\VS2019\FSJ_V1.0\Mian\tools\XC_DataHanding\xcPeripheralDataBoard.h(63,1): warning C4305: “=”: 从“double”到“float”截断 (编译源文件 tools\Qwt_image\plot.cpp)
E:\VS2019\FSJ_V1.0\Mian\tools\Qwt_image\plot.cpp(426,27): error C2228: “.systemdata”的左边必须有类/结构/联合
E:\VS2019\FSJ_V1.0\Mian\tools\Qwt_image\plot.cpp(426,27): message : 类型是“CSystemConfigData *”
E:\VS2019\FSJ_V1.0\Mian\tools\Qwt_image\plot.cpp(426,27): message : 是否改用“->”?
E:\VS2019\FSJ_V1.0\Mian\tools\Qwt_image\plot.cpp(497,33): warning C4083: 应输入“newline”；找到标识符“s”
E:\VS2019\FSJ_V1.0\Mian\tools\XC_DataHanding\xcPeripheralDataBoard.h(63,1): warning C4305: “=”: 从“double”到“float”截断 (编译源文件 UI\System\BreakDownDialog.cpp)
E:\VS2019\FSJ_V1.0\Mian\UI\System\BreakDownDialog.cpp(169,85): error C2228: “.systemdata”的左边必须有类/结构/联合
E:\VS2019\FSJ_V1.0\Mian\UI\System\BreakDownDialog.cpp(169,85): message : 类型是“CSystemConfigData *”
E:\VS2019\FSJ_V1.0\Mian\UI\System\BreakDownDialog.cpp(169,85): message : 是否改用“->”?
