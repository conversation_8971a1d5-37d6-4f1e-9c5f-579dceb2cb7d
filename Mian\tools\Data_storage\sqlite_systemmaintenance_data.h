﻿#ifndef SQLITE_SYSTEMMAINTENANCE_DATA_H
#define SQLITE_SYSTEMMAINTENANCE_DATA_H

#include "tools/Data_storage/qtsqlitedb.h"
#include "tools/Data_storage/CustomSqlTableModel.h"
#include <QDateTime>
#include <QMap>
#include <QHeaderView>
struct SystemMaintenance_SQ_GROUP
{
    QString device;       // 设备
    QString personnel;    // 维修人员
    QString u_time;       // 输入时间
    QString name;         // 器件名称
    QString model;        // 器件型号
    QString manufacturer; // 生产厂家
    QString remark;       // 其他备注
    QString time;         // 时间
};

// 系统维护日志记录表
class sqlite_SystemMaintenance_data : public QtSqliteDB
{
    Q_OBJECT
public:
    explicit sqlite_SystemMaintenance_data(QString path);

    ~sqlite_SystemMaintenance_data();

    bool select_sqlitedata(QString insertSQL) override; // 查询用户数据

    QVector<struct SystemMaintenance_SQ_GROUP> data;         // 存储查询数据库的结果
    QVector<struct SystemMaintenance_SQ_GROUP> current_data; // 存储查询数据库的结果

    QVector<int> ratios = {1, 1, 1, 1, 1, 1, 1, 1}; // 设置模型各列显示比例
    int cur_row;                                    // 记录最后一行
    QTableView *tableview;                          // 存储使用模型的view
    int CURRENT_DATA_COUNT = 1;                     // 数据缓冲CURRENT_DATA_COUNT再加入数据库
    int DATEBASE_DATA_COUNT = 500000;               // 数据库的最大数据量
    // 使用CustomSqlTableModel模型
    CustomSqlTableModel *myCustomSqlTableModel;
    void use_CustomSqlTableModel(QTableView *view); // 数据库模型初始化
    bool myCustomSqlTableModel_status = false;

    bool auto_delect_en = false;

    int insertCountLimit(struct SystemMaintenance_SQ_GROUP data);                                 // 插入数据
    int deleteOldsql(QString time);                                                               // 删除早于指定创建时间的文件 时间格式yyyy-MM-dd HH:mm:ss
    int select_usetimeAndtype_SqlTableModel(QString start_time, QString stop_time, QString type); // 根据时间及维修部位查询数据库数据并显示到view中  时间格式yyyy-MM-dd HH:mm:ss
    void set_auto_delect_en(bool en);
    bool set_column_color(QVector<QColor> c);                                            // 设置最后一行的颜色
    bool set_column_color(int row, int column, const QColor &c = QColor(255, 255, 255)); // 设置指定项的颜色
    void set_column_edit(int column, bool en);

private:
    int syncToDatabase(); // 同步本地数据到数据库
    QMetaObject::Connection coon;
};

#endif // SQLITE_SYSTEMMAINTENANCE_DATA_H
