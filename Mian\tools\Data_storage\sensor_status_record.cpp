﻿#include "sensor_status_record.h"
#include <QHeaderView>
sensor_status_record::sensor_status_record(QString path)
{
    createDb("SqliteData/DATA.db");
    if (!tableExists("SensorStatusRecord_data")) {
        execute(
            "CREATE TABLE SensorStatusRecord_data ("
            "id INTEGER PRIMARY KEY AUTOINCREMENT,"
            "name TEXT NOT NULL,"
            "content TEXT NOT NULL,"
            "remark TEXT NOT NULL,"
            "time TEXT NOT NULL);"
        );
    }

    myCustomSqlTableModel = new CustomSqlTableModel("SensorStatusRecord_data");
    //    myCustomSqlTableModel = new CustomSqlTableModel();
    myCustomSqlTableModel->setTable("SensorStatusRecord_data");
    myCustomSqlTableModel->setEditStrategy(QSqlTableModel::OnManualSubmit);
    myCustomSqlTableModel->select();
    myCustomSqlTableModel->setHeaderData(1, Qt::Horizontal, tr("报警设备"));
    myCustomSqlTableModel->setHeaderData(2, Qt::Horizontal, tr("报警内容"));
    myCustomSqlTableModel->setHeaderData(3, Qt::Horizontal, tr("备注"));
    myCustomSqlTableModel->setHeaderData(4, Qt::Horizontal, tr("时间"));
    cur_row = myCustomSqlTableModel->rowCount();
}
sensor_status_record::~sensor_status_record()
{
    QObject::disconnect(coon);
    m_dbCount--;
}
bool sensor_status_record::select_sqlitedata(QString insertSQL)
{
    QSqlQuery sql_query(m_db);
    sql_query.prepare(insertSQL);
    if (!sql_query.exec())
    {
        QSqlError error = sql_query.lastError();
        qDebug() << error.text();
        return false;
    }
    else
    {
        data.clear();
        while (sql_query.next())
        {
            struct SensorStatusRecord_SQ_GROUP temp;
            temp.name = (sql_query.value("name").toString());
            temp.content = (sql_query.value("content").toString());
            temp.remark = (sql_query.value("remark").toString());
            temp.time = (sql_query.value("time").toString());
            data.append(temp);
        }
        return true;
    }
}



void sensor_status_record::use_CustomSqlTableModel(QTableView* view)
{
    if (myCustomSqlTableModel_status)
        return;
    view->setModel(myCustomSqlTableModel);
    view->setColumnHidden(0, true);
    tableview = view;
    //view->setEditTriggers(QAbstractItemView::NoEditTriggers);
    view->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
    connect(tableview->horizontalHeader(), &QHeaderView::geometriesChanged, [&]() {
        if (tableview == nullptr)
            return;
        int totalwidth = tableview->viewport()->width();
        int ratiosum = std::accumulate(ratios.begin(), ratios.end(), 0);

        tableview->setColumnWidth(1, (totalwidth * ratios[0]) / ratiosum);
        tableview->setColumnWidth(2, (totalwidth * ratios[1]) / ratiosum);
        tableview->setColumnWidth(3, (totalwidth * ratios[2]) / ratiosum);
        tableview->setColumnWidth(4, (totalwidth * ratios[3]) / ratiosum);


        });
    myCustomSqlTableModel_status = true;
}
/*
*插入故障记录数据
* 参数:
*    data 故障数据
* 返回值: 0 插入成功
*        1 参数输入存在空值
*        2 时间数据异常
*        3 同步数据失败
*        4 旧数据删除失败
*/
int sensor_status_record::insertCountLimit(struct SensorStatusRecord_SQ_GROUP data)
{
    if (data.content.isEmpty() || data.name.isEmpty() || data.remark.isEmpty() || data.time.isEmpty())
    {
        return 1;
    }



    if (myCustomSqlTableModel_status)
    {
        const int newrow = myCustomSqlTableModel->rowCount();
        myCustomSqlTableModel->insertRow(newrow);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 1), data.name);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 2), data.content);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 3), data.remark);


        if (data.time == "beijing")
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 4), QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss"));
        }
        else if (data.time == "UTC")
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 4), QDateTime::currentDateTimeUtc().toString("yyyy-MM-dd HH:mm:ss"));
        }
        else
        {
            if (!QDateTime::fromString(data.time, "yyyy-MM-dd HH:mm:ss").isValid())
            {
                myCustomSqlTableModel->removeRow(newrow);

                return 2;
            }

            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 4), data.time);
        }
        tableview->scrollToBottom();


    }
    current_data.append(data);
    if (current_data.count() >= CURRENT_DATA_COUNT)
    {
        //将数据加载到数据库
        int re = syncToDatabase();
        if (re == 1)
            return 3;
        else if (re == 2)
            return 4;
        else
            return 0;

    }
    return 0;

}
/*
*通过时间范围搜索故障几率
* 参数:
*    无
* 返回值: 0 同步成功
*        1 同步失败
*        2 旧数据删除失败
*        3 数据同步成功，搜索成功
*        4 搜索失败
*/
int sensor_status_record::select_usetime_SqlTableModel(QString start_time, QString stop_time)
{
    //在插入数据时，如果设置CURRENT_DATA_COUNT>1,则可能存在数据只加载到了模型，未加载到数据库，在查询前为防止数据丢失，先进行数据同步保存
    int re = syncToDatabase();
    if (re != 0)
        return re;
    QString sql = "";
    if (start_time == "begin" && stop_time == "end")
    {
        sql = "";
    }
    else
    {
        sql = QString("time BETWEEN '%1' AND '%2'").arg(start_time, stop_time);
    }

    myCustomSqlTableModel->setFilter(sql);
    if (myCustomSqlTableModel->select())
    {

        return 3;
    }
    else
        return 4;
}
/*
*获取指定时间段的数据类型及各类型的个数
* 参数:
*    start_time：开始时间
*    stop_time：结束时间
* 返回值: 查询到的数据
*/
QMap<QString, int> sensor_status_record::get_startTostop_type(QString start_time, QString stop_time)
{
    QMap<QString, int> data;
    QString querystr = "";
    if (start_time == "begin" && stop_time == "end")
    {
        querystr = QString("SELECT name,COUNT(*) FROM SensorStatusRecord_data GROUP BY name ");
    }
    else
    {
        querystr = QString("SELECT name,COUNT(*) FROM SensorStatusRecord_data WHERE time BETWEEN '%1' AND '%2' GROUP BY name ").arg(start_time, stop_time);
    }
    QSqlQuery sql_query(m_db);

    if (!sql_query.exec(querystr))
    {
        return data;
    }
    while (sql_query.next())
    {
        QString type = sql_query.value(0).toString();
        int count = sql_query.value(1).toInt();
        data[type] = count;
    }

    return data;
}
/*
*删除指定时间节点前的数据
* 参数:
*    无
* 返回值: 0 删除成功，刷新模型成功
*        1 删除失败
*        2 删除成功，刷新模型失败
*/
int sensor_status_record::deleteOldsql(QString time)
{
    QSqlQuery sql_query(m_db);
    sql_query.prepare("DELETE FROM SensorStatusRecord_data WHERE time < ?");
    sql_query.addBindValue(time);
    if (!sql_query.exec())
    {
        return 1;
    }

    if (myCustomSqlTableModel->select())
        return 0;
    else
        return 2;
}
/*
*通过类型搜索故障数据
* 参数:
*    无
* 返回值:
*      true 查询成功
*      false 查询失败
*
*
*
*/
bool sensor_status_record::select_usetype_SqlTableModel(QString type)
{
    if (select_type_SqlTableModel("name", type) != 3)
        return false;
    return true;
}
/*
*搜索制定类型的数据
* 参数:
*    无
* 返回值: 0 同步成功
*        1 同步失败
*        2 旧数据删除失败
*        3 数据同步成功，搜索成功
*        4 搜索失败
*/
int sensor_status_record::select_type_SqlTableModel(QString key, QString type)
{

    //在插入数据时，如果设置CURRENT_DATA_COUNT>1,则可能存在数据只加载到了模型，未加载到数据库，在查询前为防止数据丢失，先进行数据同步保存
    int re = syncToDatabase();
    if (re != 0)
        return re;

    QString sql = "";
    if (!type.isEmpty())
    {
        sql = QString("%1 = '%2'").arg(key, type);
    }

    myCustomSqlTableModel->setFilter(sql);
    if (myCustomSqlTableModel->select())
        return 3;
    else
        return 4;
}




void sensor_status_record::set_auto_delect_en(bool en)
{
    auto_delect_en = en;
}

bool sensor_status_record::set_column_color(QVector<QColor> c)
{

    int count = 0;
    QSqlQuery sql_query(m_db);
    sql_query.prepare("SELECT COUNT(*) FROM SensorStatusRecord_data");
    if (!sql_query.exec() || !sql_query.next())
    {
        QSqlError error = sql_query.lastError();
        qDebug() << error.text();
        return false;
    }
    else
    {
        count = sql_query.value(0).toInt();
    }
    qDebug() << count;
    if (myCustomSqlTableModel_status)
    {
        if (c.count() != myCustomSqlTableModel->columnCount())
            return false;
        for (int i = 0; i < myCustomSqlTableModel->columnCount(); i++)
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(count - 1, i), c[i], Qt::ForegroundRole);
        }
        return true;

    }
    return false;
}

bool sensor_status_record::set_column_color(int row, int column, const QColor& c)
{
    int count = 0;
    QSqlQuery sql_query(m_db);
    sql_query.prepare("SELECT COUNT(*) FROM SensorStatusRecord_data");
    if (!sql_query.exec() || !sql_query.next())
    {
        QSqlError error = sql_query.lastError();
        qDebug() << error.text();
        return false;
    }
    else
    {
        count = sql_query.value(0).toInt();
    }
    qDebug() << count;
    if (myCustomSqlTableModel_status)
    {
        if (count < row && myCustomSqlTableModel->columnCount() < column)
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(row, column), c, Qt::ForegroundRole);
            return true;
        }
    }
    return false;
}

void sensor_status_record::set_column_edit(int column, bool en)
{
    myCustomSqlTableModel->set_edit(column, en);
}


/*
*同步模型数据到数据库，并检查数据库数据是否超过阈值
* 参数:
*    无
* 返回值: 0 同步成功
*        1 同步失败
*        2 旧数据删除失败
*/
int sensor_status_record::syncToDatabase()
{
    if (current_data.isEmpty())
        return 0;
    //    m_db.transaction();

        // 提交到数据库
    if (!myCustomSqlTableModel->submitAll()) {

        return 1;
    }

    current_data.clear();


    if (auto_delect_en)
    {
        //当数据大于DATEBASE_DATA_COUNT时删除旧数据
        QSqlQuery cutoffquery(m_db);
        cutoffquery.prepare("SELECT id FROM SensorStatusRecord_data ORDER BY id DESC LIMIT 1 OFFSET ?");
        cutoffquery.addBindValue(DATEBASE_DATA_COUNT - 1);

        if (cutoffquery.exec() && cutoffquery.next())
        {
            quint64 cutoffid = cutoffquery.value(0).toULongLong();
            QSqlQuery delquery(m_db);
            delquery.prepare("DELETE FROM SensorStatusRecord_data WHERE id <= ?");
            delquery.addBindValue(cutoffid);
            if (!delquery.exec())
            {
                return 2;
            }
        }
    }
    //    m_db.commit();
    myCustomSqlTableModel->setFilter("");//清除过滤条件
    myCustomSqlTableModel->select();
    return 0;
}
