﻿#ifndef __PROTOCOL_EXCHANGE_H__
#define __PROTOCOL_EXCHANGE_H__



#include "xcSysVar.h"


#include "xcPeripheralServer.h"
#include "xcPeripheralDataBoard.h"
#include "xcPeripheralAirFan.h"
#include "xcPeripheralAngleMotor.h"
#include "xcPeripheralGPS.h"
#include "xcPeripheralInfrared.h"
#include "xcPeripheralTemSource.h"
#include "xcPeripheralWeather.h"
#include "xcCtrlProcess.h"




typedef enum
{
    NN = 0,		// 1byte
    NN12,		// 1byte
    NNNN,		// 2byte
    NNNNNNNN,	// 4byte
    //XXNNNN,   // 根据设置点数决定
    FFFFFFFF,
    DATAEMPTY, //雷达接收机全部数据
    SSSS, // 字符串 以0结束
    FIRMWARE, // 更新固件数据

}enumLocalDataType;


class CxcDataExchange
{
public:
    unsigned short DataID;
    enumLocalDataType Type;
    void *pAddr;

    char *pNote;

public:

    // 数据保存到本地
    void BuffToLocalAddr(unsigned char **pBuff);

    // 从本地读取数据
    void BuffFromLocalAddr(unsigned char **pBuff);

};

const CxcDataExchange tableDJFSToolToMainBoard[]=
{

    // 采样点数
    {	0x2100,		FFFFFFFF,				&g_perDataBoard.RadioVolFilterParam,			(char*)"采集卡滤波系数",	           },
    // 6路温度信号
    {	0x2111,		FFFFFFFF,				&g_perDataBoard.fTemValue[0],   	 			(char*)"接收机温度1",		           },
    {	0x2112,		FFFFFFFF,				&g_perDataBoard.fTemValue[1],    	 			(char*)"接收机温度2",		           },
    {	0x2113,		FFFFFFFF,				&g_perDataBoard.fTemValue[2],    	 			(char*)"接收机温度3",		           },
    {	0x2114,		FFFFFFFF,				&g_perDataBoard.fTemValue[3],    	 			(char*)"接收机温度4",		           },
    {	0x2115,		FFFFFFFF,				&g_perDataBoard.fTemValue[4],    	 			(char*)"接收机温度5",		           },
    {	0x2116,		FFFFFFFF,				&g_perDataBoard.fTemValue[5],    	 			(char*)"接收机温度6",		           },
    // 16路电压均值
    {	0x2120,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[0],     		(char*)"接收机电压均值1",	           },
    {	0x2121,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[1],     		(char*)"接收机电压均值2",		       },
    {	0x2122,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[2],     		(char*)"接收机电压均值3",		       },
    {	0x2123,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[3],     		(char*)"接收机电压均值4",		       },
    {	0x2124,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[4],     		(char*)"接收机电压均值5",		       },
    {	0x2125,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[5],     		(char*)"接收机电压均值6",		       },
    {	0x2126,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[6],     		(char*)"接收机电压均值7",		       },
    {	0x2127,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[7],     		(char*)"接收机电压均值8",		       },
    {	0x2128,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[8],     		(char*)"接收机电压均值9",		       },
    {	0x2129,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[9],     		(char*)"接收机电压均值10",		       },
    {	0x212a,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[10],     		(char*)"接收机电压均值11",		       },
    {	0x212b,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[11],     		(char*)"接收机电压均值12",		       },
    {	0x212c,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[12],     		(char*)"接收机电压均值13",		       },
    {	0x212d,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[13],     		(char*)"接收机电压均值14",		       },
    {	0x212e,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[14],     		(char*)"接收机电压均值15",		       },
    {	0x212f,		FFFFFFFF,				&g_perDataBoard.RadioVolAverage[15],     		(char*)"接收机电压均值16",		       },


    // 采集卡温控相
    {	0x2150,		FFFFFFFF,				&g_perDataBoard.m_fPID_P[0],	     			(char*)"采集卡温控1系数P",             },
    {	0x2151,		FFFFFFFF,				&g_perDataBoard.m_fPID_I[0],	     			(char*)"采集卡温控1系数I",             },
    {	0x2152,		FFFFFFFF,				&g_perDataBoard.m_fPID_D[0],	     			(char*)"采集卡温控1系数D",             },
    {	0x2153,		FFFFFFFF,				&g_perDataBoard.Temp_Target[0],	     		    (char*)"采集卡温控1目标温度",          },
    {	0x2154,		FFFFFFFF,				&g_perDataBoard.Temp_Curr[0],	     			(char*)"采集卡温控1当前温度",          },
    {	0x2155,		FFFFFFFF,				&g_perDataBoard.m_fHeatPower[0],     			(char*)"采集卡温控1加热功率",          },

    {	0x2156,		FFFFFFFF,				&g_perDataBoard.m_fPID_P[1],	     			(char*)"采集卡温控2系数P",             },
    {	0x2157,		FFFFFFFF,				&g_perDataBoard.m_fPID_I[1],	     			(char*)"采集卡温控2系数I",             },
    {	0x2158,		FFFFFFFF,				&g_perDataBoard.m_fPID_D[1],	     			(char*)"采集卡温控2系数D",             },
    {	0x2159,		FFFFFFFF,				&g_perDataBoard.Temp_Target[1],	     			(char*)"采集卡温控2目标温度",          },
    {	0x215a,		FFFFFFFF,				&g_perDataBoard.Temp_Curr[1],	     			(char*)"采集卡温控2当前温度",          },
    {	0x215b,		FFFFFFFF,				&g_perDataBoard.m_fHeatPower[1],     			(char*)"采集卡温控2加热功率",          },
    //{	0x215F,		NNNNNNNN,				&g_perDataBoard.RadioVolSquareParam,	     	(char*)"均方差计算系数",               },
   
    // 噪声源
    {	0x2181,		NNNNNNNN,				&g_SysVar.TTL_OutBit,	     					(char*)"TTL输出",		                },


    // GPS数据
    {	0x2300,		FFFFFFFF,				&g_perGPS.longitude,		     				(char*)"GPS经度",	                    },
    {	0x2301,		FFFFFFFF,				&g_perGPS.latitude,			     				(char*)"GPS纬度",	                },
    {	0x2302,		NNNNNNNN,				&g_perGPS.UTC_Time,			     				(char*)"GPS时间",	                },
    {	0x2303,		NNNNNNNN,				&g_perGPS.UTC_Date,			     				(char*)"GPS日期",	                },
    {	0x2304,		NNNNNNNN,				&g_perGPS.CenturySecond,	     				(char*)"GPS世纪秒",	                    },
    {	0x2305,		FFFFFFFF,				&g_perGPS.HDOP,				     				(char*)"GPS_HDOP",	                },
    {	0x2306,		FFFFFFFF,				&g_perGPS.HeightSeaLevel,	     				(char*)"GPS海拔",	                    },
    {	0x2307,		FFFFFFFF,				&g_perGPS.HeightEarthLevel,	     				(char*)"GPS地拔",	                },

    // 天线角度电机
    {	0x2400,		FFFFFFFF,				&g_CtrlProcess.ANT_AngleTarget,     			(char*)"天线设置角度",		            },
    {	0x2401,		FFFFFFFF,				&g_perAngleMotor.Horizontal_Value[0],    		(char*)"天线水平1角度",	                },
    {	0x2402,		FFFFFFFF,				&g_perAngleMotor.Horizontal_Value[1],    		(char*)"天线水平2角度",                 },
    {	0x2403,		FFFFFFFF,				&g_perAngleMotor.ANT_AngleCurr,	     		    (char*)"天线当前角度",	                },
    {	0x2404,		NNNNNNNN,				&g_perAngleMotor.m_StepPerRound,     			(char*)"天线电机步数每圈",	        	},
    {	0x2405,		NNNNNNNN,				&g_perAngleMotor.m_StepSpeedSet,     			(char*)"天线电机速度参数",	        	},
    {	0x2406,		FFFFFFFF,				&g_perAngleMotor.ANT_AngleOffset,     			(char*)"天线角度补偿",	            	},

    {	0x2501,		FFFFFFFF,				&g_perTemSource.fTemValue[0],                   (char*)"常温源温度1",	        	},
    {	0x2502,		FFFFFFFF,				&g_perTemSource.fTemValue[1],                   (char*)"常温源温度2",	        	},
    {	0x2503,		FFFFFFFF,				&g_perTemSource.fTemValue[2],                   (char*)"常温源温度3",	        	},
    {	0x2504,		FFFFFFFF,				&g_perTemSource.fTemValue[3],                   (char*)"常温源温度4",	        	},
    {	0x2505,		FFFFFFFF,				&g_perTemSource.fTemp_LowAverage,               (char*)"常温源平均温度",	        	},

    // 气象六要素

    {	0x2607,		FFFFFFFF,				&g_perWeather.Humidity,	     				    (char*)"气象六要素湿度",		        },
    {	0x2608,		FFFFFFFF,				&g_perWeather.Temp,		     				    (char*)"气象六要素温度",		        },
    {	0x2609,		FFFFFFFF,				&g_perWeather.AirPress,	     				    (char*)"气象六要素气压",		        },
    {	0x260a,		FFFFFFFF,				&g_perWeather.WindSpeed,     					(char*)"气象六要素风速",		        },
    {	0x260b,		FFFFFFFF,				&g_perWeather.WindDir,	     					(char*)"气象六要素风向",		        },
    {	0x260c,		FFFFFFFF,				&g_perWeather.Rain10Minute,  					(char*)"气象六要素10分钟降水量",		},


    // 风机
    {	0x2700,		NNNNNNNN,				&g_perAirFan.Speed,		     				    (char*)"风机转速",		                },
    {	0x2701,		FFFFFFFF,				&g_perAirFan.PwmValue,	     					(char*)"风机功率",		                },

    // 本机参数
    {	0x1010,		NNNNNNNN,				&g_perServer.Uart2Server_Baud,	     			(char*)"波特率",		                },
    {	0x1011,		NNNNNNNN,				&g_perServer.TcpServerIP[0],	     			(char*)"服务器IP1",		                },
    {	0x1012,		NNNNNNNN,				&g_perServer.TcpServerIP[1],	     			(char*)"服务器IP2",		                },
    {	0x1013,		NNNNNNNN,				&g_perServer.TcpServerPort[0],	     			(char*)"服务器端口1",		            },
    {	0x1014,		NNNNNNNN,				&g_perServer.TcpServerPort[1],	     			(char*)"服务器端口2",		            },
    {	0x1015,		NN,				        &g_SysVar.CommRelayTest,			     		(char*)"中继使能",		                },

    // 外设工作状态
    {	0x1020,		NN,						&g_perDataBoard.m_Status,		     			(char*)"采集卡工作状态",				},
    {	0x1023,		NN,						&g_perAngleMotor.m_Status,		     			(char*)"角度电机工作状态",				},
    {	0x1025,		NN,						&g_perWeather.m_Status,			     		    (char*)"气象六要素工作状态",			},
    {	0x1026,		NN,						&g_perAirFan.m_Status,			     			(char*)"风机工作状态",					},

    {	0x1030,		NN,						&g_CtrlProcess.RunMode,					        (char*)"工作模式",						},

    {	0x1040,		NN,						&g_perDataBoard.m_PowerSet,		     		    (char*)"采集卡工作电源",				},
    {	0x1043,		NN,						&g_perAngleMotor.m_PowerSet,	     			(char*)"角度电机工作电源",				},
    {	0x1045,		NN,						&g_perWeather.m_PowerSet,		     			(char*)"气象六要素工作电源",			},
    {	0x1046,		NN,						&g_perAirFan.m_PowerSet,		     			(char*)"风机工作电源",					},
   // 版本字符串
    {	0x8000,		SSSS,			        &g_SysVar.StrSoftwareVersion,	     			(char*)"主板软件版本",					},
    {	0x8001,		SSSS,			        &g_SysVar.StrSoftwareVersionOfDB,    			(char*)"采集卡软件版本",				},
    {	0x8002,		NNNNNNNN,				&g_SysVar.FirewareUpdateAddr,	     			(char*)"更新固件包地址",				},
    {	0x8003,		FIRMWARE,				&g_SysVar.pFirewareBuff,		     			(char*)"更新固件包指针",				},
    {	0x8004,		SSSS,					&g_SysVar.FirewareUpdateCmd,	     			(char*)"更新固件指令",					},
    {	0x8005,		NNNNNNNN,				&g_SysVar.FirewareUpdateCrc,	     			(char*)"更新固件CRC",					},

    {	0x4210,		FFFFFFFF,				&g_CtrlProcess.m_arrayTofNormalTem[0],     	    (char*)"第1路常温源温度值",				},

    {	0x4F00,		NN12,					&g_SysVar.DeviceID,							    (char*)"设备唯一ID",				 	},
  
    { 0x0000,		NN,						0,												NULL, },
};




// 根据数据项ID，和本地数据进行交换
extern int DataExchangeFunc(void *pDataExChangeTable,unsigned short id,unsigned char **pBuff,int mode);

extern CxcDataExchange* GetDataBlockByID(void *pDataExChangeTable, unsigned short id );



#endif
