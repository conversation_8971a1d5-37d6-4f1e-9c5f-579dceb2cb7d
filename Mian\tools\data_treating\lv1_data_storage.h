﻿#ifndef LV1_DATA_STORAGE_H
#define LV1_DATA_STORAGE_H
#include <tools/data_treating/cpublic_struct.h>
#include <QObject>
#include <QDir>
#include <cmath>
#include <QDateTime>
#include <QTextStream>
#include "tools/data_treating/lv2_data_storage.h"
struct Lv1_Bright_temperature_data
{
    QString Record;   // 含义：记录序号              具体值
    QString DateTime; // 含义：记录日期及时间         格式为yyyy-mm-dd  hh:mm:ss，规则详见表5
    double SurTem;    // 含义：地面温度              具体观测结果，单位为℃，保留2位小数
    double SurHum;    // 含义：地面湿度              具体观测结果，单位为%RH，保留2位小数
    double SurPre;    // 含义：地面气压              具体观测结果，单位为hPa，保留2位小数
    double Tir;       // 含义：红外温度              具体观测结果，单位为℃，，保留2位小数
    int Rain;         // 含义：是否降水              具体观测结果，是否降水（1＝是，0＝否）
    int QCFlag;       // 含义：质控码                规则详见表3-3-6
    double Azimuth;   // 含义：方位角                具体观测结果，单位为度，保留3位小数
    double Elevation; // 含义：俯仰角                具体观测结果，单位为度，保留3位小数
    double Ch_Freq1;  // 含义：频率1观测亮温          具体观测结果，单位为K，保留3位小数
    double Ch_Freq2;  // 含义：频率2观测亮温          具体观测结果，单位为K，保留3位小数
    double Ch_Freq3;  // 含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数
    double Ch_Freq4;  // 含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数
    double Ch_Freq5;  // 含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数
    double Ch_Freq6;  // 含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数
    double Ch_Freq7;  // 含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数
    double Ch_Freq8;  // 含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数
    double Ch_Freq9;  // 含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数
    double Ch_Freq10; // 含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数
    double Ch_Freq11; // 含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数
    double Ch_Freq12; // 含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数
    double Ch_Freq13; // 含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数
    double Ch_Freq14; // 含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数
    //    double Ch_Freq15 ;                   //含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数
    //    double Ch_Freq16 ;                   //含义：频率n观测亮温          具体观测结果，单位为K，保留3位小数

    QString QCFlag_BT; // 含义：亮温质控编码            n1n2n3n4n5（文本格式）。规则详见表7
};

class Lv1_data_storage : public QObject
{
    Q_OBJECT
public:
    // file_path文件路径  "E:/sjl/qt/build-sqlite_class-Desktop_Qt_5_14_2_MinGW_64_bit-Debug/数据/数据Lv1/"
    // file_name 文件名:用户配置以下参数即可
    //    QString of1ag;                      // 格式：I                                                 含义：按台站区站号进行编码
    //    QString originator;                 // 格式：IIiii                                             含义：气象台站区站号，5位字符
    //    QString equipmenttype;              // 格式：设备型号                                           含义：生产厂家自定义，5个大写字符

    explicit Lv1_data_storage(QString file_path, struct FILENAME file_name, QObject* parent = nullptr);

    // 日文件
    bool Generate_new_Lv1_Bright_temperature_data(struct Lv_Basic_parameters basic_parameters, struct Lv1_Bright_temperature_data bright_temperature);
    // 分钟文件
    bool Generate_new_Lv1_Minutes_file(struct Lv_Basic_parameters basic_parameters, struct Lv1_Bright_temperature_data bright_temperature);

    void set_file_path(QString path);         // 设置文件路径
    void set_file_name(struct FILENAME name); // 设置文件名
    void set_Minutes_file_en(bool en);        // 设置分钟文件存储使能
    void set_Day_file_en(bool en);            // 设置日文件存储使能
    void set_beijing_or_utc(bool choose);     // 设置文件路径及文件名中的时间时使用北京时还是UTC时间  true beijing false utc
    struct FILENAME filename;                 // 文件名称信息结构体
    QString cur_day_filename;                 // 当前日文件名称
    QString filepath;                         // 文件保存路径
    QFile* file;                              // 记录日文件

signals:

public slots:
    void save_data(struct Lv_Basic_parameters basic_parameters, struct Lv1_Bright_temperature_data bright_temperature);

private:
    bool Insert_new_Lv1_Bright_temperature_data(Lv1_Bright_temperature_data bright_temperature); // 在日文件中插入一条新的亮温数据
    bool Insert_new_Lv1_convert_lv2_data(Lv1_Bright_temperature_data bright_temperature);
    QString Generate_file_name(struct FILENAME file_name);                                       // 生成文件名
    bool isPureAscii(const QString& str);                                                        // 判断字符串是否是ASCII码
    bool Insert_new_Lv_Basic_parameters(struct Lv_Basic_parameters basic_parameters);            // 在日文件中插入一条基础数据
    bool Insert_new_Lv1_convert_lv2_Basic_parameters(struct Lv_Basic_parameters basic_parameters);// 在日文件中插入一条基础数据
    QString Control_decimal_bit(double data, int bit, bool status);                              // 控制小数位

    QString deal_int_data(int data);
    QString deal_qstring_data(QString data);

    /*在田间日文件时判断本地路径下是否已经存在日文件，不考虑时分秒，但要验证其合法性*/
    bool comparefilenames(const QString& filename1, const QString& filename2);                                      // 对比文件名
    bool processFileName(const QString& filename, bool& timevalid, QString& processedbasename, QString& extension); // 处理并验证文件名，生成忽略时分秒后的新的文件名。
    // int ch = 14;
    int Record_number = 1; // 记录日文件的行号

    bool Minutes_file_en = false; // 是否保存为分钟文件
    bool Day_file_en = false;     // 是否保存为日文件
    bool use_beijing_utc = true;  // 北京时和utc时的标志位
};

#endif // LV1_DATA_STORAGE_H
