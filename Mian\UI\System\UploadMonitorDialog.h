﻿#ifndef UPLOADMONITORDIALOG_H
#define UPLOADMONITORDIALOG_H

#include <QDialog>
#include "qcustomplot.h"
#include <QtCharts>

namespace Ui
{
    class UploadMonitorDialog;
}

class UploadMonitorDialog : public QDialog
{
    Q_OBJECT

public:
    explicit UploadMonitorDialog(QWidget* parent = nullptr);
    ~UploadMonitorDialog();
    void CreatpieSetView();
public slots:
    void OnSliceClicked();

private slots:
    void on_pushButton_ftp_add_clicked();

    void on_pushButton_delete_clicked();

    void on_pushButton_search_status_clicked();

    void on_pushButton_search_get_status_clicked();

    void on_pushButton_search_clicked();

private:
    Ui::UploadMonitorDialog* ui;
    QTimer* auto_delect_data;
};

#endif // UPLOADMONITORDIALOG_H
