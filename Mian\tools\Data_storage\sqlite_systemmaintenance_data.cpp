﻿#include "tools/Data_storage/sqlite_systemmaintenance_data.h"

sqlite_SystemMaintenance_data::sqlite_SystemMaintenance_data(QString path)
{
    createDb(path);
    if (!tableExists("SystemMaintenance_data"))
    {
        execute(
            "CREATE TABLE SystemMaintenance_data ("
            "id INTEGER PRIMARY KEY AUTOINCREMENT,"
            "device TEXT NOT NULL,"
            "personnel TEXT NOT NULL,"
            "u_time TEXT NOT NULL,"
            "name TEXT NOT NULL,"
            "model TEXT NOT NULL,"
            "manufacturer TEXT NOT NULL,"
            "remark TEXT NOT NULL,"
            "time TEXT NOT NULL);");
    }

    myCustomSqlTableModel = new CustomSqlTableModel("SystemMaintenance_data");
    myCustomSqlTableModel->setTable("SystemMaintenance_data");
    myCustomSqlTableModel->setEditStrategy(QSqlTableModel::OnFieldChange);
    myCustomSqlTableModel->select();
    myCustomSqlTableModel->setHeaderData(1, Qt::Horizontal, tr("维修部位"));
    myCustomSqlTableModel->setHeaderData(2, Qt::Horizontal, tr("维修人员"));
    myCustomSqlTableModel->setHeaderData(3, Qt::Horizontal, tr("维修时间"));
    myCustomSqlTableModel->setHeaderData(4, Qt::Horizontal, tr("器件名称"));
    myCustomSqlTableModel->setHeaderData(5, Qt::Horizontal, tr("器件型号"));
    myCustomSqlTableModel->setHeaderData(6, Qt::Horizontal, tr("生产厂家"));
    myCustomSqlTableModel->setHeaderData(7, Qt::Horizontal, tr("其他备注"));
    myCustomSqlTableModel->setHeaderData(8, Qt::Horizontal, tr("创建时间"));
}

sqlite_SystemMaintenance_data::~sqlite_SystemMaintenance_data()
{
    QObject::disconnect(coon);
    m_dbCount--;
}

bool sqlite_SystemMaintenance_data::select_sqlitedata(QString insertSQL)
{
    QSqlQuery sql_query(m_db);
    sql_query.prepare(insertSQL);
    if (!sql_query.exec())
    {
        QSqlError error = sql_query.lastError();
        qDebug() << error.text();
        return false;
    }
    else
    {
        data.clear();
        while (sql_query.next())
        {
            struct SystemMaintenance_SQ_GROUP temp;
            temp.device = (sql_query.value("device").toString());
            temp.personnel = (sql_query.value("personnel").toString());
            temp.u_time = (sql_query.value("u_time").toString());
            temp.name = (sql_query.value("name").toString());
            temp.model = (sql_query.value("model").toString());
            temp.manufacturer = (sql_query.value("manufacturer").toString());
            temp.remark = (sql_query.value("remark").toString());
            temp.time = (sql_query.value("time").toString());

            data.append(temp);
        }
        return true;
    }
}

void sqlite_SystemMaintenance_data::use_CustomSqlTableModel(QTableView *view)
{
    if (myCustomSqlTableModel_status)
        return;
    view->setModel(myCustomSqlTableModel);
    view->setColumnHidden(0, true);

    set_column_edit(8, false);

    tableview = view;
    // view->setEditTriggers(QAbstractItemView::NoEditTriggers);
    view->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
    coon = connect(tableview->horizontalHeader(), &QHeaderView::geometriesChanged, [&]()
                   {
                       if (tableview == nullptr)
                           return;
                       int totalwidth = tableview->viewport()->width();
                       int ratiosum = std::accumulate(ratios.begin(), ratios.end(), 0);

                       tableview->setColumnWidth(1, (totalwidth * ratios[0]) / ratiosum);
                       tableview->setColumnWidth(2, (totalwidth * ratios[1]) / ratiosum);
                       tableview->setColumnWidth(3, (totalwidth * ratios[2]) / ratiosum);
                       tableview->setColumnWidth(4, (totalwidth * ratios[3]) / ratiosum);
                       tableview->setColumnWidth(5, (totalwidth * ratios[4]) / ratiosum);
                       tableview->setColumnWidth(6, (totalwidth * ratios[5]) / ratiosum);
                       tableview->setColumnWidth(7, (totalwidth * ratios[6]) / ratiosum);
                       tableview->setColumnWidth(8, (totalwidth * ratios[7]) / ratiosum);
                   });
    myCustomSqlTableModel_status = true;
}
/*
 *插入故障记录数据
 * 参数:
 *    data 故障数据
 * 返回值: 0 插入成功
 *        1 参数输入存在空值
 *        2 时间数据异常
 *        3 同步数据失败
 *        4 旧数据删除失败
 */
int sqlite_SystemMaintenance_data::insertCountLimit(SystemMaintenance_SQ_GROUP data)
{
    if (data.device.isEmpty() || data.manufacturer.isEmpty() || data.model.isEmpty() || data.name.isEmpty() || data.personnel.isEmpty() || data.remark.isEmpty() || data.u_time.isEmpty())
    {
        return 1;
    }

    if (myCustomSqlTableModel_status)
    {
        const int newrow = myCustomSqlTableModel->rowCount();
        myCustomSqlTableModel->insertRow(newrow);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 1), data.device);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 2), data.personnel);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 3), data.u_time);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 4), data.name);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 5), data.model);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 6), data.manufacturer);
        myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 7), data.remark);

        if (data.time == "beijing")
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 8), QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss"));
        }
        else if (data.time == "UTC")
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 8), QDateTime::currentDateTimeUtc().toString("yyyy-MM-dd HH:mm:ss"));
        }
        else
        {
            if (!QDateTime::fromString(data.time, "yyyy-MM-dd HH:mm:ss").isValid())
            {
                myCustomSqlTableModel->removeRow(newrow);

                return 2;
            }

            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(newrow, 8), data.time);
        }
        tableview->scrollToBottom();
    }
    current_data.append(data);
    if (current_data.count() >= CURRENT_DATA_COUNT)
    {
        // 将数据加载到数据库
        int re = syncToDatabase();
        if (re == 1)
            return 3;
        else if (re == 2)
            return 4;
        else
            return 0;
    }
    return 0;
}
/*
 *删除指定创建时间节点前的数据
 * 参数:
 *    无
 * 返回值: 0 删除成功，刷新模型成功
 *        1 删除失败
 *        2 删除成功，刷新模型失败
 */
int sqlite_SystemMaintenance_data::deleteOldsql(QString time)
{
    QSqlQuery sql_query(m_db);
    sql_query.prepare("DELETE FROM SystemMaintenance_data WHERE time < ?");
    sql_query.addBindValue(time);
    if (!sql_query.exec())
    {
        return 1;
    }

    if (myCustomSqlTableModel->select())
        return 0;
    else
        return 2;
}

int sqlite_SystemMaintenance_data::select_usetimeAndtype_SqlTableModel(QString start_time, QString stop_time, QString type)
{
    // 在插入数据时，如果设置CURRENT_DATA_COUNT>1,则可能存在数据只加载到了模型，未加载到数据库，在查询前为防止数据丢失，先进行数据同步保存
    int re = syncToDatabase();
    if (re != 0)
        return re;

    QString sql = "";
    if (start_time == "begin" && stop_time == "end" && type == "所有类型")
    {
        sql = "";
    }
    else if (start_time == "begin" && stop_time == "end" && type != "所有类型")
    {
        sql = QString("device = '%1'").arg(type);
    }
    else if ((start_time != "begin" && stop_time != "end") && type == "所有类型")
    {
        sql = QString("time BETWEEN '%1' AND '%2'").arg(start_time, stop_time);
    }
    else
    {
        sql = QString("(time BETWEEN '%1' AND '%2') AND (device = '%3')").arg(start_time, stop_time, type);
    }

    //    QString sql;
    //    if(type == "所有类型")
    //         sql = QString("time BETWEEN '%1' AND '%2'").arg(start_time,stop_time);
    //    else
    //         sql = QString("(time BETWEEN '%1' AND '%2') AND (device = '%3')").arg(start_time,stop_time,type);
    myCustomSqlTableModel->setFilter(sql);
    if (myCustomSqlTableModel->select())
    {

        return 3;
    }
    else
        return 4;
}

void sqlite_SystemMaintenance_data::set_auto_delect_en(bool en)
{
    auto_delect_en = en;
}

bool sqlite_SystemMaintenance_data::set_column_color(QVector<QColor> c)
{
    int count = 0;
    QSqlQuery sql_query(m_db);
    sql_query.prepare("SELECT COUNT(*) FROM FaultRecord_data");
    if (!sql_query.exec() || !sql_query.next())
    {
        QSqlError error = sql_query.lastError();
        qDebug() << error.text();
        return false;
    }
    else
    {
        count = sql_query.value(0).toInt();
    }
    qDebug() << count;
    if (myCustomSqlTableModel_status)
    {
        if (c.count() != myCustomSqlTableModel->columnCount())
            return false;
        for (int i = 0; i < myCustomSqlTableModel->columnCount(); i++)
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(count - 1, i), c[i], Qt::ForegroundRole);
        }
        return true;
    }
    return false;
}

bool sqlite_SystemMaintenance_data::set_column_color(int row, int column, const QColor &c)
{
    int count = 0;
    QSqlQuery sql_query(m_db);
    sql_query.prepare("SELECT COUNT(*) FROM FaultRecord_data");
    if (!sql_query.exec() || !sql_query.next())
    {
        QSqlError error = sql_query.lastError();
        qDebug() << error.text();
        return false;
    }
    else
    {
        count = sql_query.value(0).toInt();
    }
    qDebug() << count;
    if (myCustomSqlTableModel_status)
    {
        if (count < row && myCustomSqlTableModel->columnCount() < column)
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(row, column), c, Qt::ForegroundRole);
            return true;
        }
    }
    return false;
}

void sqlite_SystemMaintenance_data::set_column_edit(int column, bool en)
{
    myCustomSqlTableModel->set_edit(column, en);
}
/*
 *同步模型数据到数据库，并检查数据库数据是否超过阈值
 * 参数:
 *    无
 * 返回值: 0 同步成功
 *        1 同步失败
 *        2 旧数据删除失败
 */
int sqlite_SystemMaintenance_data::syncToDatabase()
{
    if (current_data.isEmpty())
        return 0;
    //    m_db.transaction();

    // 提交到数据库
    if (!myCustomSqlTableModel->submitAll())
    {

        return 1;
    }

    current_data.clear();

    if (auto_delect_en)
    {

        // 当数据大于DATEBASE_DATA_COUNT时删除旧数据
        QSqlQuery cutoffquery(m_db);
        cutoffquery.prepare("SELECT id FROM SystemMaintenance_data ORDER BY id DESC LIMIT 1 OFFSET ?");
        cutoffquery.addBindValue(DATEBASE_DATA_COUNT - 1);

        if (cutoffquery.exec() && cutoffquery.next())
        {
            quint64 cutoffid = cutoffquery.value(0).toULongLong();
            QSqlQuery delquery(m_db);
            delquery.prepare("DELETE FROM SystemMaintenance_data WHERE id <= ?");
            delquery.addBindValue(cutoffid);
            if (!delquery.exec())
            {
                return 2;
            }
        }
    }
    //    m_db.commit();
    myCustomSqlTableModel->setFilter(""); // 清除过滤条件
    myCustomSqlTableModel->select();

    return 0;
}
