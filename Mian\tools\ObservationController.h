﻿#ifndef OBSERVATIONCONTROLLER_H
#define OBSERVATIONCONTROLLER_H

#include <QObject>
#include <QUdpSocket>
#include <QJsonDocument>
#include <QJsonObject>
#include <QProcess>
#include <QNetworkDatagram>

class ObservationController : public QObject
{
    Q_OBJECT
public:
    explicit ObservationController(QObject *parent = nullptr);
    explicit ObservationController(const QString &ip, quint16 port, QObject *parent = nullptr);
    ~ObservationController();

public slots:
    bool start();
    void stop();
    void restart();

protected:
signals:
    void observationStarted();
    void observationStopped();
    void observationFailed(const QString &error);

protected:
    virtual void startObserving();

    virtual void stopObserving() {
        emit observationStopped();
    }

private:
    QUdpSocket *m_udpSocket;
    QHostAddress m_listenIp;
    quint16 m_listenPort;
    void processDatagram();
};

#endif // OBSERVATIONCONTROLLER_H
