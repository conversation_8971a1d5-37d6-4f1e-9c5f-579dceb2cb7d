#include "HistoryDialog.h"
#include "ui_HistoryDialog.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QPushButton>
#include <QTime>
#include <QDebug>
#include <QDir>
#include <QDesktopServices>
#include <QUrl>

HistoryDialog::HistoryDialog(QWidget *parent) : QDialog(parent),
                                                ui(new Ui::HistoryDialog)
{
    ui->setupUi(this);
    this->setWindowTitle("历史数据");
    this->setWindowFlags(this->windowFlags() | Qt::WindowMaximizeButtonHint);
    this->setWindowFlags(Qt::Window);

    set_HistoryDialog_styleSheet(systemconfigfdata->systemdata.set_skin);
}

HistoryDialog::~HistoryDialog()
{
    delete ui;
}

QTreeWidget *HistoryDialog::get_QTreeWidget()
{
    return ui->treeWidget_lv2;
}

QTreeWidget *HistoryDialog::get_QTreeWidget_lv1()
{
    return ui->treeWidget_lv1;
}
void HistoryDialog::LoadStyleFile(QString strStyle)
{

}
void HistoryDialog::set_HistoryDialog_styleSheet(int index)
{

}
