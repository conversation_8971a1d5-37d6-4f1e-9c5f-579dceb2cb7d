#include "mainwindow.h"
#include "include/relativehumidityprofile.h"  //廓线反演类
#include "include/StructRecvBusinessData.h"
#include "include/temperatureprofile.h"
#include "include/watervapordensityprofile.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , settings("Dataconfig/MainWindow_Layout.ini", QSettings::IniFormat)
{
    ui->setupUi(this);
    this->setWindowTitle("垂直廓线显示终端");
    creat_configfile(); // 配置文件
    m_pool = new ThreadPool(this);

    /***    状态栏         ***/
    // 获取当前的系统时间  记录本次的启动时间
    currentDateTime = QDateTime::currentDateTime();
    // 将时间转换为字符格式
    QString currentDateTimeString = currentDateTime.toString("yyyy-MM-dd hh:mm:ss");
    qDebug() << "当前系统时间:" << currentDateTimeString;


    //lv1数据
    struct FILENAME file_name = { "Z", "UPAR", "I", "54511", QDateTime::currentDateTime().toString("yyyyMMddHHmmss"), "O", "YMWR", "AD002", "RAW", "D", "txt" };
    file_name.of1ag = systemconfigfdata->systemdata.station_number;
    file_name.originator = systemconfigfdata->systemdata.weather_number;
    file_name.equipmenttype = systemconfigfdata->systemdata.business_number;
    QString file_path = systemconfigfdata->systemdata.LV1_path;
    lv1_data_storage = new Lv1_data_storage(file_path, file_name);

    //lv2数据
    struct FILENAME file_name2;
    file_name2.of1ag = systemconfigfdata->systemdata.station_number;
    file_name2.originator = systemconfigfdata->systemdata.weather_number;
    file_name2.equipmenttype = systemconfigfdata->systemdata.business_number;
    QString file_path2 = systemconfigfdata->systemdata.LV2_path;
    lv2_data_storage = new Lv2_data_storage(file_path2, file_name2);


    //状态数据
    struct FILENAME file_name3;
    file_name3.of1ag = "I";
    file_name3.originator = "545112";
    file_name3.equipmenttype = "AD002";
    QString file_path3 = systemconfigfdata->systemdata.status_path;
    device_status_data_storage = new Device_status_data_storage(file_path3, file_name3);
    //定标数据
    struct FILENAME file_name4;
    file_name4.of1ag = "I";
    file_name4.originator = "545112";
    file_name4.equipmenttype = "AD002";
    QString file_path4 = systemconfigfdata->systemdata.dingbiao_path;
    calibrate_data_storage = new Calibrate_data_storage(file_path4, file_name4);

    // 状态栏系统时间
    system_time = new QTimer();
    infomation_label = new QLabel();
    timelabel = new QLabel();
    statusPushButton = new QPushButton();

    //lv1数据文件保存
    lv1_data = new CxcDataExchange();
    get_lv1 = new QTimer();
    connect(get_lv1, SIGNAL(timeout()), this, SLOT(get_lv1_data())); //lv1数据获取

    auto_delect_data = new QTimer();
    connect(auto_delect_data, SIGNAL(timeout()), this, SLOT(slot_auto_delect_data())); //数据自动清理

    // 网络配置
    commNetWork = new classWidgetComm();
    widgetComm = commNetWork;
    connect(statusPushButton, SIGNAL(clicked()), this, SLOT(set_FSJ_netWork())); // 网络通信
    // 配置位置
    statusPushButton->setText("连接状态");
    infomation_label->setText("西安华腾微波有限责任公司");
    statusPushButton->setStyleSheet("color:white;border:none;");
    infomation_label->setStyleSheet("color:white;");
    timelabel->setStyleSheet("color:white;");

    ui->statusbar->addPermanentWidget(statusPushButton, 1);
    ui->statusbar->addPermanentWidget(infomation_label, 1);
    ui->statusbar->addPermanentWidget(timelabel);
    connect(system_time, SIGNAL(timeout()), this, SLOT(set_system_time_status())); // 状态栏系统时间定时器
    system_time->start(1000);
    set_system_time_status(); // 立刻刷新




    // 鼠标悬停提示信息
    ui->action_leftmove_tool->setToolTip("为彩图数据左移");
    ui->action_rightmove_tool->setToolTip("为彩图数据右移");
    ui->action_oneImage->setToolTip("指定布局视图的选择与隐藏");
    ui->action_cut_image->setToolTip("屏幕截图");
    ui->action_query_user->setToolTip("用户登录");
    ui->action_distance_tool->setToolTip("为彩图纵轴范围设置");
    ui->action_rightmove_tool->setToolTip("为彩图横轴范围设置");
    ui->action_history_data->setToolTip("历史数据查询");

    // 菜单栏 下拉框
    menu_view = new QMenu(this);
    menu_time = new QMenu(this);
    menu_dis = new QMenu(this);
    set_action_comboBox();

    // LV2数据绘制
    d_plot = new Plot(1, this);
    d_plot2 = new Plot(1, this);
    d_plot3 = new Plot(1, this);
    d_plot4 = new Plot(2, this);

    /***       廓线图                     ***/
    // 廓线图鼠标游标显示
    tracer_one = new QCPItemTracer(ui->widget_one_view_line);
    tracerLabe_one = new QCPItemText(ui->widget_one_view_line); // 生成游标说明
    set_tracer(ui->widget_one_view_line, tracer_one, tracerLabe_one);

    tracer_two = new QCPItemTracer(ui->widget_two_view_line);
    tracerLabe_two = new QCPItemText(ui->widget_two_view_line); // 生成游标说明
    set_tracer(ui->widget_two_view_line, tracer_two, tracerLabe_two);

    tracer_three = new QCPItemTracer(ui->widget_three_view_line);
    tracerLabe_three = new QCPItemText(ui->widget_three_view_line); // 生成游标说明
    set_tracer(ui->widget_three_view_line, tracer_three, tracerLabe_three);
    //廓线图隐藏
    ui->widget_one_view_line->hide();
    ui->widget_two_view_line->hide();
    ui->widget_three_view_line->hide();
    ui->label_one_view_line->hide();
    ui->label_two_view_line->hide();
    ui->label_three_view_line->hide();

    // 廓线图 添加画布
    ui->widget_one_view_line->addGraph();
    ui->widget_two_view_line->addGraph();
    ui->widget_three_view_line->addGraph();


    /***      其他界面创建           ***/
    action_view = new QAction();
    historydialog = new HistoryDialog();               // 历史记录界面
    logdialog = new LogDialog();                       // 日志记录界面
    sensorstatusdialog = new SensorStatusDialog();     // 传感器界面
    setParameter = new ControlSetParameterDialog();    // 设置参数界面
    dailymaintenance = new DailyMaintenanceDialog();   // 日常维护界面
    systemmaintenance = new SystemMaintenanceDialog(); // 系统维护界面
    uploadMonitor = new UploadMonitorDialog();         // 上传监控界面
    breakdown = new BreakDownDialog();                 // 故障管理界面
    fmgparameter = new FMGParameterDialog();           // 微波辐射参数界面
    fmgwidget = new FMGWidget();                       // 状态监控界面
    systemconfiguration = new SystemConfigurationDialog(); // 系统配置界面
    systemregister = new SystemRegister(); // 用户登录
    sensorStatusRecord = new SensorStatusRecodeDialog();//传感器状态记录界面

    m_log = logdialog;
    ftp = new FTPDialog(logdialog);
    // 界面连接槽
    connect(this, SIGNAL(CMainWindowToSystemConfigurationDialog(int)), systemconfiguration, SLOT(on_MWToOther(int)));
    connect(systemconfiguration, SIGNAL(CHistory_file_Path(QString)), this, SLOT(History_file_Path(QString)));
    connect(systemconfiguration, SIGNAL(CHistory_file_Path_lv1(QString)), this, SLOT(History_file_Path_lv1(QString)));
    connect(systemconfiguration, SIGNAL(set_background_skin(int)), this, SLOT(set_menubar_styleSheet(int)));
    connect(systemregister, SIGNAL(SystemRegistertoMainWindow()), this, SLOT(on_SystemRegister()));
    connect(systemregister, SIGNAL(signal_CalibrationParameterdata()), setParameter, SLOT(set_calibrationParameterdata()));
    connect(this, SIGNAL(Signal_CLightDataLv1()), systemconfiguration, SLOT(slot_save_Lv1_Data()));
    connect(this, SIGNAL(set_logRecord_color_signal(QString)), logdialog, SLOT(add_Operational_information(QString)));//日志文件表格数据读取槽函数
    connect(systemconfiguration, SIGNAL(signal_logRecord_color(QColor)), logdialog, SLOT(set_logRecord_color(QColor)));//表格设置颜色槽函数


    // dockwidget 标题栏颜色设置
    title_skin = "background: rgb(62, 62, 62);color: rgb(255, 255, 255);border:0px;";
    button_title_close_skin = "background: rgb(255, 255, 255);";
    title_skin1 = "background: rgb(87, 106, 140);color: rgb(255, 255, 255);border:0px;";

    // 界面布局设置 dockwidget
    // 第三方dockwidget使用
    //  配置 ADS 全局标志
    CDockManager::setConfigFlag(CDockManager::DockAreaHasCloseButton, true);                  // 停靠区域无关闭按钮
    CDockManager::setConfigFlag(CDockManager::AllTabsHaveCloseButton, false);                 // 所有标签页显示关闭按钮
    CDockManager::setConfigFlag(CDockManager::DockAreaHasUndockButton, false);                // 隐藏取消停靠按钮
    CDockManager::setConfigFlag(CDockManager::DockAreaDynamicTabsMenuButtonVisibility, true); // 动态显示溢出菜单按钮
    CDockManager::setConfigFlag(CDockManager::DisableTabTextEliding, true);                   // 禁用标签文字省略
    CDockManager::setConfigFlag(CDockManager::DoubleClickUndocksWidget, false);               // 禁用双击取消停靠
    CDockManager::setConfigFlag(CDockManager::HideSingleCentralWidgetTitleBar, true);         // 禁用标签文字省略
    CDockManager::setConfigFlag(CDockManager::ActiveTabHasCloseButton, false);                // 禁用双击取消停靠

    DockManager = new CDockManager(this); // 创建 ADS 管理器，管理所有停靠部件
#pragma warning(suppress : 4996)
    CentralDockWidget = new CDockWidget("centralWidget");
    CentralDockWidget->setWidget(ui->widget);
    // CentralDockWidget->setFeature(ads::CDockWidget::NoTab, true);    // 隐藏中央部件的标签页
    CentralDockWidget->setMinimumSizeHintMode(CDockWidget::MinimumSizeHintFromDockWidget); // 尺寸策略
    CentralDockWidget->resize(100, 100);                                                   // 初始大小
    CentralDockWidget->setMinimumSize(1, 1);
    auto *CentralDockArea = DockManager->setCentralWidget(CentralDockWidget); // 将部件设为中央区域

#pragma warning(suppress : 4996)
    leftPlace = new CDockWidget("历史记录");
    leftPlace->setWidget(historydialog);
    leftPlace->setMinimumSizeHintMode(CDockWidget::MinimumSizeHintFromDockWidget); // 尺寸策略
    leftPlace->resize(220, 1);                                                     // 初始大小
    leftPlace->setMinimumSize(220, 1);                                             // 最小尺寸
    
    DockManager->addDockWidget(DockWidgetArea::LeftDockWidgetArea, leftPlace);     // 添加到左侧区域

#pragma warning(suppress : 4996)
    rightUpPlaceholder = new CDockWidget("传感器状态");
    rightUpPlaceholder->setWidget(sensorstatusdialog);
    rightUpPlaceholder->setMinimumSizeHintMode(CDockWidget::MinimumSizeHintFromDockWidgetMinimumSize);
    rightUpPlaceholder->resize(220, 1);
    rightUpPlaceholder->setMinimumSize(220, 1);
    auto TableArea = DockManager->addDockWidget(DockWidgetArea::RightDockWidgetArea, rightUpPlaceholder, CentralDockArea); // 添加到右侧区域

#pragma warning(suppress : 4996)
    rightDownPlaceholder = new CDockWidget("日志记录"); // 创建停靠部件
    rightDownPlaceholder->setWidget(logdialog);
    rightDownPlaceholder->setMinimumSizeHintMode(CDockWidget::MinimumSizeHintFromDockWidgetMinimumSize);
    rightDownPlaceholder->resize(220, 1);
    rightDownPlaceholder->setMinimumSize(220, 1);
    auto TableArea1 = DockManager->addDockWidget(DockWidgetArea::BottomDockWidgetArea, rightDownPlaceholder, TableArea); // 添加到底部区域
    // 加载保存的布局
    loadLayout();

    set_menubar_styleSheet(systemconfigfdata->systemdata.set_skin); // 设置皮肤

    // LV2数据绘图
    qwt_init();


    /***  槽连接     ***/
    connect(ui->action_sensor_status_recode, &QAction::triggered, this, &MainWindow::sensor_status_recode);
    // 历史记录
    connect(ui->action_history_data, &QAction::triggered, this, &MainWindow::slot_history_data);
    connect(ui->action_H, &QAction::triggered, this, &MainWindow::slot_history_data);
    // 打开日志文件
    connect(ui->action_O, &QAction::triggered, this, &MainWindow::slot_open_log_file);
    // 传感器状态
    connect(ui->action_sensor_status, &QAction::triggered, this, &MainWindow::slot_sensor_state);
    connect(ui->action_sensor_status_tool, &QAction::triggered, this, &MainWindow::slot_sensor_state);
    // 日志记录
    connect(ui->action_log, &QAction::triggered, this, &MainWindow::slot_log_record);
    connect(ui->action_log_record, &QAction::triggered, this, &MainWindow::slot_log_record);
    // 基础数据显示
    connect(ui->action_equiupment_test, &QAction::triggered, this, &MainWindow::slot_basic_data_show);
    connect(ui->action_equipment_test, &QAction::triggered, this, &MainWindow::slot_basic_data_show);
    // 截图
    connect(ui->action_cut_image, &QAction::triggered, this, &MainWindow::slot_screenshot);
    connect(ui->action_screenshot, &QAction::triggered, this, &MainWindow::slot_screenshot);
    // 图像左移
    connect(ui->action_leftmove, &QAction::triggered, this, &MainWindow::slot_image_left);
    connect(ui->action_leftmove_tool, &QAction::triggered, this, &MainWindow::slot_image_left);
    // 图像右移
    connect(ui->action_rightmove, &QAction::triggered, this, &MainWindow::slot_image_right);
    connect(ui->action_rightmove_tool, &QAction::triggered, this, &MainWindow::slot_image_right);
    // 观测开关
    connect(ui->action_operation_stop, &QAction::triggered, this, &MainWindow::slot_observe_switch);
    connect(ui->action_start_stop, &QAction::triggered, this, &MainWindow::slot_observe_switch);
    
    // 遥控本控开关
    connect(ui->action_remote_control, &QAction::triggered, this, &MainWindow::slot_telecontrol_switch);
    connect(ui->action_remote_this_control, &QAction::triggered, this, &MainWindow::slot_telecontrol_switch);
    // 自动刷开关
    //connect(ui->action_auto_break, &QAction::triggered, this, &MainWindow::slot_auto_renovate_switch);
    // 风机开关
    connect(ui->action_wind_break, &QAction::triggered, this, &MainWindow::slot_fan_switch);
    // 噪声开关
    connect(ui->action_noise_break, &QAction::triggered, this, &MainWindow::slot_unpitched_sound_switch);
    // 重启按钮
    connect(ui->action_restart, &QAction::triggered, this, &MainWindow::slot_facility_reboot);
    // 廓线开关
    connect(ui->action_profile_image, &QAction::triggered, this, &MainWindow::slot_outline_image);
    // 实时开关
    connect(ui->action_realtime_tool, &QAction::triggered, this, &MainWindow::slot_real_time_switch);

    // 数据存储路径设置
    connect(ui->action_save_data_path, &QAction::triggered, this, [this]()
        {
            emit CMainWindowToSystemConfigurationDialog(0);
            systemconfiguration->setUiParam();
            systemconfiguration->exec();
        });
    // 皮肤设置
    connect(ui->action_skin_set, &QAction::triggered, this, [this]()
        {
            emit CMainWindowToSystemConfigurationDialog(1);
            systemconfiguration->setUiParam();
            systemconfiguration->exec();
        });
    // 站点设置
    connect(ui->action_station_set, &QAction::triggered, this, [this]()
        {
            emit CMainWindowToSystemConfigurationDialog(2);
            systemconfiguration->setUiParam();
            systemconfiguration->exec();
        });
    // 数据管理
    connect(ui->action_data_management, &QAction::triggered, this, [this]()
        {
            emit CMainWindowToSystemConfigurationDialog(3);
            systemconfiguration->setUiParam();
            systemconfiguration->exec();
        });
    // 网络通讯设置
    connect(ui->action_network_set, &QAction::triggered, this, [this]()
        {
            emit CMainWindowToSystemConfigurationDialog(4);
            systemconfiguration->setUiParam();
            systemconfiguration->exec();
        });
    // 视图管理
    connect(ui->action_view_set, &QAction::triggered, this, [this]()
        {
            emit CMainWindowToSystemConfigurationDialog(5);
            systemconfiguration->setUiParam();
            systemconfiguration->exec();
        });
    // 亮温质量管理
    connect(ui->action_bright_temperature, &QAction::triggered, this, [this]()
        {
            emit CMainWindowToSystemConfigurationDialog(6);
            systemconfiguration->setUiParam();
            systemconfiguration->exec();
        });
    // FTP设置
    connect(ui->action_FTP_set, &QAction::triggered, this, [this]()
        {
            emit CMainWindowToSystemConfigurationDialog(7);
            systemconfiguration->setUiParam();
            systemconfiguration->exec();
        });

    // 日常维护
    connect(ui->action_daily_maintenance, &QAction::triggered, this, [this]()
        {
            dailymaintenance->exec();
        });
    // 系统维修
    connect(ui->action_system_maintenance, &QAction::triggered, this, [this]()
        {
            systemmaintenance->exec();
        });
    // 上传监控
    connect(ui->action_upside_monitoring, &QAction::triggered, this, [this]()
        {
            uploadMonitor->exec();
        });
    // 故障管理
    connect(ui->action_breakdown_management, &QAction::triggered, this, [this]()
        {
            breakdown->exec();
        });
    // 用户管理
    connect(ui->action_query_user, &QAction::triggered, this, [this]()
        {
            systemregister->show();
        });
    // 参数设置界面
    connect(ui->action_FMG_parameter, &QAction::triggered, this, [this]()
        {
            //fmgparameter->setUiParam();
            fmgparameter->show();
        });

    // 定标界面
    connect(ui->action_parameter, &QAction::triggered, this, [this]()
        {
            /*setParameter->setUiParam();
            setParameter->exec();*/
            setParameter->show();
        });

    //保存布局
    connect(ui->action_S, &QAction::triggered, this, &MainWindow::slot_saveLayout);

    //ftp文件
    connect(ui->action_Set_FTP, &QAction::triggered, this, [this]()
        {
            ftp->show();
        });
    //帮助链接文档
     //帮助链接文档
    connect(ui->action_about, &QAction::triggered, this, [this]()
        {
                //QString file_path = QFileDialog::getExistingDirectory(nullptr, "选择一个数据路径", "F:/VS2019", QFileDialog::ShowDirsOnly | QFileDialog::DontUseNativeDialog); // 使用qt自带的文件对话框
                //qDebug()<<file_path;

                QDir currentDir = QDir::current();
                // 创建目录（相对路径）
                QString dirpath = "帮助说明文档";
                if (!currentDir.exists(dirpath))
                {
                    if (!currentDir.mkdir(dirpath))
                    {
                        qDebug() << "创建目录失败";
                        return;
                    }
                    qDebug() << "创建目录成功";
                }
                // 构建文件路径（确保目录存在后在操作）
                QFileInfo fileInfo(currentDir, dirpath);
                QString filePath = fileInfo.absoluteFilePath();
                QUrl url = QUrl::fromLocalFile(filePath);

                if (!QDesktopServices::openUrl(url))
                {
                    qWarning() << "无法打开目录:" << filePath;
                }

        });

    handca = new HandCalibrationDialog();

    connect(systemconfiguration, SIGNAL(CSystemConfigurationDialogToHand()), handca, SLOT(slot_hand_data()));

    controller = new ObservationController(systemconfigfdata->systemdata.local_network_ip, systemconfigfdata->systemdata.local_network_port);


    USER_data = new sqlite_USER_data("SqliteData/");
}

MainWindow::~MainWindow()
{
    delete ui;
}
bool MainWindow::eventFilter(QObject* watched, QEvent* event)
{
    if (event->type() == QEvent::Enter && watched->inherits("QCustomPlot"))
    {
        QCustomPlot* plot_event = qobject_cast<QCustomPlot*>(watched);

        if (plot_event == ui->widget_one_view_line)
        {
            tracer_one->setVisible(true);
            tracerLabe_one->setVisible(true);

            plot_event->replot();

        }
        else if (plot_event == ui->widget_two_view_line)
        {
            tracer_two->setVisible(true);
            tracerLabe_two->setVisible(true);

            plot_event->replot();
        }
        else if (plot_event == ui->widget_three_view_line)
        {
            tracer_three->setVisible(true);
            tracerLabe_three->setVisible(true);

            plot_event->replot();
        }

    }
    else if (event->type() == QEvent::Leave && watched->inherits("QCustomPlot"))
    {
        QCustomPlot* plot_event = qobject_cast<QCustomPlot*>(watched);

        if (plot_event == ui->widget_one_view_line)
        {
            tracer_one->setVisible(false);
            tracerLabe_one->setVisible(false);

            plot_event->replot();
        }
        else if (plot_event == ui->widget_two_view_line)
        {
            tracer_two->setVisible(false);
            tracerLabe_two->setVisible(false);

            plot_event->replot();
        }
        else if (plot_event == ui->widget_three_view_line)
        {
            tracer_three->setVisible(false);
            tracerLabe_three->setVisible(false);

            plot_event->replot();
        }

    }

    return QWidget::eventFilter(watched, event);
}
void MainWindow::closeEvent(QCloseEvent* event)
{
    if (Login_validation == false)
    {
        event->accept();
    }
    else if (Login_validation == true)
    {
        QDialog dialog;
        dialog.setWindowTitle("应用程序");
        QLabel* label = new QLabel("是否退出程序!", &dialog);
        QVBoxLayout* mainLayout = new QVBoxLayout(&dialog); // 垂直布局
        mainLayout->addWidget(label);
        // 操作按钮
        QHBoxLayout* buttonLayout = new QHBoxLayout;
        QPushButton* confirmBtn = new QPushButton("确定", &dialog);
        QPushButton* cancelBtn = new QPushButton("取消", &dialog);
        buttonLayout->addWidget(confirmBtn);
        buttonLayout->addWidget(cancelBtn);
        mainLayout->addLayout(buttonLayout);
        // 信号连接
        QObject::connect(confirmBtn, &QPushButton::clicked, &dialog, &QDialog::accept);
        QObject::connect(cancelBtn, &QPushButton::clicked, &dialog, &QDialog::reject);

        if (dialog.exec() == QDialog::Accepted)
        {
            QDialog dialog_user;
            dialog_user.setWindowTitle("登录");
            QVBoxLayout* mainLayout = new QVBoxLayout(&dialog_user); // 垂直布局
            QHBoxLayout* layout_user = new QHBoxLayout;  // 水平布局
            QHBoxLayout* layout_password = new QHBoxLayout; // 水平布局

            QLabel* label_user = new QLabel("用户名", &dialog_user);
            QLabel* label_password = new QLabel("密  码", &dialog_user);
            QLineEdit* lineEdit_user = new QLineEdit(&dialog_user);
            QLineEdit* lineEdit_password = new QLineEdit(&dialog_user);

            lineEdit_user->setPlaceholderText("请输入6~20位用户名");
            lineEdit_password->setPlaceholderText("请输入6~20位密码");
            lineEdit_password->setEchoMode(QLineEdit::Password);


            layout_user->addWidget(label_user);
            layout_user->addWidget(lineEdit_user);
            layout_password->addWidget(label_password);
            layout_password->addWidget(lineEdit_password);

            mainLayout->addLayout(layout_user);
            mainLayout->addLayout(layout_password);
            // 操作按钮
            QHBoxLayout* buttonLayout = new QHBoxLayout;
            QPushButton* confirmBtn_user = new QPushButton("确定", &dialog_user);
            QPushButton* cancelBtn_user = new QPushButton("取消", &dialog_user);
            buttonLayout->addWidget(confirmBtn_user);
            buttonLayout->addWidget(cancelBtn_user);
            mainLayout->addLayout(buttonLayout);
            // 信号连接
            QObject::connect(confirmBtn_user, &QPushButton::clicked, &dialog_user, [&]()
                {
                    QString  user = lineEdit_user->text();
                    QString  password = lineEdit_password->text();
                    QString  select_password;
                    if (user.isEmpty())
                    {
                        QMessageBox::warning(this, "错误", "用户名不能为空！");
                        return;
                    }
                    if (USER_data->select_sqlitedata_user(user))
                    {
                        if (!USER_data->data.isEmpty())
                        {
                            USER_SQ_GROUP user = USER_data->data.first();
                            select_password = user.password;
                        }
                        else
                        {
                            QMessageBox::warning(this, "错误", "用户名不存在！");
                            return;
                        }
                    }
                    else
                    {
                        QMessageBox::warning(this, "错误", "数据库查询失败！");
                        return;
                    }
                    if (password == select_password)
                    {
                        Global::open_qmessage("提示", "用户名密码验证成功，关闭程序！", "确定");
                    }

                    dialog_user.accept();
                });
            QObject::connect(cancelBtn_user, &QPushButton::clicked, &dialog_user, &QDialog::reject);
            if (dialog_user.exec() == QDialog::Accepted)
            {
                event->accept();
            }
            else
            {
                event->ignore();
            }

        }
        else
        {
            event->ignore();
        }
    }

}
void MainWindow::get_lv1_data()//获取lv1数据
{
    emit Signal_CLightDataLv1();
    currentDateTime = QDateTime::currentDateTime();
    // 将时间转换为字符格式
    QString currentDateTimeString = currentDateTime.toString("yyyy-MM-dd hh:mm:ss");
    qDebug() << "当前系统时间:" << currentDateTimeString;

    commNetWork->set_SamCardFiltCoef_read_fun(true);
    // 设置六路接收机温度读取标志位
    commNetWork->set_SixTemp_read_fun(true);
    // 设置LV1电压数据读取标志位
    commNetWork->set_AgvVoltage_read(true);
    // 采集卡温控读取标志位
    commNetWork->set_AdcCardTempCtrl_read_fun(true);
    // 噪声源读取标志位
    commNetWork->set_NoiseSrcSw_read_fun(true);
    // GPS 读取标志位
    commNetWork->set_GPSParam_read_fun(true);
    // 天线角度电机参数读取标志位
    commNetWork->set_AntAngParam_read_fun(true);
    // 常温源平均温度读取标志位
    commNetWork->set_fTempLowAverage_read_fun(true);
    // 气象六要素读取标志位
    commNetWork->set_sixWeather_read_fun(true);
    // 风机参数读取标志位
    commNetWork->set_windTurbParam_read_fun(true);
    // 工作状态读取标志位
    commNetWork->set_workStatus_read_fun(true);
    // 电源状态读取标志位
    commNetWork->set_PowerStatus_read_fun(true);
    // 当前工作模式读取标志位
    commNetWork->set_currWordMode_read_fun(true);
    // 常温源亮温读取标志位
    commNetWork->set_fistTempBt_read_fun(true);


    struct Lv1_Bright_temperature_data temp;
   
    temp.Record = "";

    // 含义：记录日期及时间
    temp.DateTime = currentDateTimeString;
    // 含义：地面温度
    temp.SurTem = g_perWeather.Humidity;
    // 含义：地面湿度
    temp.SurHum = g_perWeather.Temp;

    // 含义：地面气压
    temp.SurPre = g_perWeather.AirPress;

    // 含义：红外温度
    temp.Tir = g_perInfrared.AmbientTemp[0];
    // 含义：是否降水

    temp.Rain = std::numeric_limits<int>::lowest();
    // 含义：质控码
    temp.QCFlag = 9;
    // 含义：方位角
    temp.Azimuth = std::numeric_limits<double>::lowest();
    // 含义：俯仰角
    temp.Elevation = std::numeric_limits<double>::lowest();
    // 含义：频率1观测亮温
    temp.Ch_Freq1 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[0], 0, unpitched_sound_flag);
    // 含义：频率2观测亮温
    temp.Ch_Freq2 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[1], 1, unpitched_sound_flag);
    // 含义：频率n观测亮温
    temp.Ch_Freq3 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[2], 2, unpitched_sound_flag);
    // 含义：频率n观测亮温
    temp.Ch_Freq4 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[3], 3, unpitched_sound_flag);
    // 含义：频率n观测亮温
    temp.Ch_Freq5 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[4], 4, unpitched_sound_flag);
    // 含义：频率n观测亮温
    temp.Ch_Freq6 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[5], 5, unpitched_sound_flag);
    // 含义：频率n观测亮温
    temp.Ch_Freq7 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[6], 6, unpitched_sound_flag);
    // 含义：频率n观测亮温
    temp.Ch_Freq8 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[8], 8, unpitched_sound_flag);
    // 含义：频率n观测亮温
    temp.Ch_Freq9 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[9], 9, unpitched_sound_flag);
    // 含义：频率n观测亮温
    temp.Ch_Freq9 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[10], 10, unpitched_sound_flag);
    // 含义：频率n观测亮温
    temp.Ch_Freq11 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[11], 11, unpitched_sound_flag);
    // 含义：频率n观测亮温
    temp.Ch_Freq12 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[12], 12, unpitched_sound_flag);
    // 含义：频率n观测亮温
    temp.Ch_Freq13 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[13], 13, unpitched_sound_flag);
    // 含义：频率n观测亮温
    temp.Ch_Freq14 = BrightCalcClass::BrightCalc_fun(g_perDataBoard.RadioLV1ValueArray[14], 14, unpitched_sound_flag);

    temp.QCFlag_BT = "99999";

    struct Lv_Basic_parameters temp1; /*= {"MWR",9.44,"54511",116.2813,39.4826,32,"AD002",14,83};*/
    double longitude_angle = systemconfigfdata->systemdata.longitude_angle.toDouble();
    double longitude_minute = systemconfigfdata->systemdata.longitude_minute.toDouble();
    double longitude_second = systemconfigfdata->systemdata.longitude_second.toDouble();
    double longitude1 = longitude_angle + longitude_minute / 60 + longitude_second / 3600;
    double latitude_angle = systemconfigfdata->systemdata.latitude_angle.toDouble();
    double latitude_minute = systemconfigfdata->systemdata.latitude_minute.toDouble();
    double latitude_second = systemconfigfdata->systemdata.latitude_second.toDouble();
    double latitude1 = latitude_angle + latitude_minute / 60 + latitude_second / 3600;
    temp1.MWR = "MWR";
    temp1.Data_format_version_number = 9.44;
    temp1.Station_number = systemconfigfdata->systemdata.station_number;
    temp1.longitude = longitude1;
    temp1.dimension = latitude1;
    temp1.altitude = systemconfigfdata->systemdata.ground_hieght;
    temp1.Equipment_type = systemconfigfdata->systemdata.business_number;
    temp1.Base_theory = 14;
    temp1.Product_data = 83;

    lv1_Historical_data(&temp);

    if (systemconfigfdata->systemdata.logic == true)//调用函数
    {
        lv1_n1_fun(&temp);
        qDebug() << "checkBox_logic true";
    }
    else if (systemconfigfdata->systemdata.min_bianlv == true)
    {
        lv1_n2_fun(&temp);
        qDebug() << "checkBox_min_bianlv true";
    }
    else if (systemconfigfdata->systemdata.water == true)
    {
        lv1_n3_fun(&temp, systemconfigfdata->systemdata.rainfall_time.toInt());
        qDebug() << "checkBox_water true";
    }
    else if (systemconfigfdata->systemdata.gender == true)
    {
        lv1_n4_fun(&temp);
        qDebug() << "checkBox_gender true";
    }
    else if (systemconfigfdata->systemdata.peak == true)
    {
        lv1_n5_fun(&temp, systemconfigfdata->systemdata.min_value.toDouble(), systemconfigfdata->systemdata.max_value.toDouble());
        qDebug() << "checkBox_peak true";
    }
    set_QCFlag(&temp);



    if (systemconfigfdata->systemdata.lv1_file_format == 0)//日文件
    {
        /*struct Lv_Basic_parameters basic_parameters = { "MWR",9.44,"54511",116.2813,39.4826,32,"AD002",14,83 };
        struct Lv1_Bright_temperature_data bright_temperature = { "1","2021-09-03 00:00:00",23.01,71.90,1003.10,-24.67,0,0,0.000,90.080,65.311,62.917,54.780,39.334,35.004,29.992,27.169,118.226,158.658,261.277,289.738,294.388,294.563,294.902,"00000" };*/
        lv1_data_storage->Generate_new_Lv1_Bright_temperature_data(temp1, temp);
    }
    else if (systemconfigfdata->systemdata.lv1_file_format == 1)//分钟文件
    {
        /*struct Lv_Basic_parameters basic_parameters = { "MWR",9.44,"54511",116.2813,39.4826,32,"AD002",14,83 };
        struct Lv1_Bright_temperature_data bright_temperature = { "1","2021-09-03 00:00:00",23.01,71.90,1003.10,-24.67,0,0,0.000,90.080,65.311,62.917,54.780,39.334,35.004,29.992,27.169,118.226,158.658,261.277,289.738,294.388,294.563,294.902,"00000" };*/
        lv1_data_storage->Generate_new_Lv1_Minutes_file(temp1, temp);
    }

    //lv1->lv2
    QVector<struct Lv2_Meteorological_product_data> lv2data;
    for (int i = 0; i < 3; i++)
    {
        struct Lv2_Meteorological_product_data lv2data_temp;
        lv2data_temp.Record = temp.Record;
        lv2data_temp.DateTime = temp.DateTime;
        lv2data_temp.SurTem = temp.SurTem;
        lv2data_temp.SurHum = temp.SurHum;
        lv2data_temp.SurPre = temp.SurPre;
        lv2data_temp.Tir = temp.Tir;
        lv2data_temp.Rain = temp.Rain;
        lv2data_temp.CloudBase = std::numeric_limits<double>::lowest();
        lv2data_temp.Vint = std::numeric_limits<double>::lowest();
        lv2data_temp.Lqint = std::numeric_limits<double>::lowest();
        lv2data_temp.QCflag = temp.QCFlag;
        lv2data.append(lv2data_temp);
    }
       
    QVector<double> ch_1_14_temp = { temp.Ch_Freq1,temp.Ch_Freq2,temp.Ch_Freq3,temp.Ch_Freq4,temp.Ch_Freq5,temp.Ch_Freq6,temp.Ch_Freq7,
                                temp.Ch_Freq8,temp.Ch_Freq9,temp.Ch_Freq10,temp.Ch_Freq11,temp.Ch_Freq12,temp.Ch_Freq13,temp.Ch_Freq14 };
#pragma warning(suppress : 4996)
    std::vector ch_1_14 = ch_1_14_temp.toStdVector();//14通道数据
    //temp.SurHum湿度13   temp.SurPre气压12   temp.SurTem温度11
    //算法部分代码
    ProfileInversionModel::StructRecvBusinessData data;
    data.channelBrightnessTemperature = ch_1_14;// 14通道亮温
    data.groundStationTemperature = (int16_t)(temp.SurTem * 100.0);  // 地面温度
    data.groundStationRelativeHumidity = (uint16_t)(temp.SurHum * 100.0); // 地面相对湿度
    data.groundStationPressure = (uint32_t)(temp.SurPre * 100.0);//地面气压
    vector<ProfileInversionModel::StructRecvBusinessData> inputList = { data };

    ProfileInversionModel::RelativeHumidityProfile processor;  // 相对湿度
    auto results1 = processor.readDate(inputList);   // 反演廓线

    ProfileInversionModel::TemperatureProfile sadas;
    auto results2 = sadas.readDate(inputList);   // 反演廓线

    ProfileInversionModel::WaterVaporDensityProfile WaterVaporDensity;
    auto results3 = WaterVaporDensity.readDate(inputList);   // 反演廓线

    //温度数据
    std::vector<double> ch83_SurTem = results2[0];
    if (ch83_SurTem.size() > 83)
    {
        for (int i = 0; i < 83; i++)
            lv2data[0].H[i] = ch83_SurTem[i];
    }
    else
    {
        for (int i = 0; i < ch83_SurTem.size(); i++)
            lv2data[0].H[i] = ch83_SurTem[i];
    }
    lv2data[0].DataType = 11;
    //水汽数据
    std::vector<double> ch83_SurPre = results3[0];
    if (ch83_SurPre.size() > 83)
    {
        for (int i = 0; i < 83; i++)
            lv2data[1].H[i] = ch83_SurPre[i];
    }
    else
    {
        for (int i = 0; i < ch83_SurPre.size(); i++)
            lv2data[1].H[i] = ch83_SurPre[i];
    }
    lv2data[1].DataType = 12;
    //湿度数据
    std::vector<double> ch83_SurHum = results1[0];
    if (ch83_SurHum.size() > 83)
    {
        for (int i = 0; i < 83; i++)
            lv2data[2].H[i] = ch83_SurHum[i];
    }
    else
    {
        for (int i = 0; i < ch83_SurHum.size(); i++)
            lv2data[2].H[i] = ch83_SurHum[i];
    }
    lv2data[2].DataType = 13;

    if (systemconfigfdata->systemdata.lv2_file_format == 0)//日文件
    {
        lv2_data_storage->Generate_new_Lv2_Meteorological_product_data(temp1, lv2data);
    }
    else if (systemconfigfdata->systemdata.lv2_file_format == 1)//分钟文件
    {
        lv2_data_storage->Generate_new_Lv2_Minutes_file(temp1, lv2data);
    }


    /*History_file_Path(systemconfigfdata->systemdata.LV2_path);
    History_file_Path_lv1(systemconfigfdata->systemdata.LV1_path);*/

    int erroNum=0;

    cardTempC_struct Ktemp =  commNetWork->get_cardTempC_fun(0);
    cardTempC_struct Vtemp = commNetWork->get_cardTempC_fun(1);
    //常温源温度
    LowTemp  black_temp;
    black_temp = commNetWork->get_fTempLow_fun();
    WeathersixEles_struct weathersix = commNetWork->get_WeathersixEles_fun();
    windTurbParam_struct fan;
    //读取外设工作状态
    workStatus_struct work;
    work = commNetWork->get_workStatus_fun();
    //读取外设电源状态
    PowerStatus_struct power;
    power = commNetWork->get_PowerStatus_fun();
    struct Device_status_data basic_parameters; /*= { 1,"time",1,0,0,0,0,273.15,273.14,0,0,0,0,273.15,273.16,273.17,273.18,20,80,1024,0,0,1,0,0,0 };*/
    basic_parameters.Record = 1;//记录序号
    basic_parameters.DateTime = currentDateTimeString;

    
    basic_parameters.EServo = -1;//默认 -1 俯仰转台
    basic_parameters.AServo = -1;//默认 -1 方位转台
    if (work.colCardStat==0)
    {
        basic_parameters.RCV0 = 0;//水汽观测接收机
        basic_parameters.RCV1 = 0;//温度观测接收机
    }
    else
    {
        basic_parameters.RCV0 = 1;//水汽观测接收机
        basic_parameters.RCV1 = 1;//温度观测接收机
        erroNum++;
    }


    basic_parameters.TRec1 = Ktemp.currTemp+273;//水汽通道接收机温度
    basic_parameters.TRec2 = Vtemp.currTemp+273;//氧气通道接收机温度

    if (Ktemp.currTemp<= Ktemp.targetTemp+0.3&& Ktemp.currTemp>=Ktemp.targetTemp-0.3)
    {
        basic_parameters.SRec1 = 0;//水汽通道接收机热稳定性
    }
    else
    {
        basic_parameters.SRec1 = 1;//水汽通道接收机热稳定性
        erroNum++;
    }
    if (Vtemp.currTemp <= Vtemp.targetTemp + 0.3 && Vtemp.currTemp >= Vtemp.targetTemp - 0.3)
    {
        basic_parameters.SRec1 = 0;//氧气通道接收机热稳定性
    }
    else
    {
        basic_parameters.SRec1 = 1;//氧气通道接收机热稳定性
        erroNum++;
    }
   
    basic_parameters.LO = -1;//默认 -1
    basic_parameters.BIB = 0;//默认 -0
    basic_parameters.TAmb1 = black_temp.LowTemp1 + 273;//内置黑体温度
    basic_parameters.TAmb2 = black_temp.LowTemp2 + 273;//内置黑体温度
    basic_parameters.TAmb3 = black_temp.LowTemp3 + 273;//内置黑体温度
    basic_parameters.TAmb4 = black_temp.LowTemp4 + 273;//内置黑体温度
    basic_parameters.SurTem = weathersix.Temp;//地面温度
    basic_parameters.SurHum = weathersix.Humidity;//地面湿度
    basic_parameters.SurPre = weathersix.AirPress;//地面气压
    basic_parameters.Rain = weathersix.Rain10Minute;//降雨
    basic_parameters.Tir = -1;//默认 -1 测云组件
    basic_parameters.TimeSync = 0;//默认 0  时间同步组件
    basic_parameters.ECM = 0;//默认 0  防雨雾控制组件
    if (power.colCardStat && power.antMotStat && power.metSixElemsStat)
    {
        basic_parameters.ExPower = 0;//外接电源
    }
    else
    {
        basic_parameters.ExPower = 1;
        erroNum++;
    }
    if (commNetWork->facility_connect_status == true)
    {
        basic_parameters.Communication = 0;
    }
    else
    {
        basic_parameters.Communication = 1;
        erroNum++;
    }
    if (erroNum!=0)
    {
        basic_parameters.General = 1;//总状态
    }
    else
    {
        basic_parameters.General = 0;//总状态
    }
   
    
    if (systemconfigfdata->systemdata.status_data_file_format == 0)
    {

        device_status_data_storage->Generate_new_Device_status_data(basic_parameters);
    }
    else if (systemconfigfdata->systemdata.status_data_file_format == 1)
    {
        device_status_data_storage->Generate_new_Device_status_Minutes_file(basic_parameters);

    }
    if (systemconfigfdata->systemdata.status_data_file_format_xml == 0)
    {
        device_status_data_storage->set_save_mode(false);
    }
    else if (systemconfigfdata->systemdata.status_data_file_format_xml == 1)
    {
        device_status_data_storage->set_save_mode(true);
    }
     
   
   
    
   
    

    



}
void MainWindow::History_file_Path(QString hispath)
{
    save_path = hispath;
    //m_pool = new ThreadPool(this);
    m_scanner = new DirectoryScanner(m_pool, this);
    m_rootItem = new QTreeWidgetItem();
    connect(m_scanner, &DirectoryScanner::matchedFileFound, this, &MainWindow::onFileFound);
    historydialog->get_QTreeWidget()->setHorizontalScrollMode(QAbstractItemView::ScrollPerPixel);
    historydialog->get_QTreeWidget()->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    // 表头配置

    QHeaderView* header = historydialog->get_QTreeWidget()->header();
    header->setSectionResizeMode(QHeaderView::ResizeToContents);
    header->setStretchLastSection(false);

    historydialog->get_QTreeWidget()->clear();
    m_rootItem = new QTreeWidgetItem();
    m_rootItem->setText(0, QDir::toNativeSeparators(hispath));
    // m_rootItem->setText(0, QDir::toNativeSeparators(log_currentFilePath));
    historydialog->get_QTreeWidget()->addTopLevelItem(m_rootItem);

    // 气象产品数据文件正则表达式(匹配实际样例格式)
    QString pattern = R"(^Z_UPAR_I_\d{5}_\d{14}_P_YMWR_[A-Z0-9]{5}_CP_[MD]\.txt$)";
    qDebug() << "Using pattern:" << pattern;
    m_scanner->scan(hispath, pattern);

    // 动态调整
    connect(historydialog->get_QTreeWidget(), &QTreeWidget::itemExpanded, [=]
        { historydialog->get_QTreeWidget()->resizeColumnToContents(0); });
    connect(historydialog->get_QTreeWidget(), &QTreeWidget::itemDoubleClicked, [=](QTreeWidgetItem* item, int column)
        {
            open_lv1_lv2 = 2;
            // 获取存储的完整路径（需在填充数据时通过 setData 设置）
            QString filePath = item->data(0, Qt::UserRole).toString();

            // 若未直接存储路径，需拼接父节点路径（适用于动态构建的树）
            if (filePath.isEmpty()) {
                QList<QString> parts;
                QTreeWidgetItem* current = item;
                while (current) {
                    parts.prepend(current->text(0));
                    current = current->parent();
                }
                filePath = QDir::toNativeSeparators(parts.join('/')); // 转换为系统路径格式（网页2）[2](@ref)
            }

            qDebug() << "双击文件路径:" << filePath;
            // 验证绘图器实例
            if (!d_plot || !d_plot2 || !d_plot3 || !d_plot4) {
                qWarning() << "Plotter instances not initialized!";
                return;
            }

            // 操作成员变量
            cur_filePath = filePath;
            d_plot->open_file(filePath);
            d_plot2->open_file(filePath);
            d_plot3->open_file(filePath);
            d_plot4->open_file(filePath);
            cur_mode = 1; });
}

void MainWindow::onFileFound(const QString& filePath)
{
    // 标准化路径处理
    QString normalizedSavePath = QDir(save_path).absolutePath();
    QString normalizedFilePath = QDir(filePath).absolutePath();

    // 验证路径是否在 save_path 下
    if (!normalizedFilePath.startsWith(normalizedSavePath + "/") &&
        normalizedFilePath != normalizedSavePath)
    {
        qDebug() << "路径不在保存目录下:" << normalizedFilePath;
        return;
    }

    // 计算需要跳过的路径段数
    QStringList baseParts = QDir::toNativeSeparators(normalizedSavePath)
        .split(QDir::separator(), Qt::SkipEmptyParts);
    QStringList fileParts = QDir::toNativeSeparators(normalizedFilePath)
        .split(QDir::separator(), Qt::SkipEmptyParts);

    // 确保 filePath 在 save_path 下
    if (fileParts.mid(0, baseParts.size()) != baseParts)
    {
        qWarning() << "路径不匹配基目录";
        return;
    }

    // 需要处理的剩余部分
    QStringList remainingParts = fileParts.mid(baseParts.size());

    QTreeWidgetItem* parent = m_rootItem;
    QString currentPath = normalizedSavePath; // 从基路径开始追踪

    for (const QString& part : remainingParts)
    {
        currentPath += QDir::separator() + part;

        bool found = false;
        // 遍历现有子节点
        for (int i = 0; i < parent->childCount(); ++i)
        {
            QTreeWidgetItem* child = parent->child(i);
            if (child->text(0) == part &&
                child->data(0, Qt::UserRole).toString() == currentPath)
            {
                parent = child;
                found = true;
                break;
            }
        }

        if (!found)
        {
            QTreeWidgetItem* item = new QTreeWidgetItem(parent);
            item->setText(0, part);
            item->setData(0, Qt::UserRole, currentPath);

            // 设置图标
            QFileInfo fileInfo(currentPath);
            QIcon icon = fileInfo.isDir() ? style()->standardIcon(QStyle::SP_DirIcon) : style()->standardIcon(QStyle::SP_FileIcon);
            item->setIcon(0, icon);

            // 设置展开策略
            item->setChildIndicatorPolicy(
                fileInfo.isDir() ? QTreeWidgetItem::ShowIndicator
                : QTreeWidgetItem::DontShowIndicator);

            parent = item;
            historydialog->get_QTreeWidget()->expandItem(parent);
        }
    }
}

void MainWindow::History_file_Path_lv1(QString hispath)
{
    save_path1 = hispath;
    //lv1数据
    m_scanner1 = new DirectoryScanner(m_pool, this);
    connect(m_scanner1, &DirectoryScanner::matchedFileFound, this, &MainWindow::onFileFound1);
    historydialog->get_QTreeWidget_lv1()->setHorizontalScrollMode(QAbstractItemView::ScrollPerPixel);
    historydialog->get_QTreeWidget_lv1()->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    // 表头配置

    QHeaderView* header1 = historydialog->get_QTreeWidget_lv1()->header();
    header1->setSectionResizeMode(QHeaderView::ResizeToContents);
    header1->setStretchLastSection(false);

    historydialog->get_QTreeWidget_lv1()->clear();
    m_rootItem1 = new QTreeWidgetItem();
    m_rootItem1->setText(0, QDir::toNativeSeparators(hispath));

    historydialog->get_QTreeWidget_lv1()->addTopLevelItem(m_rootItem1);
    QString pattern1 = R"(^Z_UPAR_I_\d{5}_\d{14}_O_YMWR_[A-Z0-9]{5}_RAW_[MD]\.txt$)";
    qDebug() << "Using pattern:" << pattern1;
    m_scanner1->scan(hispath, pattern1);

    // 动态调整
    connect(historydialog->get_QTreeWidget_lv1(), &QTreeWidget::itemExpanded, [=]
        { historydialog->get_QTreeWidget_lv1()->resizeColumnToContents(0); });
    connect(historydialog->get_QTreeWidget_lv1(), &QTreeWidget::itemDoubleClicked, [=](QTreeWidgetItem* item, int column)
        {
            open_lv1_lv2 = 1;
            // 获取存储的完整路径（需在填充数据时通过 setData 设置）
            QString  filePath = item->data(0, Qt::UserRole).toString();

            // 若未直接存储路径，需拼接父节点路径（适用于动态构建的树）
            if (filePath.isEmpty()) {
                QList<QString> parts;
                QTreeWidgetItem* current = item;
                while (current) {
                    parts.prepend(current->text(0));
                    current = current->parent();
                }
                filePath = QDir::toNativeSeparators(parts.join('/')); // 转换为系统路径格式（网页2）[2](@ref)

            }
            // 操作成员变量
            /*cur_filePath = filePath;
            d_plot->open_file(filePath);
            d_plot2->open_file(filePath);
            d_plot3->open_file(filePath);
            d_plot4->open_file(filePath);
            cur_mode = 1;*/ 
        
            for (int i = 0; i < 4; i++)
            {
                timerVector[i].clear();
                table_header[i].clear();
                temperatureProfilees[i].clear();
                humidityProfilees[i].clear();
                vaporProfilees[i].clear();

            }
            
            QVector<double> h_table_header = { 0,0.025,0.05,0.075,0.1,0.125,0.15,0.175,0.2,0.225,0.25,0.275,0.3,0.325,0.35,0.375,0.4,0.425,0.45,0.475,0.5,0.55,0.6,0.65,0.7,0.75,0.8,0.85,0.9,0.95,1,1.05,1.1,1.15,1.2,1.25,1.3,1.35,1.4,1.45,1.5,1.55,1.6,1.65,1.7,1.75,1.8,1.85,1.9,1.95,2,2.25,2.5,2.75,3,3.25,3.5,3.75,4,4.25,4.5,4.75,5,5.25,5.5,5.75,6,6.25,6.5,6.75,7,7.25,7.5,7.75,8,8.25,8.5,8.75,9,9.25,9.5,9.75,10 };
            table_header[0].append(h_table_header);
            table_header[1].append(h_table_header);
            table_header[2].append(h_table_header);
            table_header[3].append(h_table_header);

            vector<ProfileInversionModel::StructRecvBusinessData> inputList;

            QFile file(filePath);
            if (!file.open(QFile::ReadOnly | QIODevice::Text)) {
                QMessageBox::warning(this, "警告", "打开文件失败!");
                return;
            }
            QTextStream in(&file);
            int lineCount = 0;

            while (!in.atEnd())
            {
                QString line = in.readLine(); // 读取每行数据
                lineCount++;
                //跳过前三行数据
                if (lineCount <= 3) continue;

                QStringList fields = line.split(","); // 以逗号分割数据
                //确保有足够的列数
                if (fields.size() < 20) continue;

                ProfileInversionModel::StructRecvBusinessData data;

                QDateTime time = QDateTime::fromString(fields[1], "yyyy-MM-dd hh:mm:ss");

                if (!time.isValid()) continue;

                timerVector[0].append(time);
                timerVector[1].append(time);
                timerVector[2].append(time);
                timerVector[3].append(time);

                //解析环境数据
                QVector<double> envParamas;

                envParamas << fields[2].toDouble();
                envParamas << fields[3].toDouble();
                envParamas << fields[4].toDouble();

                double surTem = fields[2].toDouble();
                double surHum = fields[3].toDouble();
                double surPre = fields[4].toDouble();
                data.groundStationTemperature = (int16_t)(surTem * 100.0);  // 地面温度
                data.groundStationRelativeHumidity = (uint16_t)(surHum * 100.0); // 地面相对湿度
                data.groundStationPressure = (uint32_t)(surPre * 100.0);//地面气压

                envParamaVector.append(envParamas);


                QVector<double> channels;
                for (int i = 10; i <= 24; ++i)
                {
                    channels.append(fields[i].toDouble());
                }
#pragma warning(suppress : 4996)
                data.channelBrightnessTemperature = channels.toStdVector();// 14通道亮温
                inputList.push_back(data);
                channelsVector.append(channels);

            }
            file.close();


            vector<vector<double>> results1;
            ProfileInversionModel::RelativeHumidityProfile processor;  // 相对湿度
            for each (ProfileInversionModel::StructRecvBusinessData var in inputList)
            {
                results1.push_back(processor.readDate(var));
            }
            vector<vector<double>> results2;
            ProfileInversionModel::TemperatureProfile sadas;
            for each (ProfileInversionModel::StructRecvBusinessData var in inputList)
            {
                results2.push_back(sadas.readDate(var));
            }

            vector<vector<double>> results3;
            ProfileInversionModel::WaterVaporDensityProfile WaterVaporDensity;
            for each (ProfileInversionModel::StructRecvBusinessData var in inputList)
            {
                results3.push_back(WaterVaporDensity.readDate(var));
            }
            //auto results1 = processor.readDate(inputList);   // 反演廓线

            //ProfileInversionModel::TemperatureProfile sadas;
            //auto results2 = sadas.readDate(inputList);   // 反演廓线

            //ProfileInversionModel::WaterVaporDensityProfile WaterVaporDensity;
            //auto results3 = WaterVaporDensity.readDate(inputList);   // 反演廓线
           
            int k = 0;
            for (const auto& profile : results3)
            {
                QVector<double> temp;
                for (double val : profile)
                {
                    temp.append(val);
                }
                vaporProfilees[0].append(temp);
                vaporProfilees[1].append(temp);
                vaporProfilees[2].append(temp);
                vaporProfilees[3].append(temp);
               /* if (d_plot2->title().text() == "温度")
                {
                    d_plot2->addRealtimeData(0, timerVector[0][k], table_header[0], temperatureProfilees[0][k]);
                }
                else if (d_plot2->title().text() == "水汽")
                {
                    d_plot2->addRealtimeData(0, timerVector[0][k], table_header[0], vaporProfilees[0][k]);
                }
                else if (d_plot2->title().text() == "湿度")
                {
                    d_plot2->addRealtimeData(0, timerVector[0][k], table_header[0], humidityProfilees[0][k]);
                }
                else if (d_plot2->title().text() == "温度_湿度")
                {
                    d_plot2->addRealtimeData(0, timerVector[0][k], table_header[0], temperatureProfilees[0][k]);
                    d_plot2->addRealtimeData(1, timerVector[0][k], table_header[0], humidityProfilees[0][k]);
                }*/
                d_plot2->addRealtimeData(0, timerVector[0][k], table_header[0], vaporProfilees[0][k]);
                k++;
            }
            int j = 0;
            for (const auto& profile : results1)
            {
                QVector<double> temp;
                for (double val : profile)
                {
                    temp.append(val);
                }
                humidityProfilees[0].append(temp);
                humidityProfilees[1].append(temp);
                humidityProfilees[2].append(temp);
                humidityProfilees[3].append(temp);
                d_plot3->addRealtimeData(0, timerVector[0][j], table_header[0], humidityProfilees[0][j]);
                j++;
            }
            int i =0 ;
            for (const auto& profile : results2)
            {
                QVector<double> temp;
                for (double val : profile)
                {
                    temp.append(val);
                }
                temperatureProfilees[0].append(temp);
                temperatureProfilees[1].append(temp);
                temperatureProfilees[2].append(temp);
                temperatureProfilees[3].append(temp);
                d_plot->addRealtimeData(0, timerVector[0][i], table_header[0], temperatureProfilees[0][i]);


                d_plot4->addRealtimeData(0, timerVector[0][i], table_header[0], temperatureProfilees[0][i]);
                d_plot4->addRealtimeData(1, timerVector[0][i], table_header[0], humidityProfilees[0][i]);
                i++;
            }
           
            cur_mode = 2;
           
            
            
        });
}

void MainWindow::onFileFound1(const QString& filePath)
{
    // 标准化路径处理
    QString normalizedSavePath = QDir(save_path1).absolutePath();
    QString normalizedFilePath = QDir(filePath).absolutePath();

    // 验证路径是否在 save_path 下
    if (!normalizedFilePath.startsWith(normalizedSavePath + "/") &&
        normalizedFilePath != normalizedSavePath)
    {
        qDebug() << "路径不在保存目录下:" << normalizedFilePath;
        return;
    }

    // 计算需要跳过的路径段数
    QStringList baseParts = QDir::toNativeSeparators(normalizedSavePath)
        .split(QDir::separator(), Qt::SkipEmptyParts);
    QStringList fileParts = QDir::toNativeSeparators(normalizedFilePath)
        .split(QDir::separator(), Qt::SkipEmptyParts);

    // 确保 filePath 在 save_path 下
    if (fileParts.mid(0, baseParts.size()) != baseParts)
    {
        qWarning() << "路径不匹配基目录";
        return;
    }

    // 需要处理的剩余部分
    QStringList remainingParts = fileParts.mid(baseParts.size());

    QTreeWidgetItem* parent = m_rootItem1;
    QString currentPath = normalizedSavePath; // 从基路径开始追踪

    for (const QString& part : remainingParts)
    {
        currentPath += QDir::separator() + part;

        bool found = false;
        // 遍历现有子节点
        for (int i = 0; i < parent->childCount(); ++i)
        {
            QTreeWidgetItem* child = parent->child(i);
            if (child->text(0) == part &&
                child->data(0, Qt::UserRole).toString() == currentPath)
            {
                parent = child;
                found = true;
                break;
            }
        }

        if (!found)
        {
            QTreeWidgetItem* item = new QTreeWidgetItem(parent);
            item->setText(0, part);
            item->setData(0, Qt::UserRole, currentPath);

            // 设置图标
            QFileInfo fileInfo(currentPath);
            QIcon icon = fileInfo.isDir() ? style()->standardIcon(QStyle::SP_DirIcon) : style()->standardIcon(QStyle::SP_FileIcon);
            item->setIcon(0, icon);

            // 设置展开策略
            item->setChildIndicatorPolicy(
                fileInfo.isDir() ? QTreeWidgetItem::ShowIndicator
                : QTreeWidgetItem::DontShowIndicator);

            parent = item;
            historydialog->get_QTreeWidget_lv1()->expandItem(parent);
        }
    }
}

void MainWindow::onOPtion_view(QAction* option)
{
    if (option->text() == "单视图")
    {
        index_view = "单视图";
        
        if (profile_image == false)
        {
            ui->widget_one_view->show();
            ui->widget_two_view->hide();
            ui->widget_three_view->hide();
            ui->widget_four_view->hide();
        }
        else
        {
            ui->widget_one_view_line->show();
            ui->widget_two_view_line->hide();
            ui->widget_three_view_line->hide();
            ui->widget_two_view->hide();
            ui->widget_three_view->hide();
            ui->widget_four_view->hide();
        }
        outline_flag = 1;
    }
    else if (option->text() == "双视图")
    {
        index_view = "双视图";
       
        if (profile_image == false)
        {
            ui->widget_one_view->show();
            ui->widget_two_view->show();
            ui->widget_three_view->hide();
            ui->widget_four_view->hide();
        }
        else
        {
            ui->widget_one_view_line->show();
            ui->widget_two_view_line->show();
            ui->widget_three_view_line->hide();
            ui->widget_one_view->show();
            ui->widget_two_view->show();
            ui->widget_three_view->hide();
            ui->widget_four_view->hide();
        }
        outline_flag = 2;
    }
    else if (option->text() == "三视图")
    {
        index_view = "三视图";
        
        if (profile_image == false)
        {
            ui->widget_one_view->show();
            ui->widget_two_view->show();
            ui->widget_three_view->show();
            ui->widget_four_view->hide();
        }
        else
        {
            ui->widget_one_view_line->show();
            ui->widget_two_view_line->show();
            ui->widget_three_view_line->show();
            ui->widget_one_view->show();
            ui->widget_two_view->show();
            ui->widget_three_view->show();
            ui->widget_four_view->hide();
        }
        outline_flag = 3;
    }
    else if (option->text() == "四视图")
    {
        index_view = "四视图";
        
        ui->widget_one_view->show();
        ui->widget_two_view->show();
        ui->widget_three_view->show();
        ui->widget_four_view->show();
        outline_flag = 3;
    }
    outline_image(outline_image_flag);
}

void MainWindow::onOPtion_dis(QAction* option)
{
    if (option->text() == "2千米")
    {
        d_plot->set_y_rang(0, 2, (2 / 10));
        d_plot2->set_y_rang(0, 2, (2 / 10));
        d_plot3->set_y_rang(0, 2, (2 / 10));
        d_plot4->set_y_rang(0, 2, (2 / 10));
    }
    else if (option->text() == "4千米")
    {
        d_plot->set_y_rang(0, 4, (4 / 10));
        d_plot2->set_y_rang(0, 4, (4 / 10));
        d_plot3->set_y_rang(0, 4, (4 / 10));
        d_plot4->set_y_rang(0, 4, (4 / 10));
    }
    else if (option->text() == "6千米")
    {
        d_plot->set_y_rang(0, 6, (6 / 10));
        d_plot2->set_y_rang(0, 6, (6 / 10));
        d_plot3->set_y_rang(0, 6, (6 / 10));
        d_plot4->set_y_rang(0, 6, (6 / 10));
    }
    else if (option->text() == "8千米")
    {
        d_plot->set_y_rang(0, 8, (8 / 10));
        d_plot2->set_y_rang(0, 8, (8 / 10));
        d_plot3->set_y_rang(0, 8, (8 / 10));
        d_plot4->set_y_rang(0, 8, (8 / 10));
    }
    else if (option->text() == "10千米")
    {
        d_plot->set_y_rang(0, 10, (10 / 10));
        d_plot2->set_y_rang(0, 10, (10 / 10));
        d_plot3->set_y_rang(0, 10, (10 / 10));
        d_plot4->set_y_rang(0, 10, (10 / 10));
    }
}

void MainWindow::onOPtion_time(QAction* option)
{
    if (option->text() == "1小时")
    {
        d_plot->set_x_rang(0, 3600, (3600 / 12));
        d_plot2->set_x_rang(0, 3600, (3600 / 12));
        d_plot3->set_x_rang(0, 3600, (3600 / 12));
        d_plot4->set_x_rang(0, 3600, (3600 / 12));
    }
    else if (option->text() == "4小时")
    {
        d_plot->set_x_rang(0, 14400, (14400 / 12));
        d_plot2->set_x_rang(0, 14400, (14400 / 12));
        d_plot3->set_x_rang(0, 14400, (14400 / 12));
        d_plot4->set_x_rang(0, 14400, (14400 / 12));
    }
    else if (option->text() == "8小时")
    {
        d_plot->set_x_rang(0, 28800, (28800 / 12));
        d_plot2->set_x_rang(0, 28800, (28800 / 12));
        d_plot3->set_x_rang(0, 28800, (28800 / 12));
        d_plot4->set_x_rang(0, 28800, (28800 / 12));
    }
    else if (option->text() == "12小时")
    {
        d_plot->set_x_rang(0, 43200, (43200 / 12));
        d_plot2->set_x_rang(0, 43200, (43200 / 12));
        d_plot3->set_x_rang(0, 43200, (43200 / 12));
        d_plot4->set_x_rang(0, 43200, (43200 / 12));
    }
    else if (option->text() == "16小时")
    {
        d_plot->set_x_rang(0, 57600, (57600 / 12));
        d_plot2->set_x_rang(0, 57600, (57600 / 12));
        d_plot3->set_x_rang(0, 57600, (57600 / 12));
        d_plot4->set_x_rang(0, 57600, (57600 / 12));
    }
    else if (option->text() == "20小时")
    {
        d_plot->set_x_rang(0, 72000, (72000 / 12));
        d_plot2->set_x_rang(0, 72000, (72000 / 12));
        d_plot3->set_x_rang(0, 72000, (72000 / 12));
        d_plot4->set_x_rang(0, 72000, (72000 / 12));
    }
    else if (option->text() == "24小时")
    {
        d_plot->set_x_rang(0, 86400, (86400 / 12));
        d_plot2->set_x_rang(0, 86400, (86400 / 12));
        d_plot3->set_x_rang(0, 86400, (86400 / 12));
        d_plot4->set_x_rang(0, 86400, (86400 / 12));
    }
}
void MainWindow::sensor_status_recode()
{
    sensorStatusRecord->exec();
}

void MainWindow::slot_history_data()
{
    if (!hisdialog_view)
    {
        // 隐藏历史数据
        hisdialog_view = true;
        leftPlace->toggleView(true); // 该属性控制dock小部件是开放还是关闭。
        emit set_logRecord_color_signal("一般信息:开启历史记录查看功能");
    }
    else
    {
        // 显示界面
        if (leftPlace->isClosed())
        {
            leftPlace->toggleView(true);
            hisdialog_view = true;
        }
        else
        {
            leftPlace->toggleView(false);
            hisdialog_view = false;
            emit set_logRecord_color_signal("一般信息:关闭历史记录查看功能");
        }

    }
        
}

void MainWindow::slot_open_log_file()
{
    // 获取当前的系统时间
    QDateTime currentDate = QDateTime::currentDateTime();
    // 将时间转换为字符格式
    QString timestamp = currentDate.toString("yyyyMMdd");
    QString fileName = QString("/日志文件/日志_%1.txt").arg(timestamp);
    QString datafilename = systemconfigfdata->systemdata.save_log_path + fileName;

    QFile logFile(datafilename);
    if (!logFile.exists())
    {
        if (!logFile.open(QIODevice::WriteOnly | QIODevice::Text))
        {
            QMessageBox::warning(this, "警告", "无法创建日志文件:" + datafilename);
            return;
        }
        logFile.close();
    }

    bool success = QProcess::startDetached("notepad.exe", { datafilename });
    if (!success)
    {
        QMessageBox::warning(this, "警告", "打开文件失败: " + datafilename);
        return;
    }

    emit set_logRecord_color_signal("一般信息:打开日志:" + datafilename);
}

void MainWindow::slot_sensor_state()
{
    if (!sensor_view)
    {
        rightUpPlaceholder->toggleView(true);
        sensor_view = true;
        emit set_logRecord_color_signal("一般信息:开启显示传感器状态功能");
    }
    else
    {
        if (rightUpPlaceholder->isClosed())
        {
            rightUpPlaceholder->toggleView(true);
            sensor_view = true;
        }
        else
        {
            rightUpPlaceholder->toggleView(false);
            sensor_view = false;
            emit set_logRecord_color_signal("一般信息:关闭显示传感器状态功能");
        }
    }
}

void MainWindow::slot_log_record()
{
    if (!log_view)
    {
        // rightDownPlaceholder->show();
        rightDownPlaceholder->toggleView(true);
        log_view = true;
        emit set_logRecord_color_signal("一般信息:开启显示日志窗口功能");
    }
    else
    {
        if (rightDownPlaceholder->isClosed())
        {
            rightDownPlaceholder->toggleView(true);
            log_view = true;
        }
        else
        {
            rightDownPlaceholder->toggleView(false);
            log_view = false;
            emit set_logRecord_color_signal("一般信息:关闭显示日志窗口功能");
        }
    }
}

void MainWindow::slot_basic_data_show()
{
   /* if (!fmg_flag)
    {
        fmgwidget->show();
        fmg_flag = true;
        emit set_logRecord_color_signal("一般信息:开启设备调试功能");
    }
    else
    {
        fmgwidget->hide();
        fmg_flag = false;
        emit set_logRecord_color_signal("一般信息:关闭设备调试功能");
    }*/
    fmgwidget->show();

    emit set_logRecord_color_signal("一般信息:开启设备调试功能");
}

void MainWindow::slot_screenshot()
{
    PrintScreen* mprintScree = new PrintScreen();
    mprintScree->show();
}

void MainWindow::slot_image_left()
{
    d_plot->set_x_left(1);
    d_plot2->set_x_left(1);
    d_plot3->set_x_left(1);
    d_plot4->set_x_left(1);
}

void MainWindow::slot_image_right()
{
    d_plot->set_x_right(1);
    d_plot2->set_x_right(1);
    d_plot3->set_x_right(1);
    d_plot4->set_x_right(1);
}

void MainWindow::slot_observe_switch()
{
    if (commNetWork->Connection_status == false || commNetWork->facility_connect_status == false)
    {
        Global::open_qmessage("提示", "通信未连接！", "确定");
        return;
    }
    if (!observe_switch_flag)
    {
        ui->action_start_stop->setText("停止观测");
        ui->action_operation_stop->setText("停止观测");
        ui->action_start_stop->setIcon(QIcon(":/VectorIcon/open2.png"));
        get_lv1->start(120000);
        observe_switch_flag = true;
    }
    else
    {
        ui->action_start_stop->setText("开启观测");
        ui->action_operation_stop->setText("开启观测");
        get_lv1->stop();
        ui->action_start_stop->setIcon(QIcon(":/VectorIcon/stop5.png"));
        observe_switch_flag = false;
    }
    // 缺少开启观测的参数
}

void MainWindow::slot_telecontrol_switch()
{
    if (!telecontrol_flag)
    {
        ui->action_remote_this_control->setIconText("停止遥控");
        ui->action_remote_control->setIconText("停止遥控");
        ui->action_remote_this_control->setIcon(QIcon(":/VectorIcon/remotecontrol 3.png"));
        telecontrol_flag = true;
    }
    else
    {
        ui->action_remote_this_control->setIconText("开启遥控");
        ui->action_remote_control->setIconText("开启遥控");
        ui->action_remote_this_control->setIcon(QIcon(":/VectorIcon/remotecontrol 2.png"));
        telecontrol_flag = false;
    }
    // 缺少网络访问
}

void MainWindow::slot_auto_renovate_switch()
{
    //if (!auto_renovate_flag)
    //{
    //    ui->action_auto_break->setIconText("停止刷新");
    //    ui->action_auto_break->setIcon(QIcon(":/VectorIcon/start1.png"));
    //    auto_renovate_flag = true;
    //    commNetWork->set_sendClear_fun();
    //    commNetWork->set_AntTgtAng_fun(180);
    //    commNetWork->set_sendsetting_fun();
    //    fmgwidget->set_LV1_data_update(true);
    //    commNetWork->set_SixTemp_read_fun(true);
    //    commNetWork->set_AgvVoltage_read(true);
    //    commNetWork->set_AdcCardTempCtrl_read_fun(true);
    //    commNetWork->set_sixWeather_read_fun(true);
    //}
    //else
    //{
    //    ui->action_auto_break->setIconText("开始刷新");
    //    ui->action_auto_break->setIcon(QIcon(":/VectorIcon/stop3.png"));
    //    auto_renovate_flag = false;
    //    fmgwidget->set_LV1_data_update(false);
    //    commNetWork->set_AgvVoltage_read(false);
    //    commNetWork->set_AdcCardTempCtrl_read_fun(false);
    //    commNetWork->set_sixWeather_read_fun(false);
    //}
}

void MainWindow::slot_fan_switch()
{
    if (!fan_switch_flag)
    {
        ui->action_wind_break->setIconText("风机关闭");
        ui->action_wind_break->setIcon(QIcon(":/VectorIcon/wind1.png"));
        fan_switch_flag = true;
    }
    else
    {
        ui->action_wind_break->setIconText("风机开启");
        ui->action_wind_break->setIcon(QIcon(":/VectorIcon/wind3.png"));
        fan_switch_flag = false;
    }
}

void MainWindow::slot_unpitched_sound_switch()
{
    if (!unpitched_sound_flag)
    {
        ui->action_noise_break->setIconText("噪声关闭");
        ui->action_noise_break->setIcon(QIcon(":/VectorIcon/noiseIcon.png"));
        unpitched_sound_flag = true;
    }
    else
    {
        ui->action_noise_break->setIconText("噪声开启");
        ui->action_noise_break->setIcon(QIcon(":/VectorIcon/noise1.png"));
        unpitched_sound_flag = false;
    }
}

void MainWindow::slot_facility_reboot()
{
    QMessageBox msgbox;
    QString  message = "是否重启设备！";

    msgbox.setWindowTitle("警告");
    msgbox.setText("<font color='red' size='4'>" + message + "</font>");
    msgbox.setIcon(QMessageBox::Question);


    QPushButton* button1 = msgbox.addButton("确定", QMessageBox::AcceptRole);
    QPushButton* button2 = msgbox.addButton("取消", QMessageBox::RejectRole);
    msgbox.resize(800, 200);

    msgbox.exec();


    if (msgbox.clickedButton() == button1)
    {
        //controller->restart();
    }
    else if (msgbox.clickedButton() == button2)
    {
        return;
    }
    commNetWork->set_sendClear_fun();
    commNetWork->set_DevRestart_fun();
    commNetWork->set_sendsetting_fun();
}

void MainWindow::slot_outline_image()
{
    if (!outline_image_flag)
    {
        switch (outline_flag)
        {
        case 1:
            ui->widget_one_view_line->show();
            break;
        case 2:
            ui->widget_one_view_line->show();
            ui->widget_two_view_line->show();
            break;
        case 3:
            ui->widget_one_view_line->show();
            ui->widget_two_view_line->show();
            ui->widget_three_view_line->show();
        }
        outline_image_flag = true;
    }
    else
    {
        ui->widget_one_view_line->hide();
        ui->widget_two_view_line->hide();
        ui->widget_three_view_line->hide();
        outline_image_flag = false;
    }
}
void MainWindow::slot_real_time_switch()
{
    
}
void MainWindow::set_FSJ_netWork()
{
    commNetWork->show();
}

void MainWindow::set_system_time_status()
{
    
    QDateTime current = QDateTime::currentDateTime();
    QString currentTime = current.toString("yyyy-MM-dd HH:mm:ss dddd");
    qint64 elapsedSeconds = currentDateTime.secsTo(current); // 从启动到当前的时间差
    qint64 days = elapsedSeconds / 86400;
    qint64 hours = (elapsedSeconds % 86400) / 3600;
    qint64 minutes = (elapsedSeconds % 3600) / 60;
    qint64 seconds = elapsedSeconds % 60;
    QString elapsedTime = QString("(%1天%2时%3分%4秒)").arg(days).arg(hours).arg(minutes).arg(seconds);
    timelabel->setText(QString("时间:%1 %2").arg(currentTime).arg(elapsedTime));

    if (commNetWork->Connection_status == true && commNetWork->facility_connect_status == true)
    {
        statusPushButton->setText("连接状态：已通信");
        ui->action_start_stop->setEnabled(true);
        ui->action_wind_break->setEnabled(true);
        ui->action_noise_break->setEnabled(true);
        ui->action_restart->setEnabled(true);
    }
    else
    {
        statusPushButton->setText("连接状态：未通信");
        
        ui->action_start_stop->setEnabled(false);
        ui->action_wind_break->setEnabled(false);
        ui->action_noise_break->setEnabled(false);
        ui->action_restart->setEnabled(false);
        get_lv1->stop();

    }
}


void MainWindow::qwt_init()
{
    QVBoxLayout* layout = new QVBoxLayout(ui->widget_one_view);
    //    d_plot = new Plot(1,this );
    layout->addWidget(d_plot);


    d_plot->setCanvasBackground(Qt::white);
    d_plot->canvas()->setAutoFillBackground(true);

    d_plot->setColorMap(0);
    d_plot->showSpectrogram(true);
    d_plot->showContour(true);
    d_plot->clearSpectrogram();
    d_plot->set_label_enable(true);
    d_plot->set_label_Density(1);
    QVector<int> d1_1, d2_1;
    d1_1.append(2);
    d2_1.append(11);
    d_plot->set_mode(d1_1, d2_1);
    QList<double> data1;
    data1.append(20);
    data1.append(10);
    data1.append(0);
    data1.append(-10);
    data1.append(-20);
    d_plot->set_contourLevels(data1);
    connect(d_plot, &Plot::spectrogram_change, [this](QString data) {
        change_Spectrogram(1, data);
        });



    QVBoxLayout* layout3 = new QVBoxLayout(ui->widget_two_view);
    //d_plot2 = new Plot(1, this );
    layout3->addWidget(d_plot2);

    d_plot2->setCanvasBackground(Qt::white);
    d_plot2->canvas()->setAutoFillBackground(true);

    d_plot2->setColorMap(0);
    d_plot2->showSpectrogram(true);
    d_plot2->showContour(true);
    d_plot2->clearSpectrogram();
    d_plot2->set_label_enable(true);
    d_plot2->set_label_Density(1);
    QVector<int> d1_2, d2_2;
    d1_2.append(3);
    d2_2.append(12);
    d_plot2->set_mode(d1_2, d2_2);
    QList<double> data2;
    data2.append(1);
    data2.append(5);
    data2.append(2);
    data2.append(10);
    d_plot2->set_contourLevels(data2);
    connect(d_plot2, &Plot::spectrogram_change, [this](QString data) {
        change_Spectrogram(2, data);
        });


    QVBoxLayout* layout4 = new QVBoxLayout(ui->widget_three_view);

    //d_plot3 = new Plot(1, this );
    layout4->addWidget(d_plot3);

    d_plot3->setCanvasBackground(Qt::white);
    d_plot3->canvas()->setAutoFillBackground(true);

    d_plot3->setColorMap(0);//设置颜色映射
    d_plot3->showSpectrogram(true);//显示绘图使能
    d_plot3->showContour(true);//显示等值线使能
    d_plot3->clearSpectrogram();//清楚绘图及数据
    d_plot3->set_label_enable(true);//显示等值线标签使能
    d_plot3->set_label_Density(1);//设置标签密度
    QVector<int> d1_3, d2_3;
    d1_3.append(4);
    d2_3.append(13);
    d_plot3->set_mode(d1_3, d2_3);
    QList<double> data3;
    data3.append(40);
    data3.append(50);
    data3.append(60);
    data3.append(20);
    d_plot3->set_contourLevels(data3);
    connect(d_plot3, &Plot::spectrogram_change, [this](QString data) {
        change_Spectrogram(3, data);
        });


    QVBoxLayout* layout7 = new QVBoxLayout(ui->widget_four_view);

    //d_plot4 = new Plot(2, this );
    layout7->addWidget(d_plot4);

    d_plot4->setCanvasBackground(Qt::white);
    d_plot4->canvas()->setAutoFillBackground(true);


    d_plot4->setColorMap(0);
    d_plot4->showSpectrogram(true);
    d_plot4->showContour(true);
    d_plot4->clearSpectrogram();
    d_plot4->set_label_enable(true);
    d_plot4->set_label_Density(1);
    QVector<int> d1_4, d2_4;
    d1_4.append(4);
    d1_4.append(2);
    d2_4.append(13);
    d2_4.append(11);
    d_plot4->set_mode(d1_4, d2_4);
    QList<double> data4;
    data4.append(20);
    data4.append(10);
    data4.append(0);
    data4.append(-10);
    data4.append(-20);
    d_plot4->set_Combination(true);
    d_plot4->set_contourLevels(data4);
    connect(d_plot4, &Plot::spectrogram_change, [this](QString data) {
        change_Spectrogram(4, data);
        });

    //    set_title(d_plot, "温度");
    //    set_title(d_plot2, "水汽");
    //    set_title(d_plot3, "湿度");
    //    set_title(d_plot4, "温度_湿度");



    connect(d_plot, &Plot::cur_x_y1, [this](double time, QVector<double> m_heights, QVector<double> m_temps) {

        qDebug() << "time:" << time / 60 << endl;

        //    QDateTime dateTime = QDateTime::fromSecsSinceEpoch(static_cast<qint64>(time));

        //    QString time_type = dateTime.toString("HH:mm:ss");
        int totalSeconds = static_cast<int>(time);
        int hours = totalSeconds / 3600;
        int remainingSecs = totalSeconds % 3600;
        int minutes = remainingSecs / 60;
        int seconds = remainingSecs % 60;
        QString timestr = QString("%1:%2:%3").arg(hours, 2, 10, QLatin1Char('0')).arg(minutes, 2, 10, QLatin1Char('0')).arg(seconds, 2, 10, QLatin1Char('0'));


        ui->label_one_view_line->setText(timestr);

        QString data;
        for (int i = 0; i < m_heights.size(); i++)
        {
            data += QString::number(m_heights[i]) + " ";
        }
        qDebug() << "height:" << data << endl;
        data.clear();
        for (int i = 0; i < m_temps.size(); i++)
        {
            data += QString::number(m_temps[i]) + " ";
        }


        qDebug() << "protect:" << data << endl;

        ui->widget_one_view_line->graph(0)->setData(m_temps, m_heights);
        ui->widget_one_view_line->xAxis->rescale();//重新缩放x轴
        ui->widget_one_view_line->yAxis->rescale();
        ui->widget_one_view_line->replot(QCustomPlot::rpQueuedReplot);

        });
    connect(d_plot2, &Plot::cur_x_y1, [this](double time, QVector<double> m_heights, QVector<double> m_temps) {

        qDebug() << "time:" << time / 60.0 << endl;
        int totalSeconds = static_cast<int>(time);
        int hours = totalSeconds / 3600;
        int remainingSecs = totalSeconds % 3600;
        int minutes = remainingSecs / 60;
        int seconds = remainingSecs % 60;
        QString timestr = QString("%1:%2:%3").arg(hours, 2, 10, QLatin1Char('0')).arg(minutes, 2, 10, QLatin1Char('0')).arg(seconds, 2, 10, QLatin1Char('0'));


        ui->label_two_view_line->setText(timestr);

        QString data;
        for (int i = 0; i < m_heights.size(); i++)
        {
            data += QString::number(m_heights[i]) + " ";
        }
        qDebug() << "height:" << data << endl;
        data.clear();
        for (int i = 0; i < m_temps.size(); i++)
        {
            data += QString::number(m_temps[i]) + " ";
        }
        qDebug() << "protect:" << data << endl;

        ui->widget_two_view_line->graph(0)->setData(m_temps, m_heights);
        ui->widget_two_view_line->xAxis->rescale();//重新缩放x轴
        ui->widget_two_view_line->yAxis->rescale();
        ui->widget_two_view_line->replot(QCustomPlot::rpQueuedReplot);
        });
    connect(d_plot3, &Plot::cur_x_y1, [this](double time, QVector<double> m_heights, QVector<double> m_temps) {

        qDebug() << "time:" << time / 60.0 << endl;
        int totalSeconds = static_cast<int>(time);
        int hours = totalSeconds / 3600;
        int remainingSecs = totalSeconds % 3600;
        int minutes = remainingSecs / 60;
        int seconds = remainingSecs % 60;
        QString timestr = QString("%1:%2:%3").arg(hours, 2, 10, QLatin1Char('0')).arg(minutes, 2, 10, QLatin1Char('0')).arg(seconds, 2, 10, QLatin1Char('0'));


        ui->label_three_view_line->setText(timestr);

        QString data;
        for (int i = 0; i < m_heights.size(); i++)
        {
            data += QString::number(m_heights[i]) + " ";
        }
        qDebug() << "height:" << data << endl;
        data.clear();
        for (int i = 0; i < m_temps.size(); i++)
        {
            data += QString::number(m_temps[i]) + " ";
        }
        qDebug() << "protect:" << data << endl;
        ui->widget_three_view_line->graph(0)->setData(m_temps, m_heights);
        ui->widget_three_view_line->xAxis->rescale();//重新缩放x轴
        ui->widget_three_view_line->yAxis->rescale();
        ui->widget_three_view_line->replot(QCustomPlot::rpQueuedReplot);
        });
    connect(d_plot4, &Plot::cur_x_y1, [this](double time, QVector<double> m_heights, QVector<double> m_temps) {

        qDebug() << "time:" << time / 60.0 << endl;
        QString data;
        for (int i = 0; i < m_heights.size(); i++)
        {
            data += QString::number(m_heights[i]) + " ";
        }
        qDebug() << "height:" << data << endl;
        data.clear();
        for (int i = 0; i < m_temps.size(); i++)
        {
            data += QString::number(m_temps[i]) + " ";
        }
        qDebug() << "protect:" << data << endl;


        });


    History_file_Path(systemconfigfdata->systemdata.LV2_path);
    History_file_Path_lv1(systemconfigfdata->systemdata.LV1_path);
}

void MainWindow::creat_configfile()
{
    QDir currentDir = QDir::current();
    // 创建目录（相对路径）
    QString dirpath = "Dataconfig";
    if (!currentDir.exists(dirpath))
    {
        if (!currentDir.mkdir(dirpath))
        {
            qDebug() << "创建目录失败";
            return;
        }
        qDebug() << "创建目录成功";
    }
    // 构建文件路径（确保目录存在后在操作）
    QFileInfo fileInfo(currentDir, "Dataconfig/MainWindow_Layout.ini");
    // QString filePath = currentDir.filePath("Dataconfig/MainWindow_Layout.ini");
    QString filePath = fileInfo.absoluteFilePath();
    // 检查文件失败
    if (fileInfo.exists())
    {
        qDebug() << "文件已存在，无需再创建";
        return;
    }
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly))
    {
        file.close();
        qDebug() << "创建成功";
    }
    else
    {
        qDebug() << "创建失败";
    }
}
void MainWindow::loadLayout()
{
    // 恢复主窗口的几何信息
       if (settings.contains("MainWindowGeometry"))
       {
           restoreGeometry(settings.value("MainWindowGeometry").toByteArray());
       }

       // 恢复对话框的可见状态
       hisdialog_view = settings.value("OneDialogVisible", false).toBool();
       sensor_view = settings.value("TwoDialogVisible", false).toBool();
       log_view = settings.value("ThreeDialogVisible", false).toBool();

       leftPlace->toggleView(hisdialog_view);
       rightUpPlaceholder->toggleView(sensor_view);
       rightDownPlaceholder->toggleView(log_view);

       index_view = settings.value("view").toString();

       action_view->setText(index_view);
       onOPtion_view(action_view);

       // 加载已保存的视角
       // 保存到当前目录下的 my_settings.ini 文件
       QSettings settings("Dataconfig/MainWindow_Layout.ini", QSettings::IniFormat);
       DockManager->loadPerspectives(settings); // 从QSettings加载视角
       DockManager->openPerspective("autosave");
}

void MainWindow::loadDefault()
{
    // 恢复主窗口的几何信息
       if (settings.contains("MainWindowGeometry_R"))
       {
           restoreGeometry(settings.value("MainWindowGeometry_R").toByteArray());
       }

       // 恢复对话框的可见状态
       hisdialog_view = settings.value("OneDialogVisible_R", false).toBool();
       sensor_view = settings.value("TwoDialogVisible_R", false).toBool();
       log_view = settings.value("ThreeDialogVisible_R", false).toBool();

       leftPlace->toggleView(hisdialog_view);
       rightUpPlaceholder->toggleView(sensor_view);
       rightDownPlaceholder->toggleView(log_view);

       index_view = settings.value("view_R").toString();

       action_view->setText(index_view);

       onOPtion_view(action_view);
}

void MainWindow::slot_saveLayout()
{
    // 保存主窗口的几何信息
    settings.setValue("MainWindowGeometry", saveGeometry());

    // 保存对话框的可见状态
    settings.setValue("OneDialogVisible", hisdialog_view);
    settings.setValue("TwoDialogVisible", sensor_view);
    settings.setValue("ThreeDialogVisible", log_view);

    settings.setValue("outline_image", outline_image_flag);

    //index_view=action_view->text();
    settings.setValue("View", index_view);
    save_DockManager();
    QMessageBox::warning(this, "提示", "保存默认布局操作成功,系统在下次启动时自动生效!");
}
void MainWindow::set_menubar_styleSheet(int index)
{
    if (index == 0)
    {
        LoadStyleFile(":/Topic_skin_Settings/Stylesheet_Skin/Skin1.qss");


        set_title_Color(d_plot, "温度", QColor(Qt::white), QColor(62, 62, 62));
        set_title_Color(d_plot2, "水汽", QColor(Qt::white), QColor(62, 62, 62));
        set_title_Color(d_plot3, "湿度", QColor(Qt::white), QColor(62, 62, 62));
        set_title_Color(d_plot4, "温度_湿度", QColor(Qt::white), QColor(62, 62, 62));

        d_plot->set_label_color(Qt::white);
        d_plot2->set_label_color(Qt::white);
        d_plot3->set_label_color(Qt::white);
        d_plot4->set_label_color(Qt::white);

        set_view_plot_line(ui->widget_one_view_line, QColor(62, 62, 62), QColor(255, 255, 255));
        set_view_plot_line(ui->widget_two_view_line, QColor(62, 62, 62), QColor(255, 255, 255));
        set_view_plot_line(ui->widget_three_view_line, QColor(62, 62, 62), QColor(255, 255, 255));


        set_DockWidget_title_setStyleSheet(leftPlace, title_skin, button_title_close_skin);
        set_DockWidget_title_setStyleSheet(rightUpPlaceholder, title_skin, button_title_close_skin);
        set_DockWidget_title_setStyleSheet(rightDownPlaceholder, title_skin, button_title_close_skin);
    }
    else if (index == 1)
    {

        LoadStyleFile(":/Topic_skin_Settings/Stylesheet_Skin/Skin2.qss");

        set_title_Color(d_plot, "温度", QColor(Qt::black), QColor(87, 106, 140));
        set_title_Color(d_plot2, "水汽", QColor(Qt::black), QColor(87, 106, 140));
        set_title_Color(d_plot3, "湿度", QColor(Qt::black), QColor(87, 106, 140));
        set_title_Color(d_plot4, "温度_湿度", QColor(Qt::black), QColor(87, 106, 140));


        d_plot->set_label_color(Qt::black);
        d_plot2->set_label_color(Qt::black);
        d_plot3->set_label_color(Qt::black);
        d_plot4->set_label_color(Qt::black);


        set_view_plot_line(ui->widget_one_view_line, QColor(87, 106, 140), QColor(0, 0, 0));
        set_view_plot_line(ui->widget_two_view_line, QColor(87, 106, 140), QColor(0, 0, 0));
        set_view_plot_line(ui->widget_three_view_line, QColor(87, 106, 140), QColor(0, 0, 0));


        set_DockWidget_title_setStyleSheet(leftPlace, title_skin1, button_title_close_skin);
        set_DockWidget_title_setStyleSheet(rightUpPlaceholder, title_skin1, button_title_close_skin);
        set_DockWidget_title_setStyleSheet(rightDownPlaceholder, title_skin1, button_title_close_skin);
    }
}
void MainWindow::set_title_Color(Plot* d_p, QString text, QColor data, QColor data2)
{
    QwtText titleText(text);
    // 设置字体样式
    QFont titleFont("微软雅黑", 12, QFont::Bold);
    titleText.setFont(titleFont);

    // 设置文字颜色和背景

    titleText.setColor(data);
    titleText.setBackgroundBrush(QBrush(data2));


    // 设置对齐方式（水平居中+顶部对齐）
    titleText.setRenderFlags(Qt::AlignHCenter | Qt::AlignTop);

    // 应用标题
    d_p->setTitle(titleText);
    d_p->set_yright_uint(text);
}
void MainWindow::set_view_plot_line(QCustomPlot* plot, QColor color_background, QColor color_axis)
{

    plot->addGraph();
    plot->graph(0)->setPen(QPen(Qt::red, 3));
    plot->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
    // 设置背景色
    plot->setBackground(QBrush(color_background));

    // 设置x/y轴文本色、轴线色、字体等
    plot->xAxis->setTickLabelColor(color_axis); //设置x,y轴标签刻度的颜色
    plot->xAxis->setLabelColor(color_axis);//设置x,y轴标签颜色
    plot->xAxis->setBasePen(QPen(color_axis)); //设置x,y轴的基础线条
    plot->xAxis->setTickPen(QPen(color_axis));//设置x,y轴的刻度线
    plot->xAxis->setSubTickPen(color_axis); //设置x,y轴的次刻度线


    QCPAxis* xAxis = plot->xAxis;
    QFont font("宋体", 8);
    xAxis->setTickLabelFont(font);
    QFont xtickFont = xAxis->tickLabelFont();
    xtickFont.setPointSize(8);
    plot->xAxis->setTickLabelFont(xtickFont);


    plot->yAxis->setTickLabelColor(color_axis);
    plot->yAxis->setLabelColor(color_axis);
    plot->yAxis->setBasePen(QPen(color_axis));
    plot->yAxis->setTickPen(QPen(color_axis));
    plot->yAxis->setSubTickPen(color_axis);

    QCPAxis* yAxis = plot->yAxis;
    yAxis->setTickLabelFont(font);
    QFont ytickFont = yAxis->tickLabelFont();
    ytickFont.setPointSize(8);
    plot->yAxis->setTickLabelFont(ytickFont);



    plot->axisRect()->setupFullAxesBox(true);
    plot->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectAxes | QCP::iSelectLegend | QCP::iSelectPlottables);
    plot->graph()->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssCircle, Qt::red, 1));//设置数据点圆圈为黑色
    plot->graph()->setLineStyle(QCPGraph::lsLine);//设置线样式为折线
    plot->graph()->setAdaptiveSampling(true);//自适应采样
    plot->setAntialiasedElements(QCP::aeAll);//启用所有抗锯齿

    plot->replot(QCustomPlot::rpQueuedReplot);
}
void MainWindow::save_DockManager()
{
    QString PerspectiveName = "autosave";
    if (PerspectiveName.isEmpty()) {
        return;
    }

    // 检查名称是否已存在
    if (DockManager->perspectiveNames().contains(PerspectiveName)) {
        DockManager->removePerspective(PerspectiveName);
    }

    // 添加视角并保存到配置文件
    DockManager->addPerspective(PerspectiveName);

    // 保存到当前目录下的 my_settings.ini 文件
    QSettings settings("Dataconfig/MainWindow_Layout.ini", QSettings::IniFormat);
    //settings.remove();
    DockManager->savePerspectives(settings); // 保存所有视角到QSettings
    settings.sync(); // 确保写入磁盘

}

void MainWindow::LoadStyleFile(QString strStyle_path)
{
    QFile qss(strStyle_path);
    if (!qss.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        qWarning() << " Cannot open styleSheet file:" << strStyle_path;
        return;
    }
    QString styleSheet = QString::fromUtf8(qss.readAll());
    qApp->setStyleSheet(styleSheet);
    qss.close();

    //多个qss文件读取
    //QString strDirPath = strStyle_path;
    //QStringList strListStyleFiles = QDir(strStyle_path).entryList(QDir::Files);
    //QString strStyle = GetAllStyle(strListStyleFiles,strStyle_path);
    //qApp->setStyleSheet(strStyle);
}

QString MainWindow::GetAllStyle(QStringList strListStyleFiles, QString strDirPath)
{
    if (!strDirPath.endsWith("/"))
    {
        strDirPath.append("/");
    }
    QString strStyle;
    for (auto strFileName : strListStyleFiles)
    {
        QFile fileRead(strDirPath + strFileName);
        if (fileRead.open(QFile::ReadOnly))
        {
            strStyle += QLatin1String(fileRead.readAll());
        }
        fileRead.close();
    }
    return strStyle;
}


void MainWindow::set_DockWidget_title_setStyleSheet(CDockWidget *dock, QString title_skin, QString button_title_close_skin)
{
    if (auto dockarea = dock->dockAreaWidget())
        {
            auto titlebar = dockarea->titleBar();
            titlebar->button(TitleBarButton::TitleBarButtonClose)->setStyleSheet(button_title_close_skin);
            titlebar->setStyleSheet(title_skin);
        }
}

void MainWindow::set_action_comboBox()
{
    QAction* actionOption_dis = ui->action_distance_tool; // 距离档位
       // QMenu *menu_dis = new QMenu(this);
    menu_dis->addAction("2千米");
    menu_dis->addAction("4千米");
    menu_dis->addAction("6千米");
    menu_dis->addAction("8千米");
    menu_dis->addAction("10千米");
    ui->toolBar->addAction(actionOption_dis);
    QToolButton* toolButton_dis = qobject_cast<QToolButton*>(ui->toolBar->widgetForAction(actionOption_dis));
    toolButton_dis->setMenu(menu_dis);
    toolButton_dis->setPopupMode(QToolButton::InstantPopup); // 点击直接弹出
    menu_dis->setStyleSheet("QMenu"
        "{color:white;background-color:black;}"
        "QMenu::item"
        "{color:white;background-color:black;}"
        "QMenu::item::selected"
        "{color:white;background-color:gray;}");

    connect(menu_dis, &QMenu::triggered, this, &MainWindow::onOPtion_dis);

    QAction* actionOptions_time = ui->action_time_tool; // 时间档位
    // QMenu *menu_time = new QMenu(this);
    menu_time->addAction("1小时");
    menu_time->addAction("4小时");
    menu_time->addAction("8小时");
    menu_time->addAction("12小时");
    menu_time->addAction("16小时");
    menu_time->addAction("20小时");
    menu_time->addAction("24小时");
    ui->toolBar->addAction(actionOptions_time);
    QToolButton* toolButton_time = qobject_cast<QToolButton*>(ui->toolBar->widgetForAction(actionOptions_time));
    toolButton_time->setMenu(menu_time);
    toolButton_time->setPopupMode(QToolButton::InstantPopup); // 点击直接弹出
    menu_time->setStyleSheet("QMenu"
        "{color:white;background-color:black;}"
        "QMenu::item"
        "{color:white;background-color:black;}"
        "QMenu::item::selected"
        "{color:white;background-color:gray;}");

    connect(menu_time, &QMenu::triggered, this, &MainWindow::onOPtion_time);
    QAction* action = ui->action_view_choice; // 视图选择
    QMenu* menu_view = new QMenu(this);
    QAction* action1 = menu_view->addAction("单视图");
    QAction* action2 = menu_view->addAction("双视图");
    QAction* action3 = menu_view->addAction("三视图");
    QAction* action4 = menu_view->addAction("四视图");
    ui->toolBar->addAction(action);
    QToolButton* toolButton = qobject_cast<QToolButton*>(ui->toolBar->widgetForAction(action));
    toolButton->setMenu(menu_view);
    toolButton->setPopupMode(QToolButton::InstantPopup); // 点击直接弹出
    menu_view->setStyleSheet("QMenu"
        "{color:white;background-color:black;}"
        "QMenu::item"
        "{color:white;background-color:black;}"
        "QMenu::item::selected"
        "{color:white;background-color:gray;}");

    connect(menu_view, &QMenu::triggered, this, &MainWindow::onOPtion_view);
}

void MainWindow::set_tracer(QCustomPlot* plot, QCPItemTracer* tracer, QCPItemText* tracerLabel)
{
    // 游标
    plot->setMouseTracking(true); // 让游标自动随鼠标移动，若不想游标随鼠标动，则禁止
    tracer->setVisible(true);     // 设置可见性
    // tracer->setPen(QPen(QBrush(QColor(Qt::red)),Qt::DashLine));   //虚线游标
    tracer->setPen(QPen(Qt::red));             // 圆圈轮廓颜色
    tracer->setBrush(QBrush(Qt::red));         // 圆圈圈内颜色
    tracer->setStyle(QCPItemTracer::tsCircle); // 圆圈
    tracer->setSize(3);

    // 游标说明
    tracerLabel->setVisible(true);                                   // 设置可见性
    tracerLabel->setLayer("Overlay");                                // 设置图层为overlay，因为需要频繁刷新
    tracerLabel->setPen(QPen(Qt::white));                            // 设置游标说明颜色
    tracerLabel->setPositionAlignment(Qt::AlignLeft | Qt::AlignTop); // 左上
    tracerLabel->setFont(QFont(font().family(), 8));                 // 字体大小
    tracerLabel->setPadding(QMargins(4, 4, 4, 4));                   // 文字距离边框几个像素
    tracerLabel->position->setType(QCPItemPosition::ptAxisRectRatio);
    tracerLabel->position->setParentAnchor(tracer->position); // 设置标签自动随着游标移动

    // lambda表达式 mouseMoveEvent

    connect(plot, &QCustomPlot::mouseMove, [=](QMouseEvent* event)
        {
            double x = plot->xAxis->pixelToCoord(event->pos().x());
            double y = plot->yAxis->pixelToCoord(event->pos().y());
            tracer->setGraphKey(x); // 将游标横坐标设置成刚获得的横坐标数据x
            tracer->position->setCoords(x, y);
            //        QDateTime time = QDateTime::fromSecsSinceEpoch(static_cast<qint64>(x));
            //        QString timestr = time.toString("HH:mm:ss");
            tracerLabel->setText(QString("%1,%2").arg(x).arg(y));
            tracerLabel->setColor(Qt::white);
            qDebug() << "mouseMove:" << x << y;
            plot->replot(); });
}

void MainWindow::change_Spectrogram(int num, QString data)
{
    if (num == 1)
    {


        if (data == "温度")
        {
            d_plot->set_Spectrogram_number(1);
            //设置模式及等值线
            QVector<int> d1_1, d2_1;
            d1_1.append(2);
            d2_1.append(11);
            d_plot->set_mode(d1_1, d2_1);
            QList<double> data1;
            data1.append(20);
            data1.append(10);
            data1.append(0);
            data1.append(-10);
            data1.append(-20);
            d_plot->set_Combination(false);
            d_plot->set_contourLevels(data1);
            set_title(d_plot, "温度");

            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {
                    for (int i = 0; i < count; i++)
                        d_plot->addRealtimeData(0, m_times[0][i], m_heights[0], m_temps[0][i]);
                }
                else if (open_lv1_lv2==1)
                {
                    for (int i = 0; i < temperatureProfilees[0].size(); i++)
                        d_plot->addRealtimeData(0, timerVector[0][i], table_header[0], temperatureProfilees[0][i]);
                }
               
            }
            else
            {
                d_plot->open_file(cur_filePath);
            }
        }
        else if (data == "水汽")
        {
            d_plot->set_Spectrogram_number(1);
            //设置模式及等值线
            QVector<int> d1_2, d2_2;
            d1_2.append(3);
            d2_2.append(12);
            d_plot->set_mode(d1_2, d2_2);
            QList<double> data2;
            data2.append(1);
            data2.append(5);
            data2.append(2);
            data2.append(10);
            d_plot->set_Combination(false);
            d_plot->set_contourLevels(data2);
            set_title(d_plot, "水汽");

            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {
                    for (int i = 0; i < count; i++)
                        d_plot->addRealtimeData(0, m_times[1][i], m_heights[1], m_temps[1][i]);
                }
                else if (open_lv1_lv2==1)
                {
                    for (int i = 0; i < vaporProfilees[1].size(); i++)
                        d_plot->addRealtimeData(0, timerVector[1][i], table_header[1], vaporProfilees[1][i]);
                }
                
            }
            else
            {
                d_plot->open_file(cur_filePath);
            }
        }
        else if (data == "湿度")
        {
            d_plot->set_Spectrogram_number(1);
            //设置模式及等值线
            QVector<int> d1_3, d2_3;
            d1_3.append(4);
            d2_3.append(13);
            d_plot->set_mode(d1_3, d2_3);
            QList<double> data3;
            data3.append(40);
            data3.append(50);
            data3.append(60);
            data3.append(20);
            d_plot->set_Combination(false);
            d_plot->set_contourLevels(data3);
            set_title(d_plot, "湿度");

            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2==2)
                {
                    for (int i = 0; i < count; i++)
                        d_plot->addRealtimeData(0, m_times[2][i], m_heights[2], m_temps[2][i]);
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < humidityProfilees[2].size(); i++)
                        d_plot->addRealtimeData(0, timerVector[2][i], table_header[2], humidityProfilees[2][i]);
                }
            }
            else
            {
                d_plot->open_file(cur_filePath);
            }
        }
        else if (data == "温度_湿度")
        {
            d_plot->set_Spectrogram_number(2);
            QVector<int> d1_4, d2_4;
            d1_4.append(4);
            d1_4.append(2);
            d2_4.append(13);
            d2_4.append(11);
            d_plot->set_mode(d1_4, d2_4);
            QList<double> data4;
            data4.append(20);
            data4.append(10);
            data4.append(0);
            data4.append(-10);
            data4.append(-20);
            d_plot->set_Combination(true);
            d_plot->set_contourLevels(data4);
            set_title(d_plot, "温度_湿度");
            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {

                    for (int i = 0; i < count; i++)
                    {
                        d_plot->addRealtimeData(0, m_times[2][i], m_heights[2], m_temps[2][i]);
                        d_plot->addRealtimeData(1, m_times[0][i], m_heights[0], m_temps[0][i]);
                    }
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < temperatureProfilees[0].size(); i++)
                    {
                        d_plot->addRealtimeData(0, timerVector[0][i], table_header[0], temperatureProfilees[0][i]);
                        d_plot->addRealtimeData(1, timerVector[2][i], table_header[2], humidityProfilees[2][i]);
                    }
                        

                }
            }
            else
            {
                d_plot->open_file(cur_filePath);
            }

        }
        d_plot->setColorMap(0);
        d_plot->showSpectrogram(true);
        d_plot->showContour(true);
        d_plot->set_label_enable(true);
        d_plot->set_label_Density(1);

    }
    else if (num == 2)
    {

        if (data == "温度")
        {
            d_plot2->set_Spectrogram_number(1);
            //设置模式及等值线
            QVector<int> d1_1, d2_1;
            d1_1.append(2);
            d2_1.append(11);
            d_plot2->set_mode(d1_1, d2_1);
            QList<double> data1;
            data1.append(20);
            data1.append(10);
            data1.append(0);
            data1.append(-10);
            data1.append(-20);
            d_plot2->set_Combination(false);
            d_plot2->set_contourLevels(data1);
            set_title(d_plot2, "温度");


            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {
                    for (int i = 0; i < count; i++)
                        d_plot2->addRealtimeData(0, m_times[0][i], m_heights[0], m_temps[0][i]);
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < temperatureProfilees[0].size(); i++)
                        d_plot2->addRealtimeData(0, timerVector[0][i], table_header[0], temperatureProfilees[0][i]);
                }
            }
            else
            {
                d_plot2->open_file(cur_filePath);
            }
        }
        else if (data == "水汽")
        {
            d_plot2->set_Spectrogram_number(1);
            //设置模式及等值线
            QVector<int> d1_2, d2_2;
            d1_2.append(3);
            d2_2.append(12);
            d_plot2->set_mode(d1_2, d2_2);
            QList<double> data2;
            data2.append(1);
            data2.append(5);
            data2.append(2);
            data2.append(10);
            d_plot2->set_Combination(false);
            d_plot2->set_contourLevels(data2);
            set_title(d_plot2, "水汽");

            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {

                    for (int i = 0; i < count; i++)
                        d_plot2->addRealtimeData(0, m_times[1][i], m_heights[1], m_temps[1][i]);
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < vaporProfilees[1].size(); i++)
                        d_plot2->addRealtimeData(0, timerVector[1][i], table_header[1], vaporProfilees[1][i]);
                }
            }
            else
            {
                d_plot2->open_file(cur_filePath);
            }
        }
        else if (data == "湿度")
        {
            d_plot2->set_Spectrogram_number(1);
            //设置模式及等值线
            QVector<int> d1_3, d2_3;
            d1_3.append(4);
            d2_3.append(13);
            d_plot2->set_mode(d1_3, d2_3);
            QList<double> data3;
            data3.append(40);
            data3.append(50);
            data3.append(60);
            data3.append(20);
            d_plot2->set_Combination(false);
            d_plot2->set_contourLevels(data3);
            set_title(d_plot2, "湿度");

            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {

                    for (int i = 0; i < count; i++)
                        d_plot2->addRealtimeData(0, m_times[2][i], m_heights[2], m_temps[2][i]);
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < humidityProfilees[2].size(); i++)
                        d_plot2->addRealtimeData(0, timerVector[2][i], table_header[2], humidityProfilees[2][i]);
                }
            }
            else
            {
                d_plot2->open_file(cur_filePath);
            }
        }
        else if (data == "温度_湿度")
        {
            d_plot2->set_Spectrogram_number(2);
            QVector<int> d1_4, d2_4;
            d1_4.append(4);
            d1_4.append(2);
            d2_4.append(13);
            d2_4.append(11);
            d_plot2->set_mode(d1_4, d2_4);
            QList<double> data4;
            data4.append(20);
            data4.append(10);
            data4.append(0);
            data4.append(-10);
            data4.append(-20);
            d_plot2->set_Combination(true);
            d_plot2->set_contourLevels(data4);
            set_title(d_plot2, "温度_湿度");
            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {

                    for (int i = 0; i < count; i++)
                    {
                        d_plot2->addRealtimeData(0, m_times[2][i], m_heights[2], m_temps[2][i]);
                        d_plot2->addRealtimeData(1, m_times[0][i], m_heights[0], m_temps[0][i]);
                    }
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < temperatureProfilees[0].size(); i++)
                    {
                        d_plot2->addRealtimeData(0, timerVector[0][i], table_header[0], temperatureProfilees[0][i]);
                        d_plot2->addRealtimeData(1, timerVector[2][i], table_header[2], humidityProfilees[2][i]);
                    }


                }
            }
            else
            {
                d_plot2->open_file(cur_filePath);
            }

        }
        d_plot2->setColorMap(0);
        d_plot2->showSpectrogram(true);
        d_plot2->showContour(true);
        d_plot2->set_label_enable(true);
        d_plot2->set_label_Density(1);
    }
    else if (num == 3)
    {

        if (data == "温度")
        {
            d_plot3->set_Spectrogram_number(1);
            //设置模式及等值线
            QVector<int> d1_1, d2_1;
            d1_1.append(2);
            d2_1.append(11);
            d_plot3->set_mode(d1_1, d2_1);
            QList<double> data1;
            data1.append(20);
            data1.append(10);
            data1.append(0);
            data1.append(-10);
            data1.append(-20);
            d_plot3->set_Combination(false);
            d_plot3->set_contourLevels(data1);
            set_title(d_plot3, "温度");

            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {

                    for (int i = 0; i < count; i++)
                        d_plot3->addRealtimeData(0, m_times[0][i], m_heights[0], m_temps[0][i]);
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < temperatureProfilees[0].size(); i++)
                        d_plot3->addRealtimeData(0, timerVector[0][i], table_header[0], temperatureProfilees[0][i]);
                }
            }
            else
            {
                d_plot3->open_file(cur_filePath);
            }
        }
        else if (data == "水汽")
        {
            d_plot3->set_Spectrogram_number(1);
            //设置模式及等值线
            QVector<int> d1_2, d2_2;
            d1_2.append(3);
            d2_2.append(12);
            d_plot3->set_mode(d1_2, d2_2);
            QList<double> data2;
            data2.append(1);
            data2.append(5);
            data2.append(2);
            data2.append(10);
            d_plot3->set_Combination(false);
            d_plot3->set_contourLevels(data2);
            set_title(d_plot3, "水汽");

            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {

                    for (int i = 0; i < count; i++)
                        d_plot3->addRealtimeData(0, m_times[1][i], m_heights[1], m_temps[1][i]);
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < vaporProfilees[1].size(); i++)
                        d_plot3->addRealtimeData(0, timerVector[1][i], table_header[1], vaporProfilees[1][i]);
                }
            }
            else
            {
                d_plot3->open_file(cur_filePath);
            }
        }
        else if (data == "湿度")
        {
            d_plot3->set_Spectrogram_number(1);
            //设置模式及等值线
            QVector<int> d1_3, d2_3;
            d1_3.append(4);
            d2_3.append(13);
            d_plot3->set_mode(d1_3, d2_3);
            QList<double> data3;
            data3.append(40);
            data3.append(50);
            data3.append(60);
            data3.append(20);
            d_plot3->set_Combination(false);
            d_plot3->set_contourLevels(data3);
            set_title(d_plot3, "湿度");

            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {

                    for (int i = 0; i < count; i++)
                        d_plot3->addRealtimeData(0, m_times[2][i], m_heights[2], m_temps[2][i]);
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < humidityProfilees[2].size(); i++)
                        d_plot3->addRealtimeData(0, timerVector[2][i], table_header[2], humidityProfilees[2][i]);
                }
            }
            else
            {
                d_plot3->open_file(cur_filePath);
            }
        }
        else if (data == "温度_湿度")
        {
            d_plot3->set_Spectrogram_number(2);
            QVector<int> d1_4, d2_4;
            d1_4.append(4);
            d1_4.append(2);
            d2_4.append(13);
            d2_4.append(11);
            d_plot3->set_mode(d1_4, d2_4);
            QList<double> data4;
            data4.append(20);
            data4.append(10);
            data4.append(0);
            data4.append(-10);
            data4.append(-20);
            d_plot3->set_Combination(true);
            d_plot3->set_contourLevels(data4);
            set_title(d_plot3, "温度_湿度");
            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {
                    for (int i = 0; i < count; i++)
                    {
                        d_plot3->addRealtimeData(0, m_times[2][i], m_heights[2], m_temps[2][i]);
                        d_plot3->addRealtimeData(1, m_times[0][i], m_heights[0], m_temps[0][i]);
                    }
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < temperatureProfilees[0].size(); i++)
                    {
                        d_plot3->addRealtimeData(0, timerVector[0][i], table_header[0], temperatureProfilees[0][i]);
                        d_plot3->addRealtimeData(1, timerVector[2][i], table_header[2], humidityProfilees[2][i]);
                    }


                }
            }
            else
            {
                d_plot3->open_file(cur_filePath);
            }

        }
        d_plot3->setColorMap(0);
        d_plot3->showSpectrogram(true);
        d_plot3->showContour(true);
        d_plot3->set_label_enable(true);
        d_plot3->set_label_Density(1);
    }
    else if (num == 4)
    
{

        if (data == "温度")
        {
            d_plot4->set_Spectrogram_number(1);
            //设置模式及等值线
            QVector<int> d1_1, d2_1;
            d1_1.append(2);
            d2_1.append(11);
            d_plot4->set_mode(d1_1, d2_1);
            QList<double> data1;
            data1.append(20);
            data1.append(10);
            data1.append(0);
            data1.append(-10);
            data1.append(-20);
            d_plot4->set_Combination(false);
            d_plot4->set_contourLevels(data1);
            set_title(d_plot4, "温度");


            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {
                    for (int i = 0; i < count; i++)
                        d_plot4->addRealtimeData(0, m_times[0][i], m_heights[0], m_temps[0][i]);
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < temperatureProfilees[0].size(); i++)
                        d_plot->addRealtimeData(0, timerVector[0][i], table_header[0], temperatureProfilees[0][i]);
                }
            }
            else
            {
                d_plot4->open_file(cur_filePath);
            }
        }
        else if (data == "水汽")
        {
            d_plot4->set_Spectrogram_number(1);
            //设置模式及等值线
            QVector<int> d1_2, d2_2;
            d1_2.append(3);
            d2_2.append(12);
            d_plot4->set_mode(d1_2, d2_2);
            QList<double> data2;
            data2.append(1);
            data2.append(5);
            data2.append(2);
            data2.append(10);
            d_plot4->set_Combination(false);
            d_plot4->set_contourLevels(data2);
            set_title(d_plot4, "水汽");

            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {
                    for (int i = 0; i < count; i++)
                        d_plot4->addRealtimeData(0, m_times[1][i], m_heights[1], m_temps[1][i]);
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < vaporProfilees[1].size(); i++)
                        d_plot->addRealtimeData(0, timerVector[1][i], table_header[1], vaporProfilees[1][i]);
                }
            }
            else
            {
                d_plot4->open_file(cur_filePath);
            }
        }
        else if (data == "湿度")
        {
            d_plot4->set_Spectrogram_number(1);
            //设置模式及等值线
            QVector<int> d1_3, d2_3;
            d1_3.append(4);
            d2_3.append(13);
            d_plot4->set_mode(d1_3, d2_3);
            QList<double> data3;
            data3.append(40);
            data3.append(50);
            data3.append(60);
            data3.append(20);
            d_plot4->set_Combination(false);
            d_plot4->set_contourLevels(data3);
            set_title(d_plot4, "湿度");

            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {
                    for (int i = 0; i < count; i++)
                        d_plot4->addRealtimeData(0, m_times[2][i], m_heights[2], m_temps[2][i]);
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < humidityProfilees[2].size(); i++)
                        d_plot4->addRealtimeData(0, timerVector[2][i], table_header[2], humidityProfilees[2][i]);
                }
            }
            else
            {
                d_plot4->open_file(cur_filePath);
            }
        }
        else if (data == "温度_湿度")
        {
            d_plot4->set_Spectrogram_number(2);
            QVector<int> d1_4, d2_4;
            d1_4.append(4);
            d1_4.append(2);
            d2_4.append(13);
            d2_4.append(11);
            d_plot4->set_mode(d1_4, d2_4);
            QList<double> data4;
            data4.append(20);
            data4.append(10);
            data4.append(0);
            data4.append(-10);
            data4.append(-20);
            d_plot4->set_Combination(true);
            d_plot4->set_contourLevels(data4);
            set_title(d_plot, "温度_湿度");
            if (cur_mode == 2)
            {
                //从数据获取
                if (open_lv1_lv2 == 2)
                {
                    for (int i = 0; i < count; i++)
                    {
                        d_plot4->addRealtimeData(0, m_times[2][i], m_heights[2], m_temps[2][i]);
                        d_plot4->addRealtimeData(1, m_times[0][i], m_heights[0], m_temps[0][i]);
                    }
                }
                else if (open_lv1_lv2 == 1)
                {
                    for (int i = 0; i < temperatureProfilees[0].size(); i++)
                    {
                        d_plot4->addRealtimeData(0, timerVector[0][i], table_header[0], temperatureProfilees[0][i]);
                        d_plot4->addRealtimeData(1, timerVector[2][i], table_header[2], humidityProfilees[2][i]);
                    }


                }
            }
            else
            {
                d_plot4->open_file(cur_filePath);
            }

        }
        d_plot4->setColorMap(0);
        d_plot4->showSpectrogram(true);
        d_plot4->showContour(true);
        d_plot4->set_label_enable(true);
        d_plot4->set_label_Density(1);
    }
}

void MainWindow::change_Spectrogram_lv1(int num, QString data)
{
}

void MainWindow::set_title(Plot* d_p, QString text)
{
    QwtText titleText(text);
    // 设置字体样式
    QFont titleFont("微软雅黑", 12, QFont::Bold);
    titleText.setFont(titleFont);

    // 设置文字颜色和背景

    if (systemconfigfdata->systemdata.set_skin == 0)
    {
        titleText.setColor(Qt::white);
        titleText.setBackgroundBrush(QBrush(QColor(62, 62, 62)));
    }
    else if (systemconfigfdata->systemdata.set_skin == 1)
    {
        titleText.setColor(Qt::black);
        titleText.setBackgroundBrush(QBrush(QColor(87, 106, 140)));
    }

    // 设置对齐方式（水平居中+顶部对齐）
    titleText.setRenderFlags(Qt::AlignHCenter | Qt::AlignTop);

    // 应用标题
    d_p->setTitle(titleText);
    d_p->set_yright_uint(text);
}

void MainWindow::outline_image(bool flag)
{
    if (flag)
    {
        switch (outline_flag)
        {
        case 1:
            ui->widget_one_view_line->show();
            ui->widget_two_view_line->hide();
            ui->widget_three_view_line->hide();
            break;
        case 2:
            ui->widget_one_view_line->show();
            ui->widget_two_view_line->show();
            ui->widget_three_view_line->hide();
            break;
        case 3:
            ui->widget_one_view_line->show();
            ui->widget_two_view_line->show();
            ui->widget_three_view_line->show();
        }
    }
}

int MainWindow::lv1_n1_fun(Lv1_Bright_temperature_data* data)
{
    data->QCFlag_BT[0] = '9';
    double temp[14] = { data->Ch_Freq1,data->Ch_Freq2,data->Ch_Freq3,data->Ch_Freq4,data->Ch_Freq5,data->Ch_Freq6,data->Ch_Freq7,
                       data->Ch_Freq8,data->Ch_Freq9,data->Ch_Freq10,data->Ch_Freq11,data->Ch_Freq12,data->Ch_Freq13,data->Ch_Freq14 };
    for (int i = 0; i < 14; i++)
    {
        if (temp[i] >= 7 && temp[i] <= 320 && data->QCFlag_BT[0] != "1")
        {
            data->QCFlag_BT[0] = '0';
        }
        else if (temp[i] >= 1 && temp[i] < 7 && temp[i]>320 && temp[i] <= 330)
        {
            data->QCFlag_BT[0] = '1';
        }
        else
        {
            data->QCFlag_BT[0] = '2';
            return 2;
        }
    }
}

void MainWindow::lv1_Historical_data(Lv1_Bright_temperature_data* data)
{
    QVector<double> temp;
    temp.append(data->Ch_Freq1);
    temp.append(data->Ch_Freq2);
    temp.append(data->Ch_Freq3);
    temp.append(data->Ch_Freq4);
    temp.append(data->Ch_Freq5);
    temp.append(data->Ch_Freq6);
    temp.append(data->Ch_Freq7);
    temp.append(data->Ch_Freq8);
    temp.append(data->Ch_Freq9);
    temp.append(data->Ch_Freq10);
    temp.append(data->Ch_Freq11);
    temp.append(data->Ch_Freq12);
    temp.append(data->Ch_Freq13);
    temp.append(data->Ch_Freq14);
    Minimum_variability_check_data.append(temp);

    while (Minimum_variability_check_data.size() > 5)
    {
        Minimum_variability_check_data.removeFirst();
    }
}

int MainWindow::lv1_n2_fun(Lv1_Bright_temperature_data* data)
{
    data->QCFlag_BT[1] = '9';
    if (Minimum_variability_check_data.size() < 5)
        return -1;
    for (int i = 0; i < 14; i++)
    {
        std::unordered_map<double, int> freqmap;
        for (int j = 0; j <= Minimum_variability_check_data.size(); j++)
        {
            freqmap[Minimum_variability_check_data[i][j]]++;
        }
        int duplicates = 0;
        for (auto& pair : freqmap)
        {
            if (pair.second > duplicates)
                duplicates += (pair.second);
        }
        if (duplicates < 2 && data->QCFlag_BT[1] != "1")
        {
            data->QCFlag_BT[1] = '0';
        }
        else if (duplicates >= 2 && duplicates <= 4)
        {
            data->QCFlag_BT[1] = '1';
        }
        else
        {
            data->QCFlag_BT[1] = '2';
            return 2;
        }

    }

}

int MainWindow::lv1_n3_fun(Lv1_Bright_temperature_data* data, int sec)
{
    data->QCFlag_BT[2] = '9';
    if (data->Rain == 0 && lv1_n3_start_time.isEmpty())
    {
        data->QCFlag_BT[2] = '0';
    }
    else if (data->Rain == 1 && lv1_n3_start_time.isEmpty())
    {
        lv1_n3_start_time.clear();
        lv1_n3_start_time = data->DateTime;
        if (!QDateTime::fromString(lv1_n3_start_time, "yyyy-MM-dd HH:mm:ss").isValid())
            lv1_n3_start_time.clear();
        data->QCFlag_BT[2] = '9';
        lv1_n3_1 = true;

    }
    else if (!lv1_n3_start_time.isEmpty())
    {
        QDateTime curtime = QDateTime::fromString(data->DateTime, "yyyy-MM-dd HH:mm:ss");
        QDateTime starttime = QDateTime::fromString(lv1_n3_start_time, "yyyy-MM-dd HH:mm:ss");
        if (!starttime.isValid() || !curtime.isValid())
        {
            return 9;
        }
        qint64 secdiff = starttime.secsTo(curtime);
        if (secdiff < 0)
            secdiff = secdiff * (-1);
        if (secdiff >= sec)
        {
            lv1_n3_start_time.clear();
            if (lv1_n3_1)
                data->QCFlag_BT[2] = '0';
            else
                data->QCFlag_BT[2] = '9';

        }
        else
        {
            if (data->Rain == 0)
                lv1_n3_1 = false;
            data->QCFlag_BT[2] = '9';
        }
    }

}

int MainWindow::lv1_n4_fun(struct Lv1_Bright_temperature_data* data)
{
    data->QCFlag_BT[3] = '9';
    if (Minimum_variability_check_data.size() <= 1)
        return -1;

    for (int i = 0; i < 14; i++)
    {
        double diff = fabs(Minimum_variability_check_data[i][Minimum_variability_check_data.size() - 1] - Minimum_variability_check_data[i][Minimum_variability_check_data.size() - 2]);
        if (diff <= 2 && data->QCFlag_BT[3] != "1")
        {
            data->QCFlag_BT[3] = '0';
        }
        else if (diff > 2 && diff <= 4)
        {
            data->QCFlag_BT[3] = '1';
        }
        else
        {
            data->QCFlag_BT[3] = '2';
            return 2;
        }
    }
}

int MainWindow::lv1_n5_fun(struct Lv1_Bright_temperature_data* data, double min, double max)
{
    data->QCFlag_BT[4] = '9';
    for (int i = 0; i < 14; i++)
    {
        if (Minimum_variability_check_data[i].last() <= max && Minimum_variability_check_data[i].last() >= min)
        {
            data->QCFlag_BT[4] = '0';
        }
        else
        {
            data->QCFlag_BT[4] = '2';
            return 2;
        }
    }
}
int MainWindow::set_QCFlag(struct Lv1_Bright_temperature_data* data)
{
    if (data->QCFlag_BT.contains('2'))
    {
        data->QCFlag = '2';
        return 2;
    }
    else if (data->QCFlag_BT.contains('1'))
    {
        data->QCFlag = '1';
        return 1;
    }
    else if (data->QCFlag_BT.contains('0'))
    {
        data->QCFlag = '0';
        return 0;
    }
    else
    {
        data->QCFlag = '9';
        return 9;
    }

}
void MainWindow::deleteOldFiles(QString currentFolder, QString time)
{
    if (!QDateTime::fromString(time, "yyyy-MM-dd").isValid())
    {
        return;
    }

    QDateTime targettime = QDateTime::fromString(time, "yyyy-MM-dd");
    QDir dir(currentFolder);
    if (!dir.exists())
    {
        //QMessageBox::warning(nullptr, "警告", QString("文件路径不存在:\n%1").arg(currentFolder));
        return;
    }
    QFileInfoList subDirs = dir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot);
    int totalCount = subDirs.count();
    int deletecount = 0;
    int skipCount = 0;

    //    QProgressDialog progress("正在扫描目录...","取消",0,totalCount,nullptr);

    //    progress.setWindowModality(Qt::WindowModal);
    //    progress.setValue(0);
    //    progress.setWindowTitle("目录删除进度");

    for (int i = 0; i < totalCount; ++i)
    {

        //        progress.setValue(i);
        //        progress.setLabelText(QString("正在处理目录(%1/%2)").arg(i+1).arg(totalCount));
        //        if(progress.wasCanceled())
        //        {
        //            QMessageBox::information(nullptr,"取消","操作已被取消");
        //            return;
        //        }
        QFileInfo& subDirInfo = subDirs[i];
        QString dirName = subDirInfo.fileName();
        QDate dirDate = QDate::fromString(dirName, "yyyy-MM-dd");
        if (!dirDate.isValid())
        {
            skipCount++;
            continue;
        }
        QDateTime dirDateTime(dirDate, QTime(0, 0, 0));
        //检查日期条件(早于目标日期)
        if (dirDateTime < targettime)
        {
            QDir dirToRemove(subDirInfo.absoluteFilePath());
            if (dirToRemove.removeRecursively())
            {
                deletecount++;
            }
            else
            {
                skipCount++;
            }
        }
    }
    //    progress.setValue(totalCount);
    //    QString result = QString("操作完成:共扫描%1个子目录\n").arg(totalCount);
    //    result+=QString("已删除%1个目录\n").arg(deletecount);
    //    result+=QString("跳过%1个目录\n").arg(skipCount);

    //    if(deletecount==0)
    //    {
    //        result+="\n没有找到符合条件的目录需要删除";
    //    }
    //    QMessageBox::information(nullptr, "完成", result);
    qDebug() << "delect subDirs finshed!" << totalCount << deletecount << skipCount;
}

void MainWindow::slot_delect_lv1_data()
{
    if (systemconfigfdata->systemdata.lv1_data == true)
    {
        //删除所有早于当前时间之前的子目录
        QDateTime month_file = QDateTime::currentDateTime().addDays(-(systemconfigfdata->systemdata.lv1_day));

        deleteOldFiles(systemconfigfdata->systemdata.LV1_path + "/数据Lv2", month_file.toString("yyyy-MM-dd"));
    }
}

void MainWindow::slot_delect_lv2_data()
{
    if (systemconfigfdata->systemdata.lv2_data == true)
    {
        //删除所有早于当前时间之前的子目录
        QDateTime month_file = QDateTime::currentDateTime().addDays(-(systemconfigfdata->systemdata.lv2_day));

        deleteOldFiles(systemconfigfdata->systemdata.LV2_path + "/数据Lv2", month_file.toString("yyyy-MM-dd"));
    }
}

void MainWindow::slot_auto_delect_data()
{
    slot_delect_lv1_data();
    slot_delect_lv2_data();
}