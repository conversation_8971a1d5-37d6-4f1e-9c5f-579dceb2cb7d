﻿#ifndef LARGETABLEMODEL_H
#define LARGETABLEMODEL_H
#include <QDateTime>
#include <QApplication>
#include <QTableView>
#include <QAbstractTableModel>
#include <QSortFilterProxyModel>
#include <QScrollBar>
#include <QVector>
#include <QString>
#include <QElapsedTimer>
#include <QDebug>
#include <QFile>
#include <QCache>
class RowFilterProxyModel;
// 自定义表格模型（核心虚拟化实现）
class LargeTableModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    explicit LargeTableModel(QVector<QString> data, int rows = 0, QObject* parent = nullptr)
        : QAbstractTableModel(parent), totalRows(rows), totalCols(data.size())
    {
        // 初始化缓存（存储最近访问的1000行）
        header = data;
        cache.setCapacity(1000);
    }
    // 返回总行数
    int rowCount(const QModelIndex & = QModelIndex()) const override
    {
        return totalRows;
    }
    // 返回总列数
    int columnCount(const QModelIndex & = QModelIndex()) const override
    {
        return totalCols;
    }

    bool removeRows(int row, int count, const QModelIndex& parent = QModelIndex()) override
    {
        if (row < 0 || row >= m_data.size() || count <= 0 || (row + count) > m_data.size())
            return false;

        beginRemoveRows(parent, row, row + count - 1);
        // 删除数据
        for (int i = 0; i < count; ++i)
        {
            m_data.remove(row);
            if (!VerticalHeader.isEmpty())
            {
                VerticalHeader.remove(row);
            }
        }
        totalRows = m_data.size();
        endRemoveRows();
        cache.clear();
        return true;
    }

    QVariant data(const QModelIndex& index, int role) const override
    {
        if (!index.isValid() || role != Qt::DisplayRole)
            return QVariant();

        const int row = index.row();
        // 检查缓存
        if (cache.contains(row))
        {
            return cache.value(row)[index.column()];
        }

        // 检查数据是否存在
        if (row >= m_data.size())
        {
            return QString("未加载");
        }

        // 返回存储的数据
        return m_data[row][index.column()];
    }

    QVariant headerData(int section, Qt::Orientation orientation, int role) const override
    {
        if (role == Qt::DisplayRole && orientation == Qt::Horizontal)
        {
            return header[section];
        }
        //        if(role == Qt::DisplayRole&&orientation == Qt::Vertical)
        //        {
        //            return QString("k通道电压%1(mv)").arg(section+1);
        //        }
        return QAbstractTableModel::headerData(section, orientation, role);
    }

    bool loadFromCSV(const QString filePath)
    {
        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
        {
            qDebug() << "Open file failure!";
            return false;
        }
        beginResetModel(); // 通知视图模型即将重置

        m_data.clear();
        VerticalHeader.clear();
        cache.clear();

        QTextStream in(&file);
        int row = 0;
        if (!in.atEnd())
        {
            QString headerLine = in.readLine();
            QStringList headers = headerLine.split(",");
            // 跳过第一列“序号标题”
            if (headers.size() > 0 && headers[0] == "序号")
            {
                headers.removeFirst();
            }
            header = headers.toVector();
        }
        // 读取数据行
        while (!in.atEnd())
        {
            QString line = in.readLine();
            QStringList fileds = line.split(",");
            if (fileds.isEmpty())
                continue;
            VerticalHeader.append(VerticalHeader);
            fileds.removeFirst(); // 移除行标题
            // 处理数据列
            QVector<QString> rowData;
            for (const QString& filed : fileds)
            {
                rowData.append(filed);
            }
            m_data.append(rowData);
            row++;
        }

        totalRows = row;
        endResetModel();
        file.close();
        return true;
    }

    bool isLoaded() const
    {
        return !m_data.isEmpty();
    }

    int currentRowIndex() const
    {
        return m_data.size();
    }
    void resetLoadedState()
    {
        m_initiaload = false;
    }

    bool loadFromCSV(const QString filePath, bool isappend)
    {
        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
        {
            qDebug() << "Open file failure!";
            return false;
        }
        // 如果不是追加模式则重置数据
        if (!isappend)
        {
            beginResetModel(); // 通知视图模型即将重置
            m_data.clear();
            VerticalHeader.clear();
            cache.clear();
            totalRows = 0;
            // startRow=0;
        }

        QTextStream in(&file);
        int row = 0;
        QVector<QVector<QString>> newRows; // 存储实际数据
        QVector<QString> newVerticalHeader;
        if (!in.atEnd())
        {
            QString headerLine = in.readLine();
            // 首次加载或非追加模式时处理标题
            if (!isappend || m_data.isEmpty())
            {
                QStringList headers = headerLine.split(",");
                // 跳过第一列“序号标题”
                if (headers.size() > 0 && headers[0] == "序号")
                {
                    headers.removeFirst();
                }
                // 设置列标题
                header = headers.toVector();
            }
        }
        // 读取数据行
        while (!in.atEnd())
        {
            QString line = in.readLine();
            QStringList fileds = line.split(",");
            if (fileds.isEmpty())
                continue;

            QString verticalHeader = fileds.first(); // 行标题
            newVerticalHeader.append(verticalHeader);
            fileds.removeFirst(); // 移除行标题
            // 处理数据列
            QVector<QString> rowData;
            for (const QString& filed : fileds)
            {
                rowData.append(filed);
            }
            newRows.append(rowData);
            row++;
        }
        // 添加新数据
        if (isappend)
        {
            // 使用追加方法
            addData(newRows);
        }
        else
        {
            // 直接添加数据
            m_data.append(newRows);
            VerticalHeader.append(newVerticalHeader);
            totalRows = m_data.size();
            if (!isappend)
            {
                endResetModel();
            }
        }
        if (m_initiaload)
        {
            m_initiaload = false;
        }
        file.close();
        return true;
    }

public slots:
    // 添加数据接口
    void addData(const QVector<QVector<QString>>& newData)
    {
        if (newData.isEmpty())
            return;
        const int firstRow = m_data.size();
        const int lastRow = firstRow + newData.size() - 1;
        // 1. 通知视图开始插入
        beginInsertRows(QModelIndex(), firstRow, lastRow);

        // 2. 批量添加数据
        m_data.reserve(m_data.size() + newData.size());
        for (const auto& row : newData)
        {
            m_data.append(row);
        }

        // 3. 更新总行数
        totalRows = m_data.size();

        // 4. 通知视图结束插入
        endInsertRows();
    }

    // 添加单行数据
    void addRow(const QVector<QString>& rowData)
    {
        const int row = m_data.size();

        beginInsertRows(QModelIndex(), row, row);
        m_data.append(rowData);
        totalRows = m_data.size();
        endInsertRows();
    }

    // 获取单行数据
    QVector<QString> getRow(int row) const
    {
        if (row < 0 || row >= m_data.size())
        {
            return QVector<QString>();
        }
        return m_data[row];
    }

    // 获取选中行号
    int getSelectedRow(const QItemSelection& selection) const
    {

        if (selection.isEmpty())
        {
            return -1;
        }
        QSet<int> rowsToRemove;
        for (const QItemSelectionRange& rang : selection)
        {
            for (int row = rang.top(); row <= rang.bottom(); ++row)
            {
                return row;
            }
        }
        return -1;
    }

    // 替换指定行数据
    bool replaceRow(int row, const QVector<QString>& newData)
    {
        if (row < 0 || row >= m_data.size() || newData.size() != totalCols)
        {
            return false;
        }

        m_data[row] = newData;
        cache.remove(row); // 清除该行缓存

        QModelIndex topLeft = createIndex(row, 0);
        QModelIndex bottomRight = createIndex(row, totalCols - 1);
        emit dataChanged(topLeft, bottomRight);

        return true;
    }



    // 清空数据
    void clearData()
    {
        beginResetModel();
        m_data.clear();
        cache.clear();
        totalRows = 0;
        cache.clear();
        endResetModel();
    }
    void clear1rowData()
    {
        beginResetModel();
        m_data.removeFirst();
        totalRows = totalRows - 1;
        cache.clear();
        endResetModel();
    }

    // 选中当前行删除
    void removeSelectedRows(const QItemSelection& selection)
    {
        if (selection.isEmpty())
        {
            return;
        }
        QSet<int> rowsToRemove;
        for (const QItemSelectionRange& rang : selection)
        {
            for (int row = rang.top(); row <= rang.bottom(); ++row)
            {
                rowsToRemove.insert(row);
            }
        }
        QList<int> sortedRows = rowsToRemove.values();
        std::sort(sortedRows.begin(), sortedRows.end(), std::greater<int>());

        // 批量删除行
        for (int row : sortedRows)
        {
            removeRow(row);
        }
    }
    QDateTime rowTime(int row) const
    {
        if (row < 0 || row >= m_data.size() || m_data[row].size() < 1)
        {
            return QDateTime();
        }
        // 解析第一列时间字符串
        return QDateTime::fromString(m_data[row][0], "yyyy-MM-dd hh:mm:ss");
    }

private:
    // 轻量级缓存（FIFO策略）
    class DataCache
    {
    public:
        void setCapacity(int cap) { capacity = cap; }
        bool contains(int key) const { return cache.contains(key); }
        QVector<QString> value(int key) const { return cache.value(key); }

        void insert(int key, const QVector<QString>& value)
        {
            if (cache.size() >= capacity)
            {
                cache.remove(keys.first());
                keys.removeFirst();
            }
            cache.insert(key, value);
            keys.append(key);
        }

        void remove(int key)
        {
            cache.remove(key);
            keys.removeOne(key);
        }

        void clear()
        {
            cache.clear();
            keys.clear();
        }

    private:
        int capacity = 1000;
        QMap<int, QVector<QString>> cache;
        QList<int> keys;
    };

    int totalRows;
    int totalCols;
    QVector<QVector<QString>> m_data; // 存储实际数据
    QVector<QString> header;          // 水平表头
    QVector<QString> VerticalHeader;
    mutable DataCache cache;

    bool m_initiaload = false;
};
// 动态隐藏行数
class RowFilterProxyModel : public QSortFilterProxyModel
{
    Q_OBJECT
public:
    explicit RowFilterProxyModel(QVector<QString> data, int rows = 0, int cols = 9, QObject* parent = nullptr)
        : QSortFilterProxyModel(parent), totalRows(rows), totalCols(cols)
    {
        // 初始化缓存（存储最近访问的1000行）
        header = data;
    }
    // 设置时间过滤值
    void setTimeThresholdHous(int hours)
    {
        if (m_thresholdHous != hours)
        {
            m_thresholdHous = hours;
            invalidateFilter(); // 应用过滤
        }
    }
    // 获取当前过滤阈值
    int timeThresholdHous()
    {
        return m_thresholdHous;
    }

    void setHiddenRows(const QSet<int>& rows)
    {
        if (m_hiddernRows != rows)
        {
            m_hiddernRows = rows;
            invalidateFilter();
        }
    }
    // 切换单行可见性
    void toggleRow(int source_row)
    {
        if (m_hiddernRows.contains(source_row))
        {
            m_hiddernRows.remove(source_row);
        }
        else
        {
            m_hiddernRows.insert(source_row);
        }
        invalidateFilter();
    }
    // 检查行是否隐藏
    bool isRowHidden(int source_row)
    {
        return m_hiddernRows.contains((source_row));
    }
    // 获取所有隐藏行
    QSet<int> hiddenRows() const
    {
        return m_hiddernRows;
    }

protected:
    bool filterAcceptsRow(int source_row, const QModelIndex& source_parent) const override
    {
        //        Q_UNUSED(source_parent);
        //        return !m_hiddernRows.contains((source_row));
        if (m_thresholdHous <= 0)
        {
            return true;
        }
        LargeTableModel* tablemodel = qobject_cast<LargeTableModel*>(sourceModel());
        if (!tablemodel || tablemodel->rowCount() == 0)
            return true;
        // 获取基准时间 第一行的数据
        if (!m_baseTime.isValid())
        {
            m_baseTime = tablemodel->rowTime(0);
        }
        // 获取当前行的时间
        QDateTime rowTime = tablemodel->rowTime(source_row);
        // 计算时间差
        qint64 hoursDiff = m_baseTime.secsTo(rowTime);

        return hoursDiff <= m_thresholdHous;
    }

private:
    QSet<int> m_hiddernRows;

    int totalRows;
    int totalCols;

    int m_thresholdHous = 0;
    mutable QDateTime m_baseTime; // 基准时间

    QVector<QString> header;
};
#endif // LARGETABLEMODEL_H
