#include "SystemConfigurationDialog.h"

#include "qcolordialog.h"
#include "tools/Configdata/CSystemConfigData.h"
#include "tools/Global.h"
#include <QMessageBox>
#include <QProgressDialog>
using namespace std;
SystemConfigurationDialog::SystemConfigurationDialog(QWidget* parent) : QDialog(parent),
ui(new Ui::SystemConfigurationDialog)
{
    ui->setupUi(this);
    this->setWindowTitle("系统配置");
    this->setWindowFlags(this->windowFlags() | Qt::WindowMaximizeButtonHint);
    this->setWindowFlags(Qt::Window);

    colorcard = new CColorCardDialog(this);

    set_SystemConfiguration_styleSheet(systemconfigfdata->systemdata.set_skin);

    ////状态数据
    //struct FILENAME file_name3;
    //file_name3.of1ag = "I";
    //file_name3.originator = "545112";
    //file_name3.equipmenttype = "AD002";
    //QString file_path3 = systemconfigfdata->systemdata.status_path;
    //device_status_data_storage = new Device_status_data_storage(file_path3, file_name3);
    ////定标数据
    //struct FILENAME file_name4;
    //file_name4.of1ag = "I";
    //file_name4.originator = "545112";
    //file_name4.equipmenttype = "AD002";
    //QString file_path4 = systemconfigfdata->systemdata.dingbiao_path;
    //calibrate_data_storage = new Calibrate_data_storage(file_path4, file_name4);


    buttonGroup = new QButtonGroup(this);
    buttonGroup->setExclusive(true); // 设置互斥

    buttonGroup->addButton(ui->pushButton_bright_set);
    buttonGroup->addButton(ui->pushButton_data);
    buttonGroup->addButton(ui->pushButton_ftp_file_set);
    buttonGroup->addButton(ui->pushButton_network_set);
    buttonGroup->addButton(ui->pushButton_save_data_path);
    buttonGroup->addButton(ui->pushButton_skin_set);
    buttonGroup->addButton(ui->pushButton_view_set);
    buttonGroup->addButton(ui->pushButton_station_set);

    foreach(QAbstractButton * btn, buttonGroup->buttons())
    {
        btn->setCheckable(true);
        btn->setFocusPolicy(Qt::NoFocus);
        btn->installEventFilter(this);
    }
    connect(ui->pushButton_save_data_path, &QPushButton::clicked, [=](bool checked)
        {
            if (checked)
            {
                ui->stackedWidget->setCurrentIndex(0);
            } });
    connect(ui->pushButton_skin_set, &QPushButton::clicked, [=](bool checked)
        {
            if (checked)
            {
                ui->stackedWidget->setCurrentIndex(1);
            } });
    connect(ui->pushButton_station_set, &QPushButton::clicked, [=](bool checked)
        {
            if (checked)
            {
                ui->stackedWidget->setCurrentIndex(2);
            } });
    connect(ui->pushButton_data, &QPushButton::clicked, [=](bool checked)
        {
            if (checked)
            {
                ui->stackedWidget->setCurrentIndex(3);
            } });

    connect(ui->pushButton_network_set, &QPushButton::clicked, [=](bool checked)
        {
            if (checked)
            {
                ui->stackedWidget->setCurrentIndex(4);
            } });
    connect(ui->pushButton_view_set, &QPushButton::clicked, [=](bool checked)
        {
            if (checked)
            {
                ui->stackedWidget->setCurrentIndex(5);
            } });
    connect(ui->pushButton_bright_set, &QPushButton::clicked, [=](bool checked)
        {
            if (checked)
            {
                ui->stackedWidget->setCurrentIndex(6);
            } });
    connect(ui->pushButton_ftp_file_set, &QPushButton::clicked, [=](bool checked)
        {
            if (checked)
            {
                ui->stackedWidget->setCurrentIndex(7);
            } });
    

    connect(ui->pushButton_hand, &QPushButton::clicked, this,&SystemConfigurationDialog::slot_hand_calibration);

    auto_delect_data = new QTimer();
    connect(auto_delect_data, SIGNAL(timeout()), this, SLOT(slot_auto_delect_data()));

    auto_delect_data->start(1000);

    changesystime = new ChangeSysTimer(this);
    auto_save_status = new QTimer();

    //connect(auto_save_status, SIGNAL(timeout()), this, SLOT(slot_auto_save_data()));

    

    connect(ui->checkBox_auto, &QCheckBox::clicked, [=](bool checked) {
        bool stystem_status = ui->checkBox_auto->isChecked();
        if (checked == true)
        {
            changesystime->setEnable_Autotiming(true);
        }
        else
        {
            changesystime->setEnable_Autotiming(false);
        }
        
        });

    //    connect(ui->checkBox_lv1, &QCheckBox::clicked,this ,&SystemConfigurationDialog::slot_delect_lv1_data);
    //    connect(ui->checkBox_lv2, &QCheckBox::clicked,this ,&SystemConfigurationDialog::slot_delect_lv2_data);
    connect(ui->checkBox_sensor_status, &QCheckBox::clicked, this, &SystemConfigurationDialog::slot_delect_device_status_data);
    //    connect(ui->checkBox_daily_maintenance, &QCheckBox::clicked,this ,&SystemConfigurationDialog::slot_daily_maintenance_data);
    //    connect(ui->checkBox_fault_management, &QCheckBox::clicked,this ,&SystemConfigurationDialog::slot_fault_management_data);
    //    connect(ui->checkBox_system_maintenance, &QCheckBox::clicked,this ,&SystemConfigurationDialog::slot_system_maintenance_data);
    //    connect(ui->checkBox_upload_monitor, &QCheckBox::clicked,this ,&SystemConfigurationDialog::slot_delect_upload_monitor_data);
    //    connect(ui->checkBox_log_file, &QCheckBox::clicked,this ,&SystemConfigurationDialog::slot_delect_log_file_data);
    connect(ui->checkBox_calibration, &QCheckBox::clicked, this, &SystemConfigurationDialog::slot_delect_calibration_file_data);
    // LV2数据绘制
    /*d_plot_s = new Plot(1, this);
    d_plot2_s = new Plot(1, this);
    d_plot3_s = new Plot(1, this);
    d_plot4_s = new Plot(2, this);*/
    connect(ui->comboBox_dis_rang, SIGNAL(currentIndexChanged(int)), this, SLOT(slot_dis_rang(int)));
    connect(ui->comboBox_time_rang, SIGNAL(currentIndexChanged(int)), this, SLOT(slot_time_rang(int)));

}

SystemConfigurationDialog::~SystemConfigurationDialog()
{
    delete ui;
    delete colorcard;
}

void SystemConfigurationDialog::slot_save_Lv1_Data()
{
    getUiParam();
}

void SystemConfigurationDialog::deleteOldFiles(QString currentFolder, QString time)
{
    if (!QDateTime::fromString(time, "yyyy-MM-dd").isValid())
    {
        return;
    }

    QDateTime targettime = QDateTime::fromString(time, "yyyy-MM-dd");
    QDir dir(currentFolder);
    if (!dir.exists())
    {
        //QMessageBox::warning(nullptr, "警告", QString("文件路径不存在:\n%1").arg(currentFolder));
        return;
    }
    QFileInfoList subDirs = dir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot);
    int totalCount = subDirs.count();
    int deletecount = 0;
    int skipCount = 0;

    //    QProgressDialog progress("正在扫描目录...","取消",0,totalCount,nullptr);

    //    progress.setWindowModality(Qt::WindowModal);
    //    progress.setValue(0);
    //    progress.setWindowTitle("目录删除进度");

    for (int i = 0; i < totalCount; ++i)
    {

        //        progress.setValue(i);
        //        progress.setLabelText(QString("正在处理目录(%1/%2)").arg(i+1).arg(totalCount));
        //        if(progress.wasCanceled())
        //        {
        //            QMessageBox::information(nullptr,"取消","操作已被取消");
        //            return;
        //        }
        QFileInfo& subDirInfo = subDirs[i];
        QString dirName = subDirInfo.fileName();
        QDate dirDate = QDate::fromString(dirName, "yyyy-MM-dd");
        if (!dirDate.isValid())
        {
            skipCount++;
            continue;
        }
        QDateTime dirDateTime(dirDate, QTime(0, 0, 0));
        //检查日期条件(早于目标日期)
        if (dirDateTime < targettime)
        {
            QDir dirToRemove(subDirInfo.absoluteFilePath());
            if (dirToRemove.removeRecursively())
            {
                deletecount++;
            }
            else
            {
                skipCount++;
            }
        }
    }
    //    progress.setValue(totalCount);
    //    QString result = QString("操作完成:共扫描%1个子目录\n").arg(totalCount);
    //    result+=QString("已删除%1个目录\n").arg(deletecount);
    //    result+=QString("跳过%1个目录\n").arg(skipCount);

    //    if(deletecount==0)
    //    {
    //        result+="\n没有找到符合条件的目录需要删除";
    //    }
        //QMessageBox::information(nullptr, "完成", result);
    qDebug() << "delect subDirs finshed!" << totalCount << deletecount << skipCount;
}

void SystemConfigurationDialog::slot_delect_device_status_data()
{
    if (ui->checkBox_sensor_status->isChecked() == true)
    {
        //删除所有早于当前时间之前的子目录
        QDateTime month_file = QDateTime::currentDateTime().addDays(-(ui->doubleSpinBox_sensor_status->value()));

        deleteOldFiles(ui->lineEdit_status_path->text() + "/状态数据", month_file.toString("yyyy-MM-dd"));
    }
}

void SystemConfigurationDialog::slot_delect_calibration_file_data()
{
    if (ui->checkBox_calibration->isChecked() == true)
    {
        //删除所有早于当前时间之前的子目录
        QDateTime month_file = QDateTime::currentDateTime().addDays(-(ui->doubleSpinBox_calibration->value()));

        deleteOldFiles(ui->lineEdit_dingbiao_path->text() + "/定标数据", month_file.toString("yyyy-MM-dd"));
    }
}

void SystemConfigurationDialog::slot_auto_delect_data()//自动清理
{
    slot_delect_device_status_data();
    slot_delect_calibration_file_data();
}

void SystemConfigurationDialog::slot_auto_save_data()
{
    
}

void SystemConfigurationDialog::LoadStyleFile(QString strStyle_path)
{
    QFile qss(strStyle_path);
    if (!qss.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        qWarning() << " Cannot open styleSheet file:" << strStyle_path;
        return;
    }
    QString styleSheet = QString::fromUtf8(qss.readAll());

    this->setStyleSheet(styleSheet);
    qss.close();
}
void SystemConfigurationDialog::set_SystemConfiguration_styleSheet(int index)
{

    if (index == 0)
    {
        LoadStyleFile(":/Topic_skin_Settings/SystemConfigurationDialogSkin/skin1.qss");

    }
    else if (index == 1)
    {
        LoadStyleFile(":/Topic_skin_Settings/SystemConfigurationDialogSkin/skin2.qss");

    }
}

bool SystemConfigurationDialog::eventFilter(QObject* watched, QEvent* event)
{
    if (event->type() == QEvent::MouseButtonRelease)
    {
        if (auto* btn = qobject_cast<QPushButton*>(watched))
        {
            btn->setChecked(!btn->isChecked());
        }
    }
    return QDialog::eventFilter(watched, event);
}


void SystemConfigurationDialog::on_comboBox_set_skin_currentIndexChanged(int index) // 设置皮肤背景颜色
{

    set_SystemConfiguration_styleSheet(index);
    //emit set_background_skin(index); // 其他界面同步的设置背景颜色信号
}
void SystemConfigurationDialog::setUiParam()
{
    // 数据存储路径设置
    ui->lineEdit_save_log_path->setText(systemconfigfdata.systemdata.save_log_path);
    ui->lineEdit_dingbiao_path->setText(systemconfigfdata.systemdata.dingbiao_path);
    ui->lineEdit_status_path->setText(systemconfigfdata.systemdata.status_path);
    ui->lineEdit_LV1_path->setText(systemconfigfdata.systemdata.LV1_path);
    ui->lineEdit_LV2_path->setText(systemconfigfdata.systemdata.LV2_path);
    ui->lineEdit_ceyun_path->setText(systemconfigfdata.systemdata.ceyun_path);
    ui->comboBox_lv1->setCurrentIndex(systemconfigfdata.systemdata.lv1_file_format);
    ui->comboBox_lv2->setCurrentIndex(systemconfigfdata.systemdata.lv2_file_format);

    ui->comboBox_Device_status->setCurrentIndex(systemconfigfdata.systemdata.status_data_file_format);
    ui->comboBox_Calibrate->setCurrentIndex(systemconfigfdata.systemdata.calibrate_data_file_format);
    ui->comboBox_Device_status_save_mode->setCurrentIndex(systemconfigfdata.systemdata.status_data_file_format_xml);
    ui->comboBox_Calibrate_save_mode->setCurrentIndex(systemconfigfdata.systemdata.calibrate_data_file_format_xml);
    // 皮肤设置
    ui->comboBox_set_skin->setCurrentIndex(systemconfigfdata.systemdata.set_skin);

    ui->pushButton_dead_error->setStyleSheet(QString("#pushButton_dead_error{background-color:%1;}").arg(systemconfigfdata.systemdata.dead_error));
    ui->pushButton_general_information->setStyleSheet(QString("#pushButton_general_information{background-color:%1;}").arg(systemconfigfdata.systemdata.general_information));
    ui->pushButton_general_error->setStyleSheet(QString("#pushButton_general_error{background-color:%1;}").arg(systemconfigfdata.systemdata.general_error));
    ui->pushButton_debug_information->setStyleSheet(QString("#pushButton_debug_information{background-color:%1;}").arg(systemconfigfdata.systemdata.debug_information));
    ui->pushButton_warn_information->setStyleSheet(QString("#pushButton_warn_information{background-color:%1;}").arg(systemconfigfdata.systemdata.warn_information_error));
    //    ui->pushButton_dead_error->setStyleSheet(systemconfigfdata.systemdata.dead_error);
    //    ui->pushButton_general_information->setStyleSheet(systemconfigfdata.systemdata.general_information);
    //    ui->pushButton_general_error->setStyleSheet(systemconfigfdata.systemdata.general_error);
    //    ui->pushButton_debug_information->setStyleSheet(systemconfigfdata.systemdata.debug_information);
    //    ui->pushButton_warn_information->setStyleSheet(systemconfigfdata.systemdata.warn_information_error);
        // 站点设置
    ui->lineEdit_shengming->setText(systemconfigfdata.systemdata.shengming);
    ui->lineEdit_station_name->setText(systemconfigfdata.systemdata.station_name);
    ui->lineEdit_station_number->setText(systemconfigfdata.systemdata.station_number);
    ui->lineEdit_weather_number->setText(systemconfigfdata.systemdata.weather_number);
    ui->lineEdit_business_number->setText(systemconfigfdata.systemdata.business_number);
    ui->doubleSpinBox_ground_hieght->setValue(systemconfigfdata.systemdata.ground_hieght);
    ui->doubleSpinBox_mast_height->setValue(systemconfigfdata.systemdata.mast_height);
    ui->doubleSpinBox_point->setValue(systemconfigfdata.systemdata.point);
    ui->lineEdit_longitude_angle->setText(systemconfigfdata.systemdata.longitude_angle);
    ui->lineEdit_longitude_minute->setText(systemconfigfdata.systemdata.longitude_minute);
    ui->lineEdit_longitude_second->setText(systemconfigfdata.systemdata.longitude_second);
    ui->lineEdit_latitude_angle->setText(systemconfigfdata.systemdata.latitude_angle);
    ui->lineEdit_latitude_minute->setText(systemconfigfdata.systemdata.latitude_minute);
    ui->lineEdit_latitude_second->setText(systemconfigfdata.systemdata.latitude_second);
    // 数据管理
    ui->doubleSpinBox_data_save_time->setValue(systemconfigfdata.systemdata.data_save_time);
    ui->doubleSpinBox_space_limit->setValue(systemconfigfdata.systemdata.space_limit);
    //ui->doubleSpinBox_jiao_hour->setValue(systemconfigfdata.systemdata.jiao_hour);
    //ui->doubleSpinBox_jiao_minute->setValue(systemconfigfdata.systemdata.jiao_minute);
    //ui->doubleSpinBox_jiao_second->setValue(systemconfigfdata.systemdata.jiao_second);
    ui->checkBox_auto->setChecked(systemconfigfdata.systemdata.auto_time_calibration);


    // 网络通信设置
    ui->lineEdit_local_network_ip->setText(systemconfigfdata.systemdata.local_network_ip);
    ui->doubleSpinBox_local_network_port->setValue(systemconfigfdata.systemdata.local_network_port);
    ui->lineEdit_long_network_ip->setText(systemconfigfdata.systemdata.long_network_ip);
    ui->doubleSpinBox_long_network_port->setValue(systemconfigfdata.systemdata.long_network_port);
    // 视图管理设置
    ui->comboBox_time_rang->setCurrentIndex(systemconfigfdata.systemdata.time_rang);
    ui->comboBox_dis_rang->setCurrentIndex(systemconfigfdata.systemdata.dis_rang);
    // 亮温质量控制设置
    ui->lineEdit_yuzhi1->setText(systemconfigfdata.systemdata.yuzhi1);
    ui->lineEdit_yuzhi2->setText(systemconfigfdata.systemdata.yuzhi2);
    ui->lineEdit_min_value->setText(systemconfigfdata.systemdata.min_value);
    ui->lineEdit_max_value->setText(systemconfigfdata.systemdata.max_value);
    ui->lineEdit_difference_value->setText(systemconfigfdata.systemdata.difference_value);

    ui->checkBox_logic->setChecked(systemconfigfdata.systemdata.logic);
    ui->checkBox_min_bianlv->setChecked(systemconfigfdata.systemdata.min_bianlv);
    ui->checkBox_water->setChecked(systemconfigfdata.systemdata.water);
    ui->checkBox_gender->setChecked(systemconfigfdata.systemdata.gender);
    ui->checkBox_peak->setChecked(systemconfigfdata.systemdata.peak);
    ui->lineEdit_time->setText(systemconfigfdata.systemdata.rainfall_time);



    // FTP文件设置
//    ui->lineEdit_ftp_ip->setText(systemconfigfdata.systemdata.ftp_ip);
//    ui->lineEdit_ftp_user->setText(systemconfigfdata.systemdata.ftp_user);
//    ui->lineEdit_ftp_port->setText(systemconfigfdata.systemdata.ftp_port);
//    ui->lineEdit_ftp_password->setText(systemconfigfdata.systemdata.ftp_password);
//    ui->lineEdit_ftp_local_upload_catalogue->setText(systemconfigfdata.systemdata.ftp_local_upload_catalogue);
//    ui->lineEdit_ftp_long_upload_catalogue->setText(systemconfigfdata.systemdata.ftp_long_download_catalogue);
//    ui->lineEdit_ftp_upload_interval->setText(systemconfigfdata.systemdata.ftp_upload_interval);
//    ui->lineEdit_ftp_upload_interval->setText(systemconfigfdata.systemdata.ftp_local_download_catalogue);
//    ui->lineEdit_ftp_long_download_catalogue->setText(systemconfigfdata.systemdata.ftp_long_download_catalogue);
    ui->checkBox_lv1->setChecked(systemconfigfdata.systemdata.lv1_data);
    ui->checkBox_lv2->setChecked(systemconfigfdata.systemdata.lv2_data);
    ui->checkBox_sensor_status->setChecked(systemconfigfdata.systemdata.sensor_status_data);
    ui->checkBox_daily_maintenance->setChecked(systemconfigfdata.systemdata.daily_maintenance_data);
    ui->checkBox_fault_management->setChecked(systemconfigfdata.systemdata.fault_management_data);
    ui->checkBox_system_maintenance->setChecked(systemconfigfdata.systemdata.system_maintenance_data);
    ui->checkBox_upload_monitor->setChecked(systemconfigfdata.systemdata.upload_monitor_data);
    ui->checkBox_log_file->setChecked(systemconfigfdata.systemdata.log_data);
    ui->checkBox_calibration->setChecked(systemconfigfdata.systemdata.calibration_data);

    ui->doubleSpinBox_lv1->setValue(systemconfigfdata.systemdata.lv1_day);
    ui->doubleSpinBox_lv2->setValue(systemconfigfdata.systemdata.lv2_day);
    ui->doubleSpinBox_sensor_status->setValue(systemconfigfdata.systemdata.sensor_status_day);
    ui->doubleSpinBox_daily_maintenance->setValue(systemconfigfdata.systemdata.daily_maintenance_day);
    ui->doubleSpinBox_fault_management->setValue(systemconfigfdata.systemdata.fault_management_day);
    ui->doubleSpinBox_system_maintenance->setValue(systemconfigfdata.systemdata.system_maintenance_day);
    ui->doubleSpinBox_upload_monitor->setValue(systemconfigfdata.systemdata.upload_monitor_day);
    ui->doubleSpinBox_log_file->setValue(systemconfigfdata.systemdata.log_day);
    ui->doubleSpinBox_calibration->setValue(systemconfigfdata.systemdata.calibration_day);

    // 设备关键参数设置
}

void SystemConfigurationDialog::getUiParam()
{
    // 数据存储路径设置
    systemconfigfdata.systemdata.save_log_path = ui->lineEdit_save_log_path->text();
    systemconfigfdata.systemdata.dingbiao_path = ui->lineEdit_dingbiao_path->text();
    systemconfigfdata.systemdata.status_path = ui->lineEdit_status_path->text();
    systemconfigfdata.systemdata.LV1_path = ui->lineEdit_LV1_path->text();
    systemconfigfdata.systemdata.LV2_path = ui->lineEdit_LV2_path->text();
    systemconfigfdata.systemdata.ceyun_path = ui->lineEdit_ceyun_path->text();

    systemconfigfdata.systemdata.status_data_file_format = ui->comboBox_Device_status->currentIndex();
    systemconfigfdata.systemdata.calibrate_data_file_format = ui->comboBox_Calibrate->currentIndex();
    systemconfigfdata.systemdata.status_data_file_format_xml = ui->comboBox_Device_status_save_mode->currentIndex();
    systemconfigfdata.systemdata.calibrate_data_file_format_xml = ui->comboBox_Calibrate_save_mode->currentIndex();


    // 皮肤设置
    systemconfigfdata.systemdata.set_skin = ui->comboBox_set_skin->currentIndex();

    systemconfigfdata.systemdata.dead_error = rgbStr_dead_error;
    systemconfigfdata.systemdata.general_information = rgbStr_general_information;
    systemconfigfdata.systemdata.general_error = rgbStr_general_error;
    systemconfigfdata.systemdata.debug_information = rgbStr_debug_information;
    systemconfigfdata.systemdata.warn_information_error = rgbStr_warn_information_error;
    //    systemconfigfdata.systemdata.dead_error = ui->pushButton_dead_error->styleSheet();
    //    systemconfigfdata.systemdata.general_information = ui->pushButton_general_information->styleSheet();
    //    systemconfigfdata.systemdata.general_error = ui->pushButton_general_error->styleSheet();
    //    systemconfigfdata.systemdata.debug_information = ui->pushButton_debug_information->styleSheet();
    //    systemconfigfdata.systemdata.warn_information_error =ui->pushButton_warn_information->styleSheet();

        // 站点设置
    systemconfigfdata.systemdata.shengming = ui->lineEdit_shengming->text();
    systemconfigfdata.systemdata.station_name = ui->lineEdit_station_name->text();
    systemconfigfdata.systemdata.station_number = ui->lineEdit_station_number->text();
    systemconfigfdata.systemdata.weather_number = ui->lineEdit_weather_number->text();
    systemconfigfdata.systemdata.business_number = ui->lineEdit_business_number->text();
    systemconfigfdata.systemdata.ground_hieght = ui->doubleSpinBox_ground_hieght->value();
    systemconfigfdata.systemdata.mast_height = ui->doubleSpinBox_mast_height->value();
    systemconfigfdata.systemdata.point = ui->doubleSpinBox_point->value();
    systemconfigfdata.systemdata.longitude_angle = ui->lineEdit_longitude_angle->text();
    systemconfigfdata.systemdata.longitude_minute = ui->lineEdit_longitude_minute->text();
    systemconfigfdata.systemdata.longitude_second = ui->lineEdit_longitude_second->text();
    systemconfigfdata.systemdata.latitude_angle = ui->lineEdit_latitude_angle->text();
    systemconfigfdata.systemdata.latitude_minute = ui->lineEdit_latitude_minute->text();
    systemconfigfdata.systemdata.latitude_second = ui->lineEdit_latitude_second->text();
    // 数据管理
    systemconfigfdata.systemdata.data_save_time = ui->doubleSpinBox_data_save_time->value();
    systemconfigfdata.systemdata.space_limit = ui->doubleSpinBox_space_limit->value();
    //systemconfigfdata.systemdata.jiao_hour = ui->doubleSpinBox_jiao_hour->value();
    //systemconfigfdata.systemdata.jiao_minute = ui->doubleSpinBox_jiao_minute->value();
    //systemconfigfdata.systemdata.jiao_second = ui->doubleSpinBox_jiao_second->value();

    systemconfigfdata.systemdata.auto_time_calibration=ui->checkBox_auto->isChecked();

    // 网络通信设置
    systemconfigfdata.systemdata.local_network_ip = ui->lineEdit_local_network_ip->text();
    systemconfigfdata.systemdata.local_network_port = ui->doubleSpinBox_local_network_port->value();
    systemconfigfdata.systemdata.long_network_ip = ui->lineEdit_long_network_ip->text();
    systemconfigfdata.systemdata.long_network_port = ui->doubleSpinBox_long_network_port->value();
    // 视图管理设置

    systemconfigfdata.systemdata.time_rang = ui->comboBox_time_rang->currentIndex();
    systemconfigfdata.systemdata.dis_rang = ui->comboBox_dis_rang->currentIndex();
    // 亮温质量控制设置
    systemconfigfdata.systemdata.yuzhi1 = ui->lineEdit_yuzhi1->text();
    systemconfigfdata.systemdata.yuzhi2 = ui->lineEdit_yuzhi2->text();
    systemconfigfdata.systemdata.min_value = ui->lineEdit_min_value->text();
    systemconfigfdata.systemdata.max_value = ui->lineEdit_max_value->text();
    systemconfigfdata.systemdata.difference_value = ui->lineEdit_difference_value->text();

    systemconfigfdata.systemdata.logic = ui->checkBox_logic->isChecked();
    systemconfigfdata.systemdata.min_bianlv = ui->checkBox_min_bianlv->isChecked();
    systemconfigfdata.systemdata.water = ui->checkBox_water->isChecked();
    systemconfigfdata.systemdata.gender = ui->checkBox_gender->isChecked();
    systemconfigfdata.systemdata.peak = ui->checkBox_peak->isChecked();
    systemconfigfdata.systemdata.rainfall_time = ui->lineEdit_time->text();

    // FTP文件设置
//    systemconfigfdata.systemdata.ftp_ip = ui->lineEdit_ftp_ip->text();
//    systemconfigfdata.systemdata.ftp_user = ui->lineEdit_ftp_user->text();
//    systemconfigfdata.systemdata.ftp_port = ui->lineEdit_ftp_port->text();
//    systemconfigfdata.systemdata.ftp_password = ui->lineEdit_ftp_password->text();
//    systemconfigfdata.systemdata.ftp_local_upload_catalogue = ui->lineEdit_ftp_local_upload_catalogue->text();
//    systemconfigfdata.systemdata.ftp_long_download_catalogue = ui->lineEdit_ftp_long_upload_catalogue->text();
//    systemconfigfdata.systemdata.ftp_upload_interval = ui->lineEdit_ftp_upload_interval->text();
//    systemconfigfdata.systemdata.ftp_local_download_catalogue = ui->lineEdit_ftp_upload_interval->text();
//    systemconfigfdata.systemdata.ftp_long_download_catalogue = ui->lineEdit_ftp_long_download_catalogue->text();
    systemconfigfdata.systemdata.lv1_data = ui->checkBox_lv1->isChecked();
    systemconfigfdata.systemdata.lv2_data = ui->checkBox_lv2->isChecked();
    systemconfigfdata.systemdata.sensor_status_data = ui->checkBox_sensor_status->isChecked();
    systemconfigfdata.systemdata.daily_maintenance_data = ui->checkBox_daily_maintenance->isChecked();
    systemconfigfdata.systemdata.fault_management_data = ui->checkBox_fault_management->isChecked();
    systemconfigfdata.systemdata.system_maintenance_data = ui->checkBox_system_maintenance->isChecked();
    systemconfigfdata.systemdata.upload_monitor_data = ui->checkBox_upload_monitor->isChecked();
    systemconfigfdata.systemdata.log_data = ui->checkBox_log_file->isChecked();
    systemconfigfdata.systemdata.calibration_data = ui->checkBox_calibration->isChecked();

    systemconfigfdata.systemdata.lv1_day = ui->doubleSpinBox_lv1->value();
    systemconfigfdata.systemdata.lv2_day = ui->doubleSpinBox_lv2->value();
    systemconfigfdata.systemdata.sensor_status_day = ui->doubleSpinBox_sensor_status->value();
    systemconfigfdata.systemdata.daily_maintenance_day = ui->doubleSpinBox_daily_maintenance->value();
    systemconfigfdata.systemdata.fault_management_day = ui->doubleSpinBox_fault_management->value();
    systemconfigfdata.systemdata.system_maintenance_day = ui->doubleSpinBox_system_maintenance->value();
    systemconfigfdata.systemdata.upload_monitor_day = ui->doubleSpinBox_upload_monitor->value();
    systemconfigfdata.systemdata.log_day = ui->doubleSpinBox_log_file->value();
    systemconfigfdata.systemdata.calibration_day = ui->doubleSpinBox_calibration->value();

    // 设备关键参数设置

    systemconfigfdata.saveToFile();
}

void SystemConfigurationDialog::on_MWToOther(int mode)
{
    if (mode == 0)
    {
        ui->pushButton_save_data_path->clicked(true);
        ui->pushButton_save_data_path->setChecked(true);
    }
    else if (mode == 1)
    {
        ui->pushButton_skin_set->clicked(true);
        ui->pushButton_skin_set->setChecked(true);
    }
    else if (mode == 2)
    {
        ui->pushButton_station_set->clicked(true);
        ui->pushButton_station_set->setChecked(true);
    }
    else if (mode == 3)
    {
        ui->pushButton_data->clicked(true);
        ui->pushButton_data->setChecked(true);
    }
    else if (mode == 4)
    {
        ui->pushButton_network_set->clicked(true);
        ui->pushButton_network_set->setChecked(true);
    }
    else if (mode == 5)
    {
        ui->pushButton_view_set->clicked(true);
        ui->pushButton_view_set->setChecked(true);
    }
    else if (mode == 6)
    {
        ui->pushButton_bright_set->clicked(true);
        ui->pushButton_bright_set->setChecked(true);
    }
    else if (mode == 7)
    {
        ui->pushButton_ftp_file_set->clicked(true);
        ui->pushButton_ftp_file_set->setChecked(true);
    }
}

void SystemConfigurationDialog::on_pushButton_dead_error_clicked()
{
    QColor color = QColorDialog::getColor(Qt::red, nullptr, "选择颜色");
    if (!color.isValid())
    {
        return;
    }
    color_dead_error = color;
    str_dead_error = "致命错误";
    rgbStr_dead_error = QString("rgb(%1,%2,%3)").arg(color.red()).arg(color.green()).arg(color.blue());

    ui->pushButton_dead_error->setStyleSheet(QString("QPushButton{background-color:rgb(%1,%2,%3);}").arg(color.red()).arg(color.green()).arg(color.blue()));
    getUiParam();
}

void SystemConfigurationDialog::on_pushButton_general_information_clicked()
{
    QColor color = QColorDialog::getColor(Qt::red, nullptr, "选择颜色");

    if (!color.isValid())
    {
        return;
    }
    color_general_information = color;
    str_general_information = "一般信息";
    rgbStr_general_information = QString("rgb(%1,%2,%3)").arg(color.red()).arg(color.green()).arg(color.blue());

    ui->pushButton_general_information->setStyleSheet(QString("QPushButton{background-color:rgb(%1,%2,%3);}").arg(color.red()).arg(color.green()).arg(color.blue()));
    getUiParam();
}

void SystemConfigurationDialog::on_pushButton_general_error_clicked()
{
    QColor color = QColorDialog::getColor(Qt::red, nullptr, "选择颜色");
    if (!color.isValid())
    {
        return;
    }
    str_general_error = "一般错误";
    color_general_error = color;
    rgbStr_general_error = QString("rgb(%1,%2,%3)").arg(color.red()).arg(color.green()).arg(color.blue());

    ui->pushButton_general_error->setStyleSheet(QString("QPushButton{background-color:rgb(%1,%2,%3);}").arg(color.red()).arg(color.green()).arg(color.blue()));
    QString s = ui->pushButton_general_error->styleSheet();
    qDebug() << s;
    getUiParam();
}

void SystemConfigurationDialog::on_pushButton_debug_information_clicked()
{
    QColor color = QColorDialog::getColor(Qt::red, nullptr, "选择颜色");
    if (!color.isValid())
    {
        return;
    }
    color_debug_information = color;
    str_debug_information = "调试信息";
    rgbStr_debug_information = QString("rgb(%1,%2,%3)").arg(color.red()).arg(color.green()).arg(color.blue());

    ui->pushButton_debug_information->setStyleSheet(QString("QPushButton{background-color:rgb(%1,%2,%3);}").arg(color.red()).arg(color.green()).arg(color.blue()));
    getUiParam();
}

void SystemConfigurationDialog::on_pushButton_warn_information_clicked()
{
    QColor color = QColorDialog::getColor(Qt::red, nullptr, "选择颜色");
    if (!color.isValid())
    {
        return;
    }
    color_warn_information = color;
    str_warn_information = "警告信息";
    rgbStr_warn_information_error = QString("rgb(%1,%2,%3)").arg(color.red()).arg(color.green()).arg(color.blue());

    ui->pushButton_warn_information->setStyleSheet(QString("QPushButton{background-color:rgb(%1,%2,%3);}").arg(color.red()).arg(color.green()).arg(color.blue()));
    getUiParam();
}

void SystemConfigurationDialog::on_pushButton_custom_color_clicked()
{
    QDialog dialog;
    dialog.setWindowTitle("自定义颜色");

    QVBoxLayout* mainLayout = new QVBoxLayout(&dialog); // 垂直布局
    // 添加控件
    QHBoxLayout* layout = new QHBoxLayout;  // 水平布局
    QHBoxLayout* layout1 = new QHBoxLayout; // 水平布局
    QHBoxLayout* layout2 = new QHBoxLayout; // 水平布局
    QHBoxLayout* layout3 = new QHBoxLayout; // 水平布局
    QHBoxLayout* layout4 = new QHBoxLayout; // 水平布局

    QLabel* linelable = new QLabel("背景", &dialog);
    QLabel* linelable1 = new QLabel("控件背景", &dialog);
    QLabel* linelable2 = new QLabel("文字", &dialog);
    QLabel* linelable3 = new QLabel("按钮背景", &dialog);
    QLabel* linelable4 = new QLabel("按钮边框", &dialog);
    QLineEdit* lineEdit_background = new QLineEdit(&dialog);
    QLineEdit* lineEdit_kongjian_background = new QLineEdit(&dialog);
    QLineEdit* lineEdit_wenzi_background = new QLineEdit(&dialog);
    QLineEdit* lineEdit_button_background = new QLineEdit(&dialog);
    QLineEdit* lineEdit_button_border = new QLineEdit(&dialog);

    layout->addWidget(linelable);
    layout->addWidget(lineEdit_background);

    layout1->addWidget(linelable1);
    layout1->addWidget(lineEdit_kongjian_background);

    layout2->addWidget(linelable2);
    layout2->addWidget(lineEdit_wenzi_background);

    layout3->addWidget(linelable3);
    layout3->addWidget(lineEdit_button_background);

    layout4->addWidget(linelable4);
    layout4->addWidget(lineEdit_button_border);

    mainLayout->addLayout(layout);
    mainLayout->addLayout(layout1);
    mainLayout->addLayout(layout2);
    mainLayout->addLayout(layout3);
    mainLayout->addLayout(layout4);

    // 操作按钮
    QHBoxLayout* buttonLayout = new QHBoxLayout;
    QPushButton* cancelBtn = new QPushButton("取消", &dialog);
    QPushButton* confirmBtn = new QPushButton("确定", &dialog);
    buttonLayout->addWidget(cancelBtn);
    buttonLayout->addWidget(confirmBtn);
    mainLayout->addLayout(buttonLayout);

    // 信号连接
    QObject::connect(cancelBtn, &QPushButton::clicked, &dialog, &QDialog::reject);

    QObject::connect(confirmBtn, &QPushButton::clicked, &dialog, [&]()
        {
            // 参数数据

            lineEdit_background->text();
            lineEdit_kongjian_background->text();
            lineEdit_wenzi_background->text();
            lineEdit_button_background->text();
            lineEdit_button_border->text();

            dialog.accept(); });

    dialog.exec();
}
void SystemConfigurationDialog::open_Dir_file_path(QLineEdit* lineEdit)
{
    QString dirpath = QFileDialog::getExistingDirectory(
        this,
        tr("选择目录"),
        QDir::homePath(),
        QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);
    if (!dirpath.isEmpty())
    {
        lineEdit->setText(dirpath);
    }
}
void SystemConfigurationDialog::open_file_path(QLineEdit* lineEdit)
{
    QString strFile = QFileDialog::getOpenFileName(
        this,
        tr("选择文件"),
        QDir::homePath(),
        tr("所有文件(*.*)"));
    if (!strFile.isEmpty())
    {
        lineEdit->setText(strFile);
    }
}

void SystemConfigurationDialog::on_pushButton_save_clicked()
{
    
    QDir currentDir = QDir::current();
    // 构建文件路径（确保目录存在后在操作）
    QFileInfo fileInfo(currentDir, "Dataconfig/CSystemConfigData.ini");
    QString filePath = fileInfo.absoluteFilePath();
    // 检查文件失败
    if (fileInfo.exists())
    {
        getUiParam();
        public_method.open_qmessage("提示", "界面数据保存成功", "确定");
        emit set_background_skin(systemconfigfdata.systemdata.set_skin);
        this->close();
    }
    else
    {
        public_method.open_qmessage("提示", "界面数据保存失败", "确定");
    }
    
    
    
}
void SystemConfigurationDialog::on_pushButton_cancel_clicked()
{
    this->close();
}
void SystemConfigurationDialog::closeEvent(QCloseEvent* event)
{
    this->close();
}
void SystemConfigurationDialog::on_pushButton_log_path_clicked()
{
    // open_Dir_file_path(ui->lineEdit_save_log_path);
    QString file_path = QFileDialog::getExistingDirectory(nullptr, "选择一个数据保存路径", "SqliteData/", QFileDialog::ShowDirsOnly | QFileDialog::DontUseNativeDialog); // 使用qt自带的文件对话框
    ui->lineEdit_save_log_path->setText(file_path);
}

void SystemConfigurationDialog::on_pushButton_dingbiao_path_clicked()
{
    // open_Dir_file_path(ui->lineEdit_dingbiao_path);
    QString file_path = QFileDialog::getExistingDirectory(nullptr, "选择一个数据保存路径", "SqliteData/", QFileDialog::ShowDirsOnly | QFileDialog::DontUseNativeDialog); // 使用qt自带的文件对话框
    ui->lineEdit_dingbiao_path->setText(file_path);
    calibrate_data_storage->set_file_path(file_path + "/定标数据/");
}

void SystemConfigurationDialog::on_pushButton_status_path_clicked()
{
    // open_Dir_file_path(ui->lineEdit_status_path);
    QString file_path = QFileDialog::getExistingDirectory(nullptr, "选择一个数据保存路径", "SqliteData/", QFileDialog::ShowDirsOnly | QFileDialog::DontUseNativeDialog); // 使用qt自带的文件对话框
    ui->lineEdit_status_path->setText(file_path);
    device_status_data_storage->set_file_path(file_path + "/状态数据/");
}

void SystemConfigurationDialog::on_pushButton_LV1_path_clicked()
{
    // open_Dir_file_path(ui->lineEdit_LV1_path);
    QString file_path = QFileDialog::getExistingDirectory(nullptr, "选择一个数据保存路径", "SqliteData/", QFileDialog::ShowDirsOnly | QFileDialog::DontUseNativeDialog); // 使用qt自带的文件对话框
    emit CHistory_file_Path_lv1(file_path);
    ui->lineEdit_LV1_path->setText(file_path);
    lv1_data_storage->set_file_path(file_path + "/数据Lv1/");
}

void SystemConfigurationDialog::on_pushButton_ceyun_path_clicked()
{
    // open_Dir_file_path(ui->lineEdit_ceyun_path);
    QString file_path = QFileDialog::getExistingDirectory(nullptr, "选择一个数据保存路径", "SqliteData/", QFileDialog::ShowDirsOnly | QFileDialog::DontUseNativeDialog); // 使用qt自带的文件对话框
    ui->lineEdit_ceyun_path->setText(file_path);
}

void SystemConfigurationDialog::on_pushButton_LV2_path_clicked()
{
    // open_Dir_file_path(ui->lineEdit_LV2_path);
    QString file_path = QFileDialog::getExistingDirectory(nullptr, "选择一个数据保存路径", "SqliteData/", QFileDialog::ShowDirsOnly | QFileDialog::DontUseNativeDialog); // 使用qt自带的文件对话框
    emit CHistory_file_Path(file_path);
    ui->lineEdit_LV2_path->setText(file_path);
    lv2_data_storage->set_file_path(file_path + "/数据Lv2/");
}


void SystemConfigurationDialog::on_pushButton_color_card_configuration_clicked()
{
    colorcard->show();
}

void SystemConfigurationDialog::on_comboBox_Device_status_save_mode_currentIndexChanged(int index)
{
    /*if (index == 0)
        device_status_data_storage->set_save_mode(false);
    else
        device_status_data_storage->set_save_mode(true);*/
}

void SystemConfigurationDialog::on_comboBox_Calibrate_save_mode_currentIndexChanged(int index)
{
    /*if (index == 0)
        calibrate_data_storage->set_save_mode(false);
    else
        calibrate_data_storage->set_save_mode(true);*/
}

void SystemConfigurationDialog::on_comboBox_Device_status_currentIndexChanged(int index)
{
    /*if (ui->comboBox_Device_status->currentText() == "日文件")
    {

        device_status_data_storage->set_Day_file_en(true);
        device_status_data_storage->set_Minutes_file_en(false);
    }
    else if (ui->comboBox_Device_status->currentText() == "分钟文件")
    {
        device_status_data_storage->set_Day_file_en(false);
        device_status_data_storage->set_Minutes_file_en(true);
    }*/
}

void SystemConfigurationDialog::on_comboBox_Calibrate_currentIndexChanged(int index)
{
    /*if (ui->comboBox_Calibrate->currentText() == "日文件")
    {

        calibrate_data_storage->set_Day_file_en(true);
        calibrate_data_storage->set_Minutes_file_en(false);
    }
    else if (ui->comboBox_Calibrate->currentText() == "分钟文件")
    {
        calibrate_data_storage->set_Day_file_en(false);
        calibrate_data_storage->set_Minutes_file_en(true);
    }*/
}

void SystemConfigurationDialog::on_pushButton_Calibrate_clicked() // 定标数据
{
    /*QVector<struct Calibrate_data> calibrate_data;

    struct Calibrate_data temp1, temp2, temp3, temp4;
    temp1.CALType = "NOISE";
    temp1.DataType = "Alpha";

    for (int i = 0; i < 14; i++)
    {
        temp1.ch_freq[i] = i * 1;
    }

    temp2.CALType = "NOISE";
    temp2.DataType = "Noise Tn";

    for (int i = 0; i < 14; i++)
    {
        temp2.ch_freq[i] = i * 2;
    }

    temp3.CALType = "NOISE";
    temp3.DataType = "Gain";

    for (int i = 0; i < 14; i++)
    {
        temp3.ch_freq[i] = i * 3;
    }

    temp4.CALType = "NOISE";
    temp4.DataType = "TSysN";

    for (int i = 0; i < 14; i++)
    {
        temp4.ch_freq[i] = i * 4;
    }

    calibrate_data.append(temp1);
    calibrate_data.append(temp2);
    calibrate_data.append(temp3);
    calibrate_data.append(temp4);


    if (ui->comboBox_Calibrate->currentText() == "日文件")
    {

        calibrate_data_storage->Generate_new_Calibrate_data(calibrate_data);
    }
    else if (ui->comboBox_Calibrate->currentText() == "分钟文件")
    {
        calibrate_data_storage->Generate_new_Calibrate_Minutes_file(calibrate_data);

    }*/
}

void SystemConfigurationDialog::on_pushButton_Device_status_clicked() // 状态数据
{
    /*struct Device_status_data basic_parameters = { 1,"time",1,0,0,0,0,273.15,273.14,0,0,0,0,273.15,273.16,273.17,273.18,20,80,1024,0,0,1,0,0,0 };
    if (ui->comboBox_Device_status->currentText() == "日文件")
    {

        device_status_data_storage->Generate_new_Device_status_data(basic_parameters);
    }
    else if (ui->comboBox_Device_status->currentText() == "分钟文件")
    {
        device_status_data_storage->Generate_new_Device_status_Minutes_file(basic_parameters);

    }*/
}

void SystemConfigurationDialog::SystemConfigurationDialog::slot_dis_rang(int index)
{
    /*if (index == 0)
    {
        d_plot_s->set_y_rang(0, 2, (2 / 10));
        d_plot2_s->set_y_rang(0, 2, (2 / 10));
        d_plot3_s->set_y_rang(0, 2, (2 / 10));
        d_plot4_s->set_y_rang(0, 2, (2 / 10));
    }
    else if (index == 1)
    {
        d_plot_s->set_y_rang(0, 4, (4 / 10));
        d_plot2_s->set_y_rang(0, 4, (4 / 10));
        d_plot3_s->set_y_rang(0, 4, (4 / 10));
        d_plot4_s->set_y_rang(0, 4, (4 / 10));
    }
    else if (index == 2)
    {
        d_plot_s->set_y_rang(0, 6, (6 / 10));
        d_plot2_s->set_y_rang(0, 6, (6 / 10));
        d_plot3_s->set_y_rang(0, 6, (6 / 10));
        d_plot4_s->set_y_rang(0, 6, (6 / 10));
    }
    else if (index == 3)
    {
        d_plot_s->set_y_rang(0, 8, (8 / 10));
        d_plot2_s->set_y_rang(0, 8, (8 / 10));
        d_plot3_s->set_y_rang(0, 8, (8 / 10));
        d_plot4_s->set_y_rang(0, 8, (8 / 10));
    }
    else if (index == 4)
    {
        d_plot_s->set_y_rang(0, 10, (10 / 10));
        d_plot2_s->set_y_rang(0, 10, (10 / 10));
        d_plot3_s->set_y_rang(0, 10, (10 / 10));
        d_plot4_s->set_y_rang(0, 10, (10 / 10));
    }*/
}

void SystemConfigurationDialog::slot_time_rang(int index)
{
    /*if (index == 0)
    {
        d_plot_s->set_x_rang(0, 3600, (3600 / 12));
        d_plot2_s->set_x_rang(0, 3600, (3600 / 12));
        d_plot3_s->set_x_rang(0, 3600, (3600 / 12));
        d_plot4_s->set_x_rang(0, 3600, (3600 / 12));
    }
    else if (index == 1)
    {
        d_plot_s->set_x_rang(0, 14400, (14400 / 12));
        d_plot2_s->set_x_rang(0, 14400, (14400 / 12));
        d_plot3_s->set_x_rang(0, 14400, (14400 / 12));
        d_plot4_s->set_x_rang(0, 14400, (14400 / 12));
    }
    else if (index == 2)
    {
        d_plot_s->set_x_rang(0, 28800, (28800 / 12));
        d_plot2_s->set_x_rang(0, 28800, (28800 / 12));
        d_plot3_s->set_x_rang(0, 28800, (28800 / 12));
        d_plot4_s->set_x_rang(0, 28800, (28800 / 12));
    }
    else if (index == 3)
    {
        d_plot_s->set_x_rang(0, 43200, (43200 / 12));
        d_plot2_s->set_x_rang(0, 43200, (43200 / 12));
        d_plot3_s->set_x_rang(0, 43200, (43200 / 12));
        d_plot4_s->set_x_rang(0, 43200, (43200 / 12));
    }
    else if (index == 4)
    {
        d_plot_s->set_x_rang(0, 57600, (57600 / 12));
        d_plot2_s->set_x_rang(0, 57600, (57600 / 12));
        d_plot3_s->set_x_rang(0, 57600, (57600 / 12));
        d_plot4_s->set_x_rang(0, 57600, (57600 / 12));
    }
    else if (index == 5)
    {
        d_plot_s->set_x_rang(0, 72000, (72000 / 12));
        d_plot2_s->set_x_rang(0, 72000, (72000 / 12));
        d_plot3_s->set_x_rang(0, 72000, (72000 / 12));
        d_plot4_s->set_x_rang(0, 72000, (72000 / 12));
    }
    else if (index == 6)
    {
        d_plot_s->set_x_rang(0, 86400, (86400 / 12));
        d_plot2_s->set_x_rang(0, 86400, (86400 / 12));
        d_plot3_s->set_x_rang(0, 86400, (86400 / 12));
        d_plot4_s->set_x_rang(0, 86400, (86400 / 12));
    }*/
}

void SystemConfigurationDialog::slot_hand_calibration()
{
    /*QDate dateStr1 = ui->dateEdit->date();
    QString timeStr1 = dateStr1.toString("yyyy-MM-dd");*/

    emit CSystemConfigurationDialogToHand();
    
}
