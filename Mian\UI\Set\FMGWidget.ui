<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FMGWidget</class>
 <widget class="QWidget" name="FMGWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1730</width>
    <height>1066</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">#line
{
background-color: rgb(170, 170, 255);
}
#line_2
{
background-color: rgb(170, 170, 255);
}
#line_3
{
background-color: rgb(170, 170, 255);
}
#line_4
{
background-color: rgb(170, 170, 255);
}
#line_5
{
background-color: rgb(170, 170, 255);
}
#line_6
{
background-color: rgb(170, 170, 255);
}
#FMGWidget
{
background-color: qlineargradient(spread:reflect, x1:0.0547264, y1:0.0568182, x2:1, y2:1, stop:0 rgba(36, 18, 88, 255), stop:1 rgba(255, 255, 255, 255));
}
#page_device_status
{
background-color: rgb(0, 40, 81);
}

#page_atmosphere_six_element
{
background-color: rgb(0, 40, 81);
}
#page_init_data_monitor
{
background-color: rgb(0, 40, 81);
}

#page_light_data_monitor
{
background-color: rgb(0, 40, 81);
}
QSplitter::handle
{
	background-color: rgb(160, 160, 160);
}
#widget
{
	background-color: rgb(0, 40, 81);
}
#widget_kv_data
{
	background-color: rgb(0, 40, 81);
}
#widget_k
{
	background-color: rgb(0, 40, 81);
}
#widget_v
{
	background-color: rgb(0, 40, 81);
}
#widget_3
{
	background-color: rgb(0, 40, 81);
}
#widget_4
{
	background-color: rgb(0, 40, 81);
}
#widget_plot
{
	background-color: rgb(0, 40, 81);
}
#widget_status
{
	background-color: rgb(0, 40, 81);
}
#widget_left
{
	background-color: rgb(0, 40, 81);
}

QLineEdit
{background-color: rgb(74, 92, 124);color:white;}




#pushButton_init_data_monitor
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(64, 166, 0, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}




#pushButton_light_data_monitor
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(64, 166, 0, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}



#pushButton_weather_six
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_weather_six::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_weather_six::hover
{border:1px solid white;}



#pushButton_k_voltage
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_k_voltage::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_k_voltage::hover
{border:1px solid white;}

#pushButton_v_voltage
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_v_voltage::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_v_voltage::hover
{border:1px solid white;}

#pushButton_k_temp
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_k_temp::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_k_temp::hover
{border:1px solid white;}

#pushButton_v_temp
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_v_temp::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_v_temp::hover
{border:1px solid white;}


#pushButton_k_temp_voltage
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_k_temp_voltage::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_k_temp_voltage::hover
{border:1px solid white;}

#pushButton_v_temp_voltage
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_v_temp_voltage::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_v_temp_voltage::hover
{border:1px solid white;}

#pushButton_all_temp_voltage
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_all_temp_voltage::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_all_temp_voltage::hover
{border:1px solid white;}




#pushButton_k_light
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_k_light::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_k_light::hover
{border:1px solid white;}

#pushButton_v_light
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_v_light::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_v_light::hover
{border:1px solid white;}

#pushButton_k_v_light
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_k_v_light::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_k_v_light::hover
{border:1px solid white;}



#pushButton_rain
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_rain::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_rain::hover
{border:1px solid white;}

#pushButton_normal_temp
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_normal_temp::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_normal_temp::hover
{border:1px solid white;}

#pushButton_all_light
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_all_light::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_all_light::hover
{border:1px solid white;}


#pushButton_k_show_data
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_k_show_data:pressed
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_k_show_data::hover
{border:1px solid white;}


#pushButton_v_show_data
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_v_show_data:pressed
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_v_show_data::hover
{border:1px solid white;}


#pushButton_sensor_status
{background-color: rgb(74, 92, 124);color:white;border: none;}
#pushButton_sensor_status:pressed
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
#pushButton_sensor_status::hover
{border:1px solid white;}


#widget
{
background-color: rgb(62, 62, 62);
}
QComboBox
{color:white;background-color:rgb(74, 92, 124);}
QComboBox::drop-down
{color:white;
image: url(:/VectorIcon/downarrows.png);
}
QComboBox QAbstractItemView
{color:white;background-color:rgb(74, 92, 124);selecttion-background-color:rgb(74, 92, 124);}

	
</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_49" stretch="1,10">
   <item>
    <widget class="QWidget" name="widget_left" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="Line" name="line_6">
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget_swtich_button" native="true">
        <property name="minimumSize">
         <size>
          <width>170</width>
          <height>45</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_48"/>
       </widget>
      </item>
      <item>
       <widget class="Line" name="line_5">
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_weather_six">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>35</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>气象六要素</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="Line" name="line">
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_k_voltage">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>35</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>K通道电压</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_v_voltage">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>35</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>V通道电压</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_k_temp">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>35</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>K通道温度</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_v_temp">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>35</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>V通道温度</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_all_temp_voltage">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>35</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>原始数据全部显示</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="Line" name="line_2">
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_k_light">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>35</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>K通道亮温</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_v_light">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>35</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>V通道亮温</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_rain">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>35</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>降雨量</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_normal_temp">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>35</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>常温源</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_all_light">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>35</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>亮温数据全部显示</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="Line" name="line_4">
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_k_show_data">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>35</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>K通道数据显示</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButton_v_show_data">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>35</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>V通道数据显示</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Expanding</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_12" stretch="0">
      <item>
       <widget class="QSplitter" name="splitter_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <widget class="QWidget" name="widget_plot" native="true">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>810</width>
           <height>1022</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item>
           <widget class="QStackedWidget" name="stackedWidget_FMG">
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="currentIndex">
             <number>1</number>
            </property>
            <widget class="QWidget" name="page_atmosphere_six_element">
             <layout class="QVBoxLayout" name="verticalLayout_14">
              <item>
               <widget class="QCustomPlot" name="widget_temp" native="true"/>
              </item>
              <item>
               <widget class="QCustomPlot" name="widget_humidity" native="true"/>
              </item>
              <item>
               <widget class="QCustomPlot" name="widget_pressure" native="true"/>
              </item>
              <item>
               <widget class="QCustomPlot" name="widget_wind_speed" native="true"/>
              </item>
              <item>
               <widget class="QCustomPlot" name="widget_wind_direction" native="true"/>
              </item>
              <item>
               <widget class="QCustomPlot" name="widget_rain" native="true"/>
              </item>
             </layout>
            </widget>
            <widget class="QWidget" name="page_init_data_monitor">
             <layout class="QVBoxLayout" name="verticalLayout_7">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="14,4,14,4">
                <item>
                 <widget class="QCustomPlot" name="widget_k_voltage" native="true"/>
                </item>
                <item>
                 <widget class="QWidget" name="widget_k_cebian_hz" native="true">
                  <property name="styleSheet">
                   <string notr="true">
#pushButton_k_2224
{
	border-image: url(:/VectorIcon/eyesclose.png);
}

#pushButton_k_2304
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_k_2384
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_k_2544
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_k_2624
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_k_2784
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_k_3000
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_k_3140
{
	border-image: url(:/VectorIcon/eyesclose.png);
}</string>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_8">
                   <item row="6" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_3000">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_3000">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>30.00GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="0" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_2224">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_2224">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>22.24GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="3" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_11" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_2544">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_2544">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>25.44GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="1" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_13" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_2304">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_2304">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>23.04GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="2" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_50" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_2384">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_2384">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>23.04GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="9" column="0">
                    <widget class="QPushButton" name="pushButton_k_all_show">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>满屏</string>
                     </property>
                    </widget>
                   </item>
                   <item row="5" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_2784">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_2784">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>27.84GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="7" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_3140">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_3140">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>31.40GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="10" column="0">
                    <widget class="QPushButton" name="pushButton_k_clear">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>清除</string>
                     </property>
                    </widget>
                   </item>
                   <item row="8" column="0">
                    <widget class="QPushButton" name="pushButton_k_stop">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>暂停</string>
                     </property>
                    </widget>
                   </item>
                   <item row="4" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_2624">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_2624">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>26.24GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QCustomPlot" name="widget_v_voltage" native="true"/>
                </item>
                <item>
                 <widget class="QWidget" name="widget_v_cebian_hz" native="true">
                  <property name="styleSheet">
                   <string notr="true">

#pushButton_v_5126
{
	border-image: url(:/VectorIcon/eyesclose.png);
}

#pushButton_v_5228
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_v_5386
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_v_5494
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_v_5550
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_v_5666
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_v_5730
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_v_5800
{
	border-image: url(:/VectorIcon/eyesclose.png);
}</string>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_7">
                   <item row="0" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_14" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_5126">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_5126">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>51.26GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="1" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_5228">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_5228">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>52.28GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="2" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_15" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_5386">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_5386">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>53.86GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="3" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_16" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_5494">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_5494">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>54.94GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="4" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_17" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_5550">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_5550">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>55.50GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="5" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_18" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_5666">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_5666">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>56.66GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="6" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_19" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_5730">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_5730">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>57.30GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="7" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_20" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_5800">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_5800">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>58.00GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="8" column="0">
                    <widget class="QPushButton" name="pushButton_v_stop">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>暂停</string>
                     </property>
                    </widget>
                   </item>
                   <item row="9" column="0">
                    <widget class="QPushButton" name="pushButton_v_all_show">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>满屏</string>
                     </property>
                    </widget>
                   </item>
                   <item row="10" column="0">
                    <widget class="QPushButton" name="pushButton_v_clear">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>清除</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="14,4,14,4">
                <item>
                 <widget class="QCustomPlot" name="widget_k_temp" native="true">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QWidget" name="widget_k_cebian_temp" native="true">
                  <property name="styleSheet">
                   <string notr="true">
#pushButton_k_temp1
{
	border-image: url(:/VectorIcon/eyesclose.png);
}

#pushButton_k_temp2
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_k_temp3
{
	border-image: url(:/VectorIcon/eyesclose.png);
}</string>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_6">
                   <item row="0" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_40" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_temp1">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true"/>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_temp1">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>温度1</string>
                       </property>
                       <property name="alignment">
                        <set>Qt::AlignCenter</set>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="1" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_39" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_temp2">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_temp2">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>温度2</string>
                       </property>
                       <property name="alignment">
                        <set>Qt::AlignCenter</set>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="2" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_38" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_temp3">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_temp3">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>温度3</string>
                       </property>
                       <property name="alignment">
                        <set>Qt::AlignCenter</set>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="3" column="0">
                    <widget class="QPushButton" name="pushButton_k_temp_stop">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>暂停</string>
                     </property>
                    </widget>
                   </item>
                   <item row="4" column="0">
                    <widget class="QPushButton" name="pushButton_k_temp_all_show">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>满屏</string>
                     </property>
                    </widget>
                   </item>
                   <item row="5" column="0">
                    <widget class="QPushButton" name="pushButton_k_temp_clear">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>清除</string>
                     </property>
                    </widget>
                   </item>
                   <item row="6" column="0">
                    <spacer name="verticalSpacer_4">
                     <property name="orientation">
                      <enum>Qt::Vertical</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Expanding</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>20</width>
                       <height>40</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QCustomPlot" name="widget_v_temp" native="true"/>
                </item>
                <item>
                 <widget class="QWidget" name="widget_v_cebian_temp" native="true">
                  <property name="styleSheet">
                   <string notr="true">

#pushButton_v_temp1
{
	border-image: url(:/VectorIcon/eyesclose.png);
}

#pushButton_v_temp2
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_v_temp3
{
	border-image: url(:/VectorIcon/eyesclose.png);
}</string>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_5">
                   <item row="0" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_41" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_temp1">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_temp1">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>温度1</string>
                       </property>
                       <property name="alignment">
                        <set>Qt::AlignCenter</set>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="1" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_42" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_temp2">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_temp2">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>温度1</string>
                       </property>
                       <property name="alignment">
                        <set>Qt::AlignCenter</set>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="2" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_43" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_temp3">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_temp3">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>温度1</string>
                       </property>
                       <property name="alignment">
                        <set>Qt::AlignCenter</set>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="3" column="0">
                    <widget class="QPushButton" name="pushButton_v_temp_stop">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>暂停</string>
                     </property>
                    </widget>
                   </item>
                   <item row="4" column="0">
                    <widget class="QPushButton" name="pushButton_v_temp_all_show">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>满屏</string>
                     </property>
                    </widget>
                   </item>
                   <item row="5" column="0">
                    <widget class="QPushButton" name="pushButton_v_temp_clear">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>清除</string>
                     </property>
                    </widget>
                   </item>
                   <item row="6" column="0">
                    <spacer name="verticalSpacer_5">
                     <property name="orientation">
                      <enum>Qt::Vertical</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>20</width>
                       <height>40</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
            <widget class="QWidget" name="page_light_data_monitor">
             <layout class="QVBoxLayout" name="verticalLayout_16">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="14,4,14,4">
                <item>
                 <widget class="QCustomPlot" name="widget_k_light_temp" native="true"/>
                </item>
                <item>
                 <widget class="QWidget" name="widget_light_k_cebian_hz" native="true">
                  <property name="styleSheet">
                   <string notr="true">
#pushButton_k_light_2224
{
	border-image: url(:/VectorIcon/eyesclose.png);
}

#pushButton_k_light_2304
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_k_light_2384
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_k_light_2544
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_k_light_2624
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_k_light_2784
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_k_light_3000
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_k_light_3140
{
	border-image: url(:/VectorIcon/eyesclose.png);
}</string>
                  </property>
                  <layout class="QGridLayout" name="gridLayout">
                   <item row="0" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_28" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_light_2224">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_light_2224">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>22.24GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="1" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_27" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_light_2304">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_light_2304">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>23.04GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="2" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_26" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_light_2384">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_light_2384">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>23.84GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="3" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_25" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_light_2544">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_light_2544">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>25.44GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="4" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_24" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_light_2624">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_light_2624">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>26.24GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="5" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_23" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_light_2784">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_light_2784">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>27.84GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="6" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_22" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_light_3000">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_light_3000">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>30.00GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="7" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_21" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_k_light_3140">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_k_light_3140">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>31.40GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="8" column="0">
                    <widget class="QPushButton" name="pushButton_k_light_stop">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>暂停</string>
                     </property>
                    </widget>
                   </item>
                   <item row="9" column="0">
                    <widget class="QPushButton" name="pushButton_k_light_all_show">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>满屏</string>
                     </property>
                    </widget>
                   </item>
                   <item row="10" column="0">
                    <widget class="QPushButton" name="pushButton_k_light_clear">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>清除</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QCustomPlot" name="widget_v_light_temp" native="true"/>
                </item>
                <item>
                 <widget class="QWidget" name="widget_light_v_cebian_hz" native="true">
                  <property name="styleSheet">
                   <string notr="true">
#pushButton_v_light_5126
{
	border-image: url(:/VectorIcon/eyesclose.png);
}

#pushButton_v_light_5228
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_v_light_5386
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_v_light_5494
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_v_light_5550
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_v_light_5666
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_v_light_5730
{
	border-image: url(:/VectorIcon/eyesclose.png);
}
#pushButton_v_light_5800
{
	border-image: url(:/VectorIcon/eyesclose.png);
}</string>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_2">
                   <item row="0" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_36" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_light_5126">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_light_5126">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>51.26GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="1" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_35" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_light_5228">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_light_5228">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>52.28GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="2" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_34" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_light_5386">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_light_5386">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>53.86GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="3" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_33" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_light_5494">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_light_5494">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>54.94GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="4" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_32" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_light_5550">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_light_5550">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>55.50GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="5" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_31" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_light_5666">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_light_5666">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>56.66GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="6" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_30" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_light_5730">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_light_5730">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>57.30GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="7" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_29" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_v_light_5800">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_v_light_5800">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>58.00GHZ</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="8" column="0">
                    <widget class="QPushButton" name="pushButton_v_light_stop">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>暂停</string>
                     </property>
                    </widget>
                   </item>
                   <item row="9" column="0">
                    <widget class="QPushButton" name="pushButton_v_light_all_show">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>满屏</string>
                     </property>
                    </widget>
                   </item>
                   <item row="10" column="0">
                    <widget class="QPushButton" name="pushButton_v_light_clear">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>清除</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="14,4,14,4">
                <item>
                 <widget class="QCustomPlot" name="widget_light_rain" native="true"/>
                </item>
                <item>
                 <widget class="QWidget" name="widget_light_cebian_rain" native="true">
                  <property name="styleSheet">
                   <string notr="true">

#pushButton_light_rain
{
	border-image: url(:/VectorIcon/eyesclose.png);
}

</string>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_3">
                   <item row="0" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_44" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_light_rain">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_light_rain">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>雨量</string>
                       </property>
                       <property name="alignment">
                        <set>Qt::AlignCenter</set>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="1" column="0">
                    <widget class="QPushButton" name="pushButton_light_rain_stop">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>暂停</string>
                     </property>
                    </widget>
                   </item>
                   <item row="2" column="0">
                    <widget class="QPushButton" name="pushButton_light_rain_all_show">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>满屏</string>
                     </property>
                    </widget>
                   </item>
                   <item row="3" column="0">
                    <widget class="QPushButton" name="pushButton_light_rain_clear">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>清除</string>
                     </property>
                    </widget>
                   </item>
                   <item row="4" column="0">
                    <spacer name="verticalSpacer_2">
                     <property name="orientation">
                      <enum>Qt::Vertical</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>20</width>
                       <height>40</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QCustomPlot" name="widget_light_temp" native="true"/>
                </item>
                <item>
                 <widget class="QWidget" name="widget_light_cebian_temp" native="true">
                  <property name="styleSheet">
                   <string notr="true">

#pushButton_light_temp
{
	border-image: url(:/VectorIcon/eyesclose.png);
}

</string>
                  </property>
                  <layout class="QGridLayout" name="gridLayout_4">
                   <item row="0" column="0">
                    <layout class="QHBoxLayout" name="horizontalLayout_45" stretch="0,1">
                     <item>
                      <widget class="QPushButton" name="pushButton_light_temp">
                       <property name="minimumSize">
                        <size>
                         <width>52</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>8</pointsize>
                        </font>
                       </property>
                       <property name="text">
                        <string/>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="label_light_temp">
                       <property name="minimumSize">
                        <size>
                         <width>64</width>
                         <height>28</height>
                        </size>
                       </property>
                       <property name="text">
                        <string>温度</string>
                       </property>
                       <property name="alignment">
                        <set>Qt::AlignCenter</set>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item row="1" column="0">
                    <widget class="QPushButton" name="pushButton_light_temp_stop">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>暂停</string>
                     </property>
                    </widget>
                   </item>
                   <item row="2" column="0">
                    <widget class="QPushButton" name="pushButton_light_temp_all_show">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>满屏</string>
                     </property>
                    </widget>
                   </item>
                   <item row="3" column="0">
                    <widget class="QPushButton" name="pushButton_light_temp_clear">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>28</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton
{background-color: rgb(74, 92, 124);color:white;border: none;}

QPushButton:focus
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}

QPushButton:hover
{border:1px solid white;}</string>
                     </property>
                     <property name="text">
                      <string>清除</string>
                     </property>
                    </widget>
                   </item>
                   <item row="4" column="0">
                    <spacer name="verticalSpacer_3">
                     <property name="orientation">
                      <enum>Qt::Vertical</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>20</width>
                       <height>40</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="widget_kv_data" native="true">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Minimum" vsizetype="Maximum">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="styleSheet">
          <string notr="true">QTableView
{background-color:rgb(74, 92, 124);color:black;}
QTableView:item
{background-:rgb(74, 92, 124);color:white;}
QTableView:item:selected
{background-color: rgb(0, 170, 255);color:white;}
QHeaderView::section
{background-color:rgb(74, 92, 124);color:rgb(255,255,255);}
QHeaderView::section:vertical
{background-color:rgb(74, 92, 124);color:rgb(255,255,255);}
QTableCornerButton::section
{background-color:rgb(74, 92, 124);border: 2px solid;}


#label_v
{
color:white;border:none;
}
#label_v_2
{
color:white;border:none;
}

#label_k
{
color:white;border:none;
}
#label_k_2
{
color:white;border:none;
}

</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_47">
          <item>
           <widget class="QWidget" name="widget_k" native="true">
            <layout class="QVBoxLayout" name="verticalLayout_11">
             <item>
              <widget class="QSplitter" name="splitter">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <widget class="QWidget" name="widget_6" native="true">
                <layout class="QVBoxLayout" name="verticalLayout_4">
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_37" stretch="1,2,0">
                   <item>
                    <widget class="QLabel" name="label_k">
                     <property name="text">
                      <string>k通道电压数据</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QComboBox" name="comboBox_k_data">
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <item>
                      <property name="text">
                       <string>5分钟</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>10分钟</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>30分钟</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>1小时</string>
                      </property>
                     </item>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>100</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <widget class="QTableView" name="tableView_k"/>
                 </item>
                </layout>
               </widget>
               <widget class="QWidget" name="widget_5" native="true">
                <layout class="QVBoxLayout" name="verticalLayout_8">
                 <item>
                  <widget class="QLabel" name="label_k_2">
                   <property name="text">
                    <string>定标数据</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QTableView" name="tableView_k_calibration"/>
                 </item>
                </layout>
               </widget>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widget_v" native="true">
            <layout class="QVBoxLayout" name="verticalLayout_12">
             <item>
              <widget class="QSplitter" name="splitter_2">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <widget class="QWidget" name="widget_4" native="true">
                <layout class="QVBoxLayout" name="verticalLayout_10">
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_46" stretch="1,2,0">
                   <item>
                    <widget class="QLabel" name="label_v">
                     <property name="text">
                      <string>v通道电压数据</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QComboBox" name="comboBox_v_data">
                     <item>
                      <property name="text">
                       <string>5分钟</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>10分钟</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>30分钟</string>
                      </property>
                     </item>
                     <item>
                      <property name="text">
                       <string>1小时</string>
                      </property>
                     </item>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_2">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>100</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <widget class="QTableView" name="tableView_v"/>
                 </item>
                </layout>
               </widget>
               <widget class="QWidget" name="widget_3" native="true">
                <layout class="QVBoxLayout" name="verticalLayout_9">
                 <item>
                  <widget class="QLabel" name="label_v_2">
                   <property name="text">
                    <string>定标数据</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QTableView" name="tableView_v_calibration"/>
                 </item>
                </layout>
               </widget>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCustomPlot</class>
   <extends>QWidget</extends>
   <header>qcustomplot.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
