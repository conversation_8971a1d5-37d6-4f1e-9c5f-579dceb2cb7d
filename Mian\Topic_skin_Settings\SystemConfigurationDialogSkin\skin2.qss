﻿/*系统参数配置界面样式表*/
SystemConfigurationDialog
{background-color: rgb(74, 92, 124);}
SystemConfigurationDialog QLabel
{color: rgb(0, 0, 0);}
SystemConfigurationDialog QGroupBox
{color: rgb(0, 0, 0);}
SystemConfigurationDialog QPushButton
{background-color: rgb(49, 68, 102);color:white;border: 2px solid #ffffff;}
SystemConfigurationDialog QStackedWidget
{background-color: rgb(87, 106, 140);}
SystemConfigurationDialog QCheckBox
{color: rgb(0, 0, 0);}

/*数据配置路径按钮*/
SystemConfigurationDialog QPushButton#pushButton_bright_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_bright_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_bright_set::hover
{border:1px solid white;}

SystemConfigurationDialog QPushButton#pushButton_data
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_data::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_data::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_ftp_file_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_ftp_file_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_ftp_file_set::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_network_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_network_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_network_set::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_save_data_path
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_save_data_path::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_save_data_path::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_skin_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_skin_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_skin_set::hover
{border:1px solid white;}



SystemConfigurationDialog QPushButton#pushButton_bright_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_bright_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_bright_set::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_bright_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_bright_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_bright_set::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_view_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_view_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_view_set::hover
{border:1px solid white;}


SystemConfigurationDialog QPushButton#pushButton_station_set
{background-color: rgb(74, 92, 124);color:white;border: none;}
SystemConfigurationDialog QPushButton#pushButton_station_set::checked
{background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(127, 94, 255, 255), stop:1 rgba(255, 255, 255, 255));color:white;border:none;}
SystemConfigurationDialog#pushButton_station_set::hover
{border:1px solid white;}
