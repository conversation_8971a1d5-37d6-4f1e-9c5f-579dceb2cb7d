﻿#ifndef SQLITE_FTPUPLOADRECORD_DATA_H
#define SQLITE_FTPUPLOADRECORD_DATA_H
#include "tools/Data_storage/CustomSqlTableModel.h"
#include "tools/Data_storage/qtsqlitedb.h"
#include <QDateTime>
#include <QHeaderView>
#include <QMap>
struct FtpUploadRecord_SQ_GROUP
{
    QString filename; // 文件名
    QString u_time;   // 时间
    QString status;
    QString time;
};
// FTP上传记录表
class sqlite_FtpUploadRecord_data : public QtSqliteDB
{
    Q_OBJECT
public:
    explicit sqlite_FtpUploadRecord_data(QString path);
    ~sqlite_FtpUploadRecord_data();

    bool select_sqlitedata(QString insertSQL) override; // 查询用户数据

    QVector<struct FtpUploadRecord_SQ_GROUP> data;         // 存储查询数据库的结果
    QVector<struct FtpUploadRecord_SQ_GROUP> current_data; // 存储查询数据库的结果

    QVector<int> ratios = {1, 1, 1, 1}; // 设置模型各列显示比例
    int cur_row;                        // 记录最后一行
    QTableView *tableview;              // 存储使用模型的view
    int CURRENT_DATA_COUNT = 1;         // 数据缓冲CURRENT_DATA_COUNT再加入数据库
    int DATEBASE_DATA_COUNT = 50;       // 数据库的最大数据量
    // 使用CustomSqlTableModel模型
    CustomSqlTableModel *myCustomSqlTableModel;
    void use_CustomSqlTableModel(QTableView *view); // 数据库模型初始化
    bool myCustomSqlTableModel_status = false;
    bool auto_delect_en = false;
    int insertCountLimit(struct FtpUploadRecord_SQ_GROUP data);                                         // 插入数据
    int select_usetime_SqlTableModel(QString start_time, QString stop_time, QString type = "所有状态"); // 根据时间查询数据库数据并显示到view中  时间格式yyyy-MM-dd HH:mm:ss
    QMap<QString, int> get_startTostop_type(QString start_time, QString stop_time);                     // 获取指定时间段的上传状态及个数
    int deleteOldsql(QString time);                                                                     // 删除早于指定时间的文件 时间格式yyyy-MM-dd HH:mm:ss
    bool select_usestatus_SqlTableModel(QString status);                                                // 查询指定状态的所有上传记录
    void set_auto_delect_en(bool en);
    bool set_column_color(QVector<QColor> c);                                            // 设置最后一行的颜色
    bool set_column_color(int row, int column, const QColor &c = QColor(255, 255, 255)); // 设置指定项的颜色
    void set_column_edit(int column, bool en);

private:
    int syncToDatabase(); // 同步本地数据到数据库
    int select_type_SqlTableModel(QString key, QString type);
    QMetaObject::Connection coon;
};

#endif // SQLITE_FTPUPLOADRECORD_DATA_H
