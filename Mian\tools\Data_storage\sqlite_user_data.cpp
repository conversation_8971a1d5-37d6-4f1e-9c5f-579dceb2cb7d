﻿#include "tools/Data_storage/sqlite_user_data.h"
#include <QRegularExpression>
#include <QChar>
#include <QHeaderView>
#include <QDebug>
sqlite_USER_data::sqlite_USER_data(QString path)
{
    createDb(path);
    if (!tableExists("USER_data"))
    {
        execute(
            "CREATE TABLE USER_data ("
            "name TEXT PRIMARY KEY NOT NULL,"
            "password TEXT NOT NULL,"
            "permissions INT NOT NULL,"
            "describe TEXT,"
            "time TEXT NOT NULL);");
    }

    myCustomSqlTableModel = new CustomSqlTableModel("USER_data");
    myCustomSqlTableModel->setTable("USER_data");
    myCustomSqlTableModel->setEditStrategy(QSqlTableModel::OnFieldChange);
    myCustomSqlTableModel->select();
    myCustomSqlTableModel->setHeaderData(0, Qt::Horizontal, tr("用户名"));
    myCustomSqlTableModel->setHeaderData(3, Qt::Horizontal, tr("描述"));
    myCustomSqlTableModel->setHeaderData(4, Qt::Horizontal, tr("创建时间"));
}

sqlite_USER_data::~sqlite_USER_data()
{
    QObject::disconnect(coon);
    m_dbCount--;
}

void sqlite_USER_data::use_CustomSqlTableModel(QTableView *view)
{

    // 设置模型
    view->setModel(myCustomSqlTableModel);
    tableview = view;
    // 隐藏密码及权限
    view->setColumnHidden(1, true);
    view->setColumnHidden(2, true);
    // 设置不可编辑
    set_column_edit(4, false);
    set_column_edit(3, false);
    set_column_edit(0, false);
    // view->setEditTriggers(QAbstractItemView::NoEditTriggers);
    // 设置表格列按比例充满view
    view->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
    coon = connect(tableview->horizontalHeader(), &QHeaderView::geometriesChanged, [&]()
                   {
                       if (tableview == nullptr)
                           return;
                       int totalwidth = tableview->viewport()->width();
                       int ratiosum = std::accumulate(ratios.begin(), ratios.end(), 0);

                       tableview->setColumnWidth(0, (totalwidth * ratios[0]) / ratiosum);
                       tableview->setColumnWidth(3, (totalwidth * ratios[1]) / ratiosum);
                       tableview->setColumnWidth(4, (totalwidth * ratios[2]) / ratiosum);
                   });
    // 模型使用使能
    myCustomSqlTableModel_status = true;
    // tableview->reset();
}

bool sqlite_USER_data::select_sqlitedata(QString insertSQL)
{
    QSqlQuery sql_query(m_db);
    sql_query.prepare(insertSQL);
    if (!sql_query.exec())
    {
        QSqlError error = sql_query.lastError();
        qDebug() << error.text();
        return false;
    }
    else
    {
        data.clear();
        while (sql_query.next())
        {
            struct USER_SQ_GROUP temp;
            temp.name = (sql_query.value("name").toString());
            /*temp.password = "无法查看";
            temp.permissions = -1;*/
             temp.password = (sql_query.value("password").toString());
             temp.permissions = (sql_query.value("permissions").toInt());
            temp.describe = (sql_query.value("describe").toString());
            temp.time = (sql_query.value("time").toString());
            data.append(temp);
        }
        return true;
    }
}

bool sqlite_USER_data::select_sqlitedata_user(QString username)
{
    QSqlQuery sql_query(m_db);
    sql_query.prepare("SELECT * FROM USER_data WHERE name = ?");
    sql_query.addBindValue(username);
    if (!sql_query.exec())
    {
        QSqlError error = sql_query.lastError();
        qDebug() << error.text();
        return false;
    }
    else
    {
        data.clear();
        while (sql_query.next())
        {
            struct USER_SQ_GROUP temp;
            temp.name = (sql_query.value("name").toString());
            temp.password = xorEncrypt((sql_query.value("password").toString()));
            temp.permissions = (sql_query.value("permissions").toInt());
            temp.describe = (sql_query.value("describe").toString());
            temp.time = (sql_query.value("time").toString());
            data.append(temp);
        }
        return true;
    }
}



/*
 *新建用户账号
 * 参数:
 *    data 存储用户信息（时间不用）
 * 返回值: 0 新建成功
 *        1 用户已存在
 *        2 秘密不符合要求
 *        3 用户名不符合要求
 *        4 权限不符合要求
 *        5 数据库异常
 */
int sqlite_USER_data::new_user(struct USER_SQ_GROUP data)
{

    // 检查用户名唯一性
    QRegularExpression regex1("^(?=.*[A-Za-z])(?=.*\\d).{6,20}$");
    if (!regex1.match(data.name).hasMatch())
    {
        return 3;
    }
    QSqlQuery sql_query(m_db);
    sql_query.prepare("SELECT 1 FROM USER_data WHERE name = ?");
    sql_query.addBindValue(data.name);
    sql_query.exec();
    if (sql_query.next())
    {
        return 1;
    }
    // 验证密码
    QRegularExpression regex("^(?=.*[A-Za-z])(?=.*\\d).{6,20}$");
    if (!regex.match(data.password).hasMatch())
    {
        return 2;
    }
    // 验证权限是否合规
    if (data.permissions < 1 || data.permissions > 3)
    {
        return 4;
    }
    // 插入数据
    QSqlQuery query(m_db);
    query.prepare("INSERT INTO USER_data (name,password,permissions,describe,time) VALUES (?,?,?,?,?)");
    query.addBindValue(data.name);
    query.addBindValue(xorEncrypt(data.password));
    query.addBindValue(data.permissions);
    query.addBindValue(data.describe);
    if (data.time == "beijing")
    {
        query.addBindValue(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss"));
    }
    else if (data.time == "UTC")
    {
        query.addBindValue(QDateTime::currentDateTimeUtc().toString("yyyy-MM-dd HH:mm:ss"));
    }
    else
    {
        if (!QDateTime::fromString(data.time, "yyyy-MM-dd HH:mm:ss").isValid())
        {
            return 5;
        }
        query.addBindValue(data.time);
    }

    if (!query.exec())
    {
        return 5;
    }

    myCustomSqlTableModel->select();

    return 0;
}
/*
 *查询所有用户信息 不显示权限及密码
 * 返回值: 用户数据组
 */
QVector<USER_SQ_GROUP> sqlite_USER_data::select_user_group()
{
    select_sqlitedata("SELECT * FROM USER_data");
    return data;
}
/*
 *超级用户删除高级用户和普通用户
 * 参数:
 *    data 超级用户(即当前登录的超级用户)
 *    name 被删除的用户名
 * 返回值: 0 删除成功
 *        1 权限不足
 *        2 用户不存在
 *        3 该超级用户不存在
 *        4 数据库异常
 */
int sqlite_USER_data::delete_user(USER_SQ_GROUP data, QString name)
{
    // 判断超级用户是否存在
    //    //检查用户是否存在
    //    QSqlQuery sql_querys(m_db);
    //    sql_querys.prepare("SELECT * FROM USER_data WHERE name = ?");
    //    sql_querys.addBindValue(data.name);
    //    sql_querys.exec();
    //    if(!sql_querys.next())
    //    {
    //        return 3;
    //    }
    //    else
    //    {

    //        struct USER_SQ_GROUP temp;
    //        temp.name = (sql_querys.value("name").toString());
    //        temp.password = xorEncrypt(sql_querys.value("password").toString());
    //        temp.permissions = (sql_querys.value("permissions").toInt());
    //        temp.describe = (sql_querys.value("describe").toString());
    //        temp.time = (sql_querys.value("time").toString());

    //        //判断当前用户权限
    //        if(temp.permissions!=3)
    //        {
    //            return 1;
    //        }

    //    }

    // 判断待删除用户是否存在
    QSqlQuery sql_query(m_db);
    sql_query.prepare("SELECT 1 FROM USER_data WHERE name = ?");
    sql_query.addBindValue(name);
    sql_query.exec();
    if (!sql_query.next())
    {
        return 2;
    }

    QSqlQuery sql_query1(m_db);
    sql_query1.prepare("DELETE FROM USER_data WHERE name = ?");
    sql_query1.addBindValue(name);
    if (!sql_query1.exec())
    {
        return 4;
    }
    myCustomSqlTableModel->select();

    return 0;
}
/*
 *更新用户数据  注：用户名不可修改
 * 参数:
 *    data 用户新的信息
 * 返回值: 0 更新成功
 *        1 用户不存在
 *        2 秘密不符合要求
 *        3 权限不符合要求
 *        4 数据库异常
 */
int sqlite_USER_data::update_user(USER_SQ_GROUP data)
{

    // 验证密码是否合规
    QRegularExpression regex("^(?=.*[A-Za-z])(?=.*\\d).{6,20}$");
    if (!regex.match(data.password).hasMatch())
    {
        return 2;
    }
    // 验证权限是否合规
    if (data.permissions < 1 || data.permissions > 3)
    {
        return 3;
    }
    // 判断待更新用户是否存在
    QSqlQuery sql_query(m_db);
    sql_query.prepare("SELECT 1 FROM USER_data WHERE name = ?");
    sql_query.addBindValue(data.name);
    sql_query.exec();
    if (!sql_query.next())
    {
        return 1;
    }

    QSqlQuery sql_query1(m_db);
    sql_query1.prepare("UPDATE USER_data SET password = ?,permissions = ?,describe = ? WHERE name = ?");
    sql_query1.addBindValue(xorEncrypt(data.password));
    sql_query1.addBindValue(data.permissions);
    sql_query1.addBindValue(data.describe);
    sql_query1.addBindValue(data.name);

    if (!sql_query1.exec())
    {
        return 4;
    }
    myCustomSqlTableModel->select();
    return 0;
}
/*
 *登录用户账号
 * 参数:
 *    data 存储用户信息 只需用户名及密码
 * 返回值: 0 登录成功
 *        1 用户不存在
 *        2 密码不符合要求
 *        3 用户名不符合要求
 *        4 密码错误
 *
 */
int sqlite_USER_data::log_in(USER_SQ_GROUP data)
{
    // 验证用户名是否合规
    QRegularExpression regex1("^(?=.*[A-Za-z])(?=.*\\d).{6,20}$");
    if (!regex1.match(data.name).hasMatch())
    {
        return 3;
    }

    // 验证密码是否合规
    QRegularExpression regex("^(?=.*[A-Za-z])(?=.*\\d).{6,20}$");
    if (!regex.match(data.password).hasMatch())
    {
        return 2;
    }
    // 检查用户是否存在
    QSqlQuery sql_query(m_db);
    sql_query.prepare("SELECT * FROM USER_data WHERE name = ?");
    sql_query.addBindValue(data.name);
    sql_query.exec();
    if (!sql_query.next())
    {
        return 1;
    }
    else
    {
        this->data.clear();
        struct USER_SQ_GROUP temp;
        temp.name = (sql_query.value("name").toString());
        temp.password = xorEncrypt(sql_query.value("password").toString());
        temp.permissions = (sql_query.value("permissions").toInt());
        temp.describe = (sql_query.value("describe").toString());
        temp.time = (sql_query.value("time").toString());
        this->data.append(temp);
    }
    if (this->data[0].password == data.password)
    {
        return 0;
    }
    else
    {
        return 4;
    }
}

void sqlite_USER_data::set_column_edit(int column, bool en)
{
    myCustomSqlTableModel->set_edit(column, en);
}

bool sqlite_USER_data::set_column_color(QVector<QColor> c)
{
    int count = 0;
    QSqlQuery sql_query(m_db);
    sql_query.prepare("SELECT COUNT(*) FROM FaultRecord_data");
    if (!sql_query.exec() || !sql_query.next())
    {
        QSqlError error = sql_query.lastError();
        qDebug() << error.text();
        return false;
    }
    else
    {
        count = sql_query.value(0).toInt();
    }
    qDebug() << count;
    if (myCustomSqlTableModel_status)
    {
        if (c.count() != myCustomSqlTableModel->columnCount())
            return false;
        for (int i = 0; i < myCustomSqlTableModel->columnCount(); i++)
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(count - 1, i), c[i], Qt::ForegroundRole);
        }
        return true;
    }
    return false;
}

bool sqlite_USER_data::set_column_color(int row, int column, const QColor &c)
{
    int count = 0;
    QSqlQuery sql_query(m_db);
    sql_query.prepare("SELECT COUNT(*) FROM FaultRecord_data");
    if (!sql_query.exec() || !sql_query.next())
    {
        QSqlError error = sql_query.lastError();
        qDebug() << error.text();
        return false;
    }
    else
    {
        count = sql_query.value(0).toInt();
    }
    qDebug() << count;
    if (myCustomSqlTableModel_status)
    {
        if (count < row && myCustomSqlTableModel->columnCount() < column)
        {
            myCustomSqlTableModel->setData(myCustomSqlTableModel->index(row, column), c, Qt::ForegroundRole);
            return true;
        }
    }
    return false;
}
/*
 *字符串加解密
 * 参数:
 *    data 待加密字符串
 * 返回值: 加密字符串
 *
 */
QString sqlite_USER_data::xorEncrypt(QString data)
{
    QString result;

    QString key = "!@#strongkey$%^";
    int key_len = key.length();

    for (int i = 0; i < data.length(); i++)
    {
        QChar ch = QChar::fromLatin1(data.at(i).toLatin1() ^ key.at(i % key_len).toLatin1());
        result.append(ch);
    }

    return result;
}
